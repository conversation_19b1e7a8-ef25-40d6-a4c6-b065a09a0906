using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Services;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة عرض تفاصيل الوثيقة
    /// </summary>
    public partial class DocumentDetailsForm : Form
    {
        private readonly Document _document;
        private readonly AttachmentRepository _attachmentRepository;
        private readonly FileService _fileService;

        public DocumentDetailsForm(Document document)
        {
            InitializeComponent();
            _document = document;
            _attachmentRepository = new AttachmentRepository();
            _fileService = new FileService();
            InitializeControls();
            LoadDocumentDetails();
        }

        private void InitializeControls()
        {
            this.Text = "تفاصيل الوثيقة - " + _document.DocumentNumber;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 242, 247);

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "تفاصيل الوثيقة",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // لوحة التفاصيل
            var detailsPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(740, 400),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            CreateDetailsContent(detailsPanel);
            this.Controls.Add(detailsPanel);

            // أزرار الإجراءات
            CreateActionButtons();
        }

        private void CreateDetailsContent(Panel panel)
        {
            int yPos = 20;
            int labelWidth = 120;
            int valueWidth = 500;
            int spacing = 35;

            // تسلسل الكتاب
            panel.Controls.Add(CreateDetailLabel("تسلسل الكتاب:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.DocumentSequence.ToString(), new Point(50, yPos), valueWidth));
            yPos += spacing;

            // تاريخ الإدخال
            panel.Controls.Add(CreateDetailLabel("تاريخ الإدخال:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.EntryDate.ToString("yyyy/MM/dd HH:mm:ss"), new Point(50, yPos), valueWidth));
            yPos += spacing;

            // نوع الكتاب
            panel.Controls.Add(CreateDetailLabel("نوع الكتاب:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.DocumentType, new Point(50, yPos), valueWidth));
            yPos += spacing;

            // رقم الكتاب
            panel.Controls.Add(CreateDetailLabel("رقم الكتاب:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.DocumentNumber, new Point(50, yPos), valueWidth));
            yPos += spacing;

            // تاريخ الكتاب
            panel.Controls.Add(CreateDetailLabel("تاريخ الكتاب:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.DocumentDate.ToString("yyyy/MM/dd"), new Point(50, yPos), valueWidth));
            yPos += spacing;

            // موضوع الكتاب
            panel.Controls.Add(CreateDetailLabel("موضوع الكتاب:", new Point(600, yPos)));
            var subjectTextBox = new TextBox
            {
                Location = new Point(50, yPos),
                Size = new Size(valueWidth, 60),
                Text = _document.Subject,
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                BackColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };
            panel.Controls.Add(subjectTextBox);
            yPos += 80;

            // صادر من / وارد إلى
            if (!string.IsNullOrEmpty(_document.IssuedFrom))
            {
                panel.Controls.Add(CreateDetailLabel("صادر من:", new Point(600, yPos)));
                panel.Controls.Add(CreateDetailValue(_document.IssuedFrom, new Point(50, yPos), valueWidth));
                yPos += spacing;
            }

            if (!string.IsNullOrEmpty(_document.ReceivedTo))
            {
                panel.Controls.Add(CreateDetailLabel("وارد إلى:", new Point(600, yPos)));
                panel.Controls.Add(CreateDetailValue(_document.ReceivedTo, new Point(50, yPos), valueWidth));
                yPos += spacing;
            }

            // القسم
            panel.Controls.Add(CreateDetailLabel("القسم:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.Department?.DepartmentName ?? "غير محدد", new Point(50, yPos), valueWidth));
            yPos += spacing;

            // الأضبارة
            panel.Controls.Add(CreateDetailLabel("الأضبارة:", new Point(600, yPos)));
            panel.Controls.Add(CreateDetailValue(_document.Folder?.FolderName ?? "غير محدد", new Point(50, yPos), valueWidth));
            yPos += spacing;

            // تسلسل الحفظ
            panel.Controls.Add(CreateDetailLabel("تسلسل الحفظ:", new Point(600, yPos)));
            var saveSeqLabel = CreateDetailValue(_document.SaveSequence.ToString(), new Point(50, yPos), valueWidth);
            saveSeqLabel.ForeColor = Color.Red;
            saveSeqLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            panel.Controls.Add(saveSeqLabel);
        }

        private Label CreateDetailLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
        }

        private Label CreateDetailValue(string text, Point location, int width)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(width, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F),
                BorderStyle = BorderStyle.Fixed3D,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(5)
            };
        }

        private void CreateActionButtons()
        {
            // زر عرض المرفقات
            var viewAttachmentsButton = new Button
            {
                Text = "📎 عرض المرفقات",
                Location = new Point(20, 480),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            viewAttachmentsButton.Click += ViewAttachmentsButton_Click;
            this.Controls.Add(viewAttachmentsButton);

            // زر تعديل الوثيقة
            var editButton = new Button
            {
                Text = "✏️ تعديل الوثيقة",
                Location = new Point(180, 480),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // زر طباعة
            var printButton = new Button
            {
                Text = "🖨️ طباعة",
                Location = new Point(340, 480),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += PrintButton_Click;
            this.Controls.Add(printButton);

            // زر إغلاق
            var closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(610, 480),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);
        }

        private void LoadDocumentDetails()
        {
            // تحديث عنوان النافذة مع عدد المرفقات
            var attachments = _attachmentRepository.GetByDocumentId(_document.DocumentId);
            this.Text = "تفاصيل الوثيقة - " + _document.DocumentNumber + " (" + attachments.Count + " مرفق)";
        }

        private void ViewAttachmentsButton_Click(object sender, EventArgs e)
        {
            var attachmentsForm = new DocumentAttachmentsForm(_document);
            attachmentsForm.ShowDialog();
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            var editForm = new EditDocumentForm(_document);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                // إعادة تحميل التفاصيل بعد التعديل
                LoadDocumentDetails();
            }
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("وظيفة الطباعة قيد التطوير", "الطباعة", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
