using System;
using System.Collections.Generic;

namespace ArchiveSystem.Models
{
    public class DocumentSimple
    {
        public int DocumentId { get; set; }
        public string DocumentNumber { get; set; }
        public string Subject { get; set; }
        public string DocumentType { get; set; }
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public string FolderNumber { get; set; }
        public DateTime DocumentDate { get; set; }
        public DateTime? ReceivedDate { get; set; }
        public string SenderReceiver { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public bool IsActive { get; set; }
        public List<DocumentAttachment> Attachments { get; set; }

        public int AttachmentsCount
        {
            get { return Attachments != null ? Attachments.Count : 0; }
        }

        public string DynamicSenderReceiver
        {
            get { return DocumentType == "صادر" ? "الجهة المستقبلة" : "الجهة المرسلة"; }
        }

        public string DocumentDateFormatted
        {
            get { return DocumentDate.ToString("yyyy/MM/dd"); }
        }

        public string ReceivedDateFormatted
        {
            get { return ReceivedDate.HasValue ? ReceivedDate.Value.ToString("yyyy/MM/dd") : "-"; }
        }

        public string TypeIcon
        {
            get { return DocumentType == "صادر" ? "📤" : "📥"; }
        }

        public DocumentSimple()
        {
            DocumentDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            IsActive = true;
            CreatedBy = "النظام";
            Attachments = new List<DocumentAttachment>();
        }

        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrEmpty(DocumentNumber) || DocumentNumber.Trim().Length == 0)
                errors.Add("رقم الوثيقة مطلوب");
            else if (DocumentNumber.Length > 50)
                errors.Add("رقم الوثيقة يجب أن يكون أقل من 50 حرف");

            if (string.IsNullOrEmpty(Subject) || Subject.Trim().Length == 0)
                errors.Add("موضوع الوثيقة مطلوب");
            else if (Subject.Length < 5)
                errors.Add("موضوع الوثيقة يجب أن يكون 5 أحرف على الأقل");
            else if (Subject.Length > 200)
                errors.Add("موضوع الوثيقة يجب أن يكون أقل من 200 حرف");

            if (string.IsNullOrEmpty(DocumentType))
                errors.Add("نوع الوثيقة مطلوب");
            else if (DocumentType != "صادر" && DocumentType != "وارد")
                errors.Add("نوع الوثيقة يجب أن يكون صادر أو وارد");

            if (DepartmentId <= 0)
                errors.Add("يجب اختيار القسم");

            if (string.IsNullOrEmpty(FolderNumber) || FolderNumber.Trim().Length == 0)
                errors.Add("رقم الأضبارة مطلوب");
            else if (FolderNumber.Length > 50)
                errors.Add("رقم الأضبارة يجب أن يكون أقل من 50 حرف");

            if (DocumentDate > DateTime.Now.Date)
                errors.Add("تاريخ الوثيقة لا يمكن أن يكون في المستقبل");

            if (ReceivedDate.HasValue && ReceivedDate.Value > DateTime.Now.Date)
                errors.Add("تاريخ الاستلام لا يمكن أن يكون في المستقبل");

            if (!string.IsNullOrEmpty(SenderReceiver) && SenderReceiver.Length > 200)
                errors.Add("المرسل/المستقبل يجب أن يكون أقل من 200 حرف");

            if (!string.IsNullOrEmpty(Notes) && Notes.Length > 1000)
                errors.Add("الملاحظات يجب أن تكون أقل من 1000 حرف");

            return errors.Count == 0;
        }

        public override string ToString()
        {
            return DocumentNumber + " - " + Subject;
        }
    }
}
