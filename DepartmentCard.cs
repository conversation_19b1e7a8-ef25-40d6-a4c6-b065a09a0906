using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Helpers;
using ArchiveSystem.Models;

namespace ArchiveSystem
{
    /// <summary>
    /// بطاقة عرض القسم - UserControl
    /// </summary>
    public partial class DepartmentCard : UserControl
    {
        #region Fields
        private DepartmentSimple _department;
        private Label _nameLabel;
        private Label _statisticsLabel;
        private Button _editButton;
        private Button _deleteButton;
        private Button _openButton;
        private Panel _headerPanel;
        private Panel _contentPanel;
        private Panel _buttonsPanel;
        
        // إحصائيات القسم
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
        #endregion

        #region Events
        public event EventHandler<DepartmentSimple> EditClicked;
        public event EventHandler<DepartmentSimple> DeleteClicked;
        public event EventHandler<DepartmentSimple> OpenClicked;
        #endregion

        #region Constructor
        public DepartmentCard(DepartmentSimple department)
        {
            _department = department;
            InitializeComponent();
            SetupCard();
            CreateControls();
            LoadDepartmentData();
        }
        #endregion

        #region Properties
        public DepartmentSimple Department
        {
            get { return _department; }
            set
            {
                _department = value;
                LoadDepartmentData();
            }
        }
        #endregion

        #region Initialization
        private void SetupCard()
        {
            this.Size = new Size(300, 200);
            this.BackColor = Color.White;
            this.BorderStyle = BorderStyle.FixedSingle;
            this.Padding = new Padding(5);
            this.RightToLeft = RightToLeft.Yes;
            this.Font = RTLHelper.ArabicFont;
            
            // تأثيرات الهوفر
            this.MouseEnter += DepartmentCard_MouseEnter;
            this.MouseLeave += DepartmentCard_MouseLeave;
        }

        private void CreateControls()
        {
            // لوحة العنوان
            _headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = RTLHelper.AccentColor,
                Padding = new Padding(10, 5, 10, 5)
            };

            _nameLabel = RTLHelper.CreateStyledLabel(
                "",
                new Point(10, 10),
                new Size(270, 30),
                RTLHelper.ArabicFontBold,
                Color.White,
                ContentAlignment.MiddleRight
            );

            _headerPanel.Controls.Add(_nameLabel);

            // لوحة المحتوى
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(10)
            };

            _statisticsLabel = RTLHelper.CreateStyledLabel(
                "",
                new Point(10, 10),
                new Size(270, 60),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _contentPanel.Controls.Add(_statisticsLabel);

            // لوحة الأزرار
            _buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 50,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(5)
            };

            CreateButtons();

            // إضافة اللوحات للبطاقة
            this.Controls.Add(_contentPanel);
            this.Controls.Add(_headerPanel);
            this.Controls.Add(_buttonsPanel);
        }

        private void CreateButtons()
        {
            int buttonWidth = 85;
            int buttonHeight = 35;
            int spacing = 5;
            int startX = 5;

            _editButton = RTLHelper.CreateStyledButton(
                "تعديل القسم",
                new Point(startX, 7),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                EditButton_Click
            );

            _deleteButton = RTLHelper.CreateStyledButton(
                "حذف القسم",
                new Point(startX + buttonWidth + spacing, 7),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DangerColor,
                DeleteButton_Click
            );

            _openButton = RTLHelper.CreateStyledButton(
                "فتح القسم",
                new Point(startX + (buttonWidth + spacing) * 2, 7),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                OpenButton_Click
            );

            // تطبيق تأثيرات الأزرار
            RTLHelper.ApplyButtonEffects(_editButton);
            RTLHelper.ApplyButtonEffects(_deleteButton);
            RTLHelper.ApplyButtonEffects(_openButton);

            _buttonsPanel.Controls.Add(_editButton);
            _buttonsPanel.Controls.Add(_deleteButton);
            _buttonsPanel.Controls.Add(_openButton);
        }
        #endregion

        #region Data Loading
        private void LoadDepartmentData()
        {
            if (_department != null)
            {
                _nameLabel.Text = _department.DepartmentName;
                UpdateStatistics();
            }
        }

        public void UpdateStatistics()
        {
            string statisticsText = "الكتب الصادرة: " + OutgoingDocuments + "\n";
            statisticsText += "الكتب الواردة: " + IncomingDocuments + "\n";
            statisticsText += "المجموع: " + (OutgoingDocuments + IncomingDocuments);
            
            _statisticsLabel.Text = statisticsText;
        }
        #endregion

        #region Event Handlers
        private void EditButton_Click(object sender, EventArgs e)
        {
            if (EditClicked != null)
                EditClicked(this, _department);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (DeleteClicked != null)
                DeleteClicked(this, _department);
        }

        private void OpenButton_Click(object sender, EventArgs e)
        {
            if (OpenClicked != null)
                OpenClicked(this, _department);
        }

        private void DepartmentCard_MouseEnter(object sender, EventArgs e)
        {
            this.BackColor = RTLHelper.LightColor;
            this.BorderStyle = BorderStyle.Fixed3D;
        }

        private void DepartmentCard_MouseLeave(object sender, EventArgs e)
        {
            this.BackColor = Color.White;
            this.BorderStyle = BorderStyle.FixedSingle;
        }
        #endregion

        #region Designer Code
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.Name = "DepartmentCard";
            this.Size = new Size(300, 200);
            this.ResumeLayout(false);
        }
        #endregion
    }
}
