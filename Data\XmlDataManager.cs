using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Windows.Forms;
using ArchiveSystem.Models;

namespace ArchiveSystem.Data
{
    public class XmlDataManager
    {
        private static XmlDataManager _instance;
        private static readonly object _lock = new object();
        private string _dataPath;
        private string _departmentsFile;
        private string _foldersFile;
        private string _documentsFile;

        public static XmlDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new XmlDataManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private XmlDataManager()
        {
            InitializeData();
        }

        private void InitializeData()
        {
            _dataPath = Path.Combine(Application.StartupPath, "Data");
            _departmentsFile = Path.Combine(_dataPath, "departments.xml");
            _foldersFile = Path.Combine(_dataPath, "folders.xml");
            _documentsFile = Path.Combine(_dataPath, "documents.xml");

            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }

            CreateInitialFiles();
        }

        private void CreateInitialFiles()
        {
            if (!File.Exists(_departmentsFile))
            {
                CreateDepartmentsFile();
            }

            if (!File.Exists(_foldersFile))
            {
                CreateFoldersFile();
            }

            if (!File.Exists(_documentsFile))
            {
                CreateDocumentsFile();
            }
        }

        private void CreateDepartmentsFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Departments");
            doc.AppendChild(root);

            // إضافة أقسام افتراضية
            var departments = new[]
            {
                new { Id = 1, Name = "الإدارة العامة", Description = "القسم الرئيسي للإدارة العامة والتنسيق" },
                new { Id = 2, Name = "الشؤون المالية", Description = "قسم الشؤون المالية والمحاسبة والميزانية" },
                new { Id = 3, Name = "الموارد البشرية", Description = "قسم الموارد البشرية والتوظيف والتدريب" },
                new { Id = 4, Name = "تقنية المعلومات", Description = "قسم تقنية المعلومات والدعم التقني" },
                new { Id = 5, Name = "الشؤون القانونية", Description = "قسم الشؤون القانونية والاستشارات القانونية" }
            };

            foreach (var dept in departments)
            {
                var element = doc.CreateElement("Department");
                element.SetAttribute("Id", dept.Id.ToString());
                element.SetAttribute("Name", dept.Name);
                element.SetAttribute("Description", dept.Description);
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("IsActive", "true");
                root.AppendChild(element);
            }

            doc.Save(_departmentsFile);
        }

        private void CreateFoldersFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Folders");
            doc.AppendChild(root);

            // إضافة أضابير افتراضية
            var folders = new[]
            {
                new { Id = 1, Name = "أضبارة عامة", Description = "أضبارة للوثائق العامة والمراسلات", DepartmentId = 1 },
                new { Id = 2, Name = "التعاميم", Description = "أضبارة التعاميم والقرارات الإدارية", DepartmentId = 1 },
                new { Id = 3, Name = "الميزانية", Description = "أضبارة وثائق الميزانية والمالية", DepartmentId = 2 },
                new { Id = 4, Name = "المشتريات", Description = "أضبارة وثائق المشتريات والعقود", DepartmentId = 2 },
                new { Id = 5, Name = "التوظيف", Description = "أضبارة وثائق التوظيف والتعيينات", DepartmentId = 3 },
                new { Id = 6, Name = "التدريب", Description = "أضبارة وثائق التدريب والتطوير", DepartmentId = 3 },
                new { Id = 7, Name = "المشاريع التقنية", Description = "أضبارة مشاريع تقنية المعلومات", DepartmentId = 4 },
                new { Id = 8, Name = "العقود القانونية", Description = "أضبارة العقود والاتفاقيات القانونية", DepartmentId = 5 }
            };

            foreach (var folder in folders)
            {
                var element = doc.CreateElement("Folder");
                element.SetAttribute("Id", folder.Id.ToString());
                element.SetAttribute("Name", folder.Name);
                element.SetAttribute("Description", folder.Description);
                element.SetAttribute("DepartmentId", folder.DepartmentId.ToString());
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("IsActive", "true");
                root.AppendChild(element);
            }

            doc.Save(_foldersFile);
        }

        private void CreateDocumentsFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Documents");
            doc.AppendChild(root);

            // إضافة وثائق تجريبية
            var documents = new[]
            {
                new { Id = 1, Number = "001", Subject = "كتاب تعميم جديد", Type = "صادر", DepartmentId = 1, FolderId = 2 },
                new { Id = 2, Number = "002", Subject = "طلب معلومات مالية", Type = "وارد", DepartmentId = 2, FolderId = 3 },
                new { Id = 3, Number = "003", Subject = "تقرير التوظيف الشهري", Type = "صادر", DepartmentId = 3, FolderId = 5 },
                new { Id = 4, Number = "004", Subject = "استفسار قانوني", Type = "وارد", DepartmentId = 5, FolderId = 8 },
                new { Id = 5, Number = "005", Subject = "خطة المشاريع التقنية", Type = "صادر", DepartmentId = 4, FolderId = 7 }
            };

            foreach (var doc in documents)
            {
                var element = doc.CreateElement("Document");
                element.SetAttribute("Id", doc.Id.ToString());
                element.SetAttribute("Number", doc.Number);
                element.SetAttribute("Subject", doc.Subject);
                element.SetAttribute("Type", doc.Type);
                element.SetAttribute("DepartmentId", doc.DepartmentId.ToString());
                element.SetAttribute("FolderId", doc.FolderId.ToString());
                element.SetAttribute("DocumentDate", DateTime.Now.AddDays(-doc.Id).ToString("yyyy-MM-dd"));
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("CreatedBy", "النظام");
                element.SetAttribute("IsActive", "true");
                root.AppendChild(element);
            }

            doc.Save(_documentsFile);
        }

        public List<Department> GetDepartments()
        {
            var departments = new List<Department>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                foreach (XmlNode node in doc.SelectNodes("//Department[@IsActive='true']"))
                {
                    var department = new Department
                    {
                        DepartmentId = int.Parse(node.Attributes["Id"].Value),
                        DepartmentName = node.Attributes["Name"].Value,
                        Description = node.Attributes["Description"].Value,
                        CreatedDate = DateTime.Parse(node.Attributes["CreatedDate"].Value),
                        IsActive = bool.Parse(node.Attributes["IsActive"].Value)
                    };
                    departments.Add(department);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة الأقسام: " + ex.Message);
            }

            return departments;
        }

        public bool SaveDepartment(Department department)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                if (department.DepartmentId == 0)
                {
                    // إضافة قسم جديد
                    department.DepartmentId = GetNextId(doc, "Department");
                    
                    var element = doc.CreateElement("Department");
                    element.SetAttribute("Id", department.DepartmentId.ToString());
                    element.SetAttribute("Name", department.DepartmentName);
                    element.SetAttribute("Description", department.Description ?? "");
                    element.SetAttribute("CreatedDate", department.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    element.SetAttribute("IsActive", department.IsActive.ToString().ToLower());
                    
                    doc.DocumentElement.AppendChild(element);
                }
                else
                {
                    // تحديث قسم موجود
                    var node = doc.SelectSingleNode($"//Department[@Id='{department.DepartmentId}']");
                    if (node != null)
                    {
                        node.Attributes["Name"].Value = department.DepartmentName;
                        node.Attributes["Description"].Value = department.Description ?? "";
                        node.Attributes["IsActive"].Value = department.IsActive.ToString().ToLower();
                    }
                }

                doc.Save(_departmentsFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteDepartment(int departmentId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                var node = doc.SelectSingleNode($"//Department[@Id='{departmentId}']");
                if (node != null)
                {
                    // حذف منطقي
                    node.Attributes["IsActive"].Value = "false";
                    doc.Save(_departmentsFile);
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        public int GetDepartmentsCount()
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);
                return doc.SelectNodes("//Department[@IsActive='true']").Count;
            }
            catch
            {
                return 0;
            }
        }

        public int GetFoldersCount()
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);
                return doc.SelectNodes("//Folder[@IsActive='true']").Count;
            }
            catch
            {
                return 0;
            }
        }

        public int GetDocumentsCount(string type = null)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);
                
                string xpath = "//Document[@IsActive='true']";
                if (!string.IsNullOrEmpty(type))
                {
                    xpath += $"[@Type='{type}']";
                }
                
                return doc.SelectNodes(xpath).Count;
            }
            catch
            {
                return 0;
            }
        }

        public bool DepartmentNameExists(string name, int excludeId = 0)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);
                
                string xpath = $"//Department[@Name='{name}' and @IsActive='true']";
                if (excludeId > 0)
                {
                    xpath += $" and @Id!='{excludeId}'";
                }
                
                return doc.SelectSingleNode(xpath) != null;
            }
            catch
            {
                return false;
            }
        }

        private int GetNextId(XmlDocument doc, string elementName)
        {
            int maxId = 0;
            foreach (XmlNode node in doc.SelectNodes($"//{elementName}"))
            {
                if (int.TryParse(node.Attributes["Id"].Value, out int id))
                {
                    if (id > maxId) maxId = id;
                }
            }
            return maxId + 1;
        }
    }
}
