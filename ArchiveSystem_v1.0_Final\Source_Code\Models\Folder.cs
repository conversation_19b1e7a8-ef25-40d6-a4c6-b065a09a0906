using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace ArchiveSystem.Models
{
    public class Folder : INotifyPropertyChanged
    {
        private int _folderId;
        private string _folderName;
        private string _description;
        private int _departmentId;
        private DateTime _createdDate;
        private bool _isActive;
        private Department _department;

        public int FolderId
        {
            get => _folderId;
            set { _folderId = value; OnPropertyChanged(nameof(FolderId)); }
        }

        public string FolderName
        {
            get => _folderName;
            set { _folderName = value; OnPropertyChanged(nameof(FolderName)); }
        }

        public string Description
        {
            get => _description;
            set { _description = value; OnPropertyChanged(nameof(Description)); }
        }

        public int DepartmentId
        {
            get => _departmentId;
            set { _departmentId = value; OnPropertyChanged(nameof(DepartmentId)); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
        }

        public Department Department
        {
            get => _department;
            set { _department = value; OnPropertyChanged(nameof(Department)); }
        }

        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string CreatedDateFormatted => CreatedDate.ToString("yyyy/MM/dd");
        public string DepartmentName => Department?.DepartmentName ?? "غير محدد";

        public Folder()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
        }

        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrWhiteSpace(FolderName))
                errors.Add("اسم الأضبارة مطلوب");
            else if (FolderName.Length < 3)
                errors.Add("اسم الأضبارة يجب أن يكون 3 أحرف على الأقل");
            else if (FolderName.Length > 100)
                errors.Add("اسم الأضبارة يجب أن يكون أقل من 100 حرف");

            if (DepartmentId <= 0)
                errors.Add("يجب اختيار القسم");

            if (!string.IsNullOrEmpty(Description) && Description.Length > 500)
                errors.Add("الوصف يجب أن يكون أقل من 500 حرف");

            return errors.Count == 0;
        }

        public override string ToString()
        {
            return FolderName ?? "أضبارة جديدة";
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
