@echo off
chcp 65001 >nul
echo ========================================
echo 🔨 Building PIKA Enhanced Application
echo    بناء تطبيق PIKA المحسن
echo ========================================
echo.
echo 🚀 المميزات المحسنة:
echo    Enhanced Features:
echo ✅ شريط جانبي قابل للطي
echo    Collapsible sidebar
echo ✅ تصميم حديث متوافق مع Windows 7
echo    Modern Windows 7 compatible design
echo ✅ انتقالات سلسة
echo    Smooth animations
echo ✅ إحصائيات تفاعلية
echo    Interactive statistics
echo.
echo ========================================
echo.
echo 🔧 بدء عملية البناء...
echo    Starting build process...
echo.

C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe PIKA_Enhanced.csproj /p:Configuration=Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ Build Successful!
    echo    تم البناء بنجاح!
    echo ========================================
    echo.
    echo 📁 الملف المُنشأ: bin\Release\PIKA_Enhanced.exe
    echo    Created file: bin\Release\PIKA_Enhanced.exe
    echo.
    echo 🎯 المميزات المحققة:
    echo    Achieved Features:
    echo ✅ شريط جانبي قابل للطي في الجانب الأيمن
    echo    Collapsible right sidebar
    echo ✅ تحسينات Windows 7
    echo    Windows 7 optimizations
    echo ✅ تصميم واجهة حديث
    echo    Modern UI design
    echo ✅ انتقالات سلسة
    echo    Smooth animations
    echo ✅ إحصائيات تفاعلية
    echo    Interactive statistics
    echo.
    echo 🚀 للتشغيل: run_enhanced.bat
    echo    To run: run_enhanced.bat
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ Build Failed!
    echo    فشل في البناء!
    echo ========================================
    echo يرجى مراجعة رسائل الخطأ أعلاه
    echo Please check the error messages above.
    echo.
)

echo اضغط أي مفتاح للمتابعة...
echo Press any key to continue...
pause >nul
