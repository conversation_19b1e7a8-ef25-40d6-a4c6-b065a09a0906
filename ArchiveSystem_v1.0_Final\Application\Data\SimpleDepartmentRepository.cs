using System;
using System.Collections.Generic;
using ArchiveSystem.Models;

namespace ArchiveSystem.Data
{
    public class SimpleDepartmentRepository
    {
        private readonly SimpleDataManager _dataManager;

        public SimpleDepartmentRepository()
        {
            _dataManager = SimpleDataManager.Instance;
        }

        public List<DepartmentSimple> GetAll()
        {
            try
            {
                return _dataManager.GetDepartments();
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في استرجاع الأقسام: " + ex.Message);
            }
        }

        public DepartmentSimple GetById(int departmentId)
        {
            try
            {
                var departments = _dataManager.GetDepartments();
                foreach (var dept in departments)
                {
                    if (dept.DepartmentId == departmentId)
                        return dept;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في استرجاع القسم: " + ex.Message);
            }
        }

        public bool Insert(DepartmentSimple department)
        {
            try
            {
                // التحقق من صحة البيانات
                List<string> errors;
                if (!department.IsValid(out errors))
                {
                    throw new ArgumentException("بيانات القسم غير صحيحة: " + string.Join(", ", errors.ToArray()));
                }

                // التحقق من عدم تكرار الاسم
                if (DepartmentNameExists(department.DepartmentName))
                {
                    throw new ArgumentException("اسم القسم '" + department.DepartmentName + "' موجود مسبقاً");
                }

                return _dataManager.SaveDepartment(department);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في إضافة القسم: " + ex.Message);
            }
        }

        public bool Update(DepartmentSimple department)
        {
            try
            {
                // التحقق من صحة البيانات
                List<string> errors;
                if (!department.IsValid(out errors))
                {
                    throw new ArgumentException("بيانات القسم غير صحيحة: " + string.Join(", ", errors.ToArray()));
                }

                // التحقق من عدم تكرار الاسم (باستثناء القسم الحالي)
                if (DepartmentNameExists(department.DepartmentName, department.DepartmentId))
                {
                    throw new ArgumentException("اسم القسم '" + department.DepartmentName + "' موجود مسبقاً");
                }

                return _dataManager.SaveDepartment(department);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في تحديث القسم: " + ex.Message);
            }
        }

        public bool Delete(int departmentId)
        {
            try
            {
                return _dataManager.DeleteDepartment(departmentId);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في حذف القسم: " + ex.Message);
            }
        }

        public bool DepartmentNameExists(string departmentName, int excludeId = 0)
        {
            return _dataManager.DepartmentNameExists(departmentName, excludeId);
        }

        public int GetTotalCount()
        {
            return _dataManager.GetDepartmentsCount();
        }
    }
}
