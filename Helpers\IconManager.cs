using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;

namespace ArchiveSystem.Helpers
{
    /// <summary>
    /// مدير الأيقونات للتطبيق
    /// </summary>
    public static class IconManager
    {
        private static string _iconsPath;
        
        static IconManager()
        {
            _iconsPath = Path.Combine(Application.StartupPath, "Icons");
            if (!Directory.Exists(_iconsPath))
            {
                Directory.CreateDirectory(_iconsPath);
            }
            
            // إنشاء الأيقونات إذا لم تكن موجودة
            CreateApplicationIcons();
        }

        /// <summary>
        /// إنشاء جميع أيقونات التطبيق
        /// </summary>
        private static void CreateApplicationIcons()
        {
            try
            {
                // إنشاء الأيقونة الرئيسية للتطبيق
                CreateMainApplicationIcon();
                
                // إنشاء أيقونات النوافذ الفرعية
                CreateDepartmentsIcon();
                CreateDocumentsIcon();
                CreateSearchIcon();
                CreateSettingsIcon();
                CreateAddIcon();
                CreateEditIcon();
                CreateDeleteIcon();
                CreateViewIcon();
            }
            catch (Exception ex)
            {
                // في حالة فشل إنشاء الأيقونات، نتجاهل الخطأ لتجنب تعطيل التطبيق
                System.Diagnostics.Debug.WriteLine("خطأ في إنشاء الأيقونات: " + ex.Message);
            }
        }

        /// <summary>
        /// إنشاء الأيقونة الرئيسية للتطبيق
        /// </summary>
        private static void CreateMainApplicationIcon()
        {
            var icon = CreateIcon(64, 64, (g, rect) => {
                // خلفية متدرجة
                using (var brush = new LinearGradientBrush(rect, Color.FromArgb(52, 152, 219), Color.FromArgb(41, 128, 185), 45f))
                {
                    g.FillRectangle(brush, rect);
                }

                // رسم مجلد كبير
                var folderRect = new Rectangle(8, 16, 48, 36);
                using (var brush = new SolidBrush(Color.FromArgb(241, 196, 15)))
                {
                    g.FillRectangle(brush, folderRect);
                }

                // رسم وثائق داخل المجلد
                var docRect1 = new Rectangle(16, 24, 12, 16);
                var docRect2 = new Rectangle(32, 28, 12, 16);
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillRectangle(brush, docRect1);
                    g.FillRectangle(brush, docRect2);
                }

                // إضافة حدود
                using (var pen = new Pen(Color.FromArgb(39, 174, 96), 2))
                {
                    g.DrawRectangle(pen, 2, 2, rect.Width - 4, rect.Height - 4);
                }
            });

            SaveIcon(icon, "MainApp.ico");
        }

        /// <summary>
        /// إنشاء أيقونة الأقسام
        /// </summary>
        private static void CreateDepartmentsIcon()
        {
            var icon = CreateIcon(32, 32, (g, rect) => {
                // خلفية
                using (var brush = new SolidBrush(Color.FromArgb(46, 204, 113)))
                {
                    g.FillEllipse(brush, rect);
                }

                // رسم مجلد
                var folderRect = new Rectangle(6, 8, 20, 16);
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillRectangle(brush, folderRect);
                }

                // رسم علامة زائد
                using (var pen = new Pen(Color.FromArgb(46, 204, 113), 2))
                {
                    g.DrawLine(pen, 16, 12, 16, 20);
                    g.DrawLine(pen, 12, 16, 20, 16);
                }
            });

            SaveIcon(icon, "Departments.ico");
        }

        /// <summary>
        /// إنشاء أيقونة الوثائق
        /// </summary>
        private static void CreateDocumentsIcon()
        {
            var icon = CreateIcon(32, 32, (g, rect) => {
                // خلفية
                using (var brush = new SolidBrush(Color.FromArgb(52, 152, 219)))
                {
                    g.FillEllipse(brush, rect);
                }

                // رسم وثيقة
                var docRect = new Rectangle(8, 6, 16, 20);
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillRectangle(brush, docRect);
                }

                // رسم خطوط النص
                using (var pen = new Pen(Color.FromArgb(52, 152, 219), 1))
                {
                    g.DrawLine(pen, 10, 10, 22, 10);
                    g.DrawLine(pen, 10, 13, 20, 13);
                    g.DrawLine(pen, 10, 16, 22, 16);
                    g.DrawLine(pen, 10, 19, 18, 19);
                }
            });

            SaveIcon(icon, "Documents.ico");
        }

        /// <summary>
        /// إنشاء أيقونة البحث
        /// </summary>
        private static void CreateSearchIcon()
        {
            var icon = CreateIcon(32, 32, (g, rect) => {
                // خلفية
                using (var brush = new SolidBrush(Color.FromArgb(155, 89, 182)))
                {
                    g.FillEllipse(brush, rect);
                }

                // رسم عدسة مكبرة
                using (var pen = new Pen(Color.White, 2))
                {
                    g.DrawEllipse(pen, 8, 8, 12, 12);
                    g.DrawLine(pen, 18, 18, 24, 24);
                }
            });

            SaveIcon(icon, "Search.ico");
        }

        /// <summary>
        /// إنشاء أيقونة الإعدادات
        /// </summary>
        private static void CreateSettingsIcon()
        {
            var icon = CreateIcon(32, 32, (g, rect) => {
                // خلفية
                using (var brush = new SolidBrush(Color.FromArgb(127, 140, 141)))
                {
                    g.FillEllipse(brush, rect);
                }

                // رسم ترس
                var center = new Point(16, 16);
                var radius = 8;
                using (var pen = new Pen(Color.White, 2))
                {
                    // رسم الترس بشكل مبسط
                    g.DrawEllipse(pen, center.X - radius, center.Y - radius, radius * 2, radius * 2);
                    g.DrawEllipse(pen, center.X - 3, center.Y - 3, 6, 6);
                    
                    // رسم أسنان الترس
                    for (int i = 0; i < 8; i++)
                    {
                        double angle = i * Math.PI / 4;
                        int x1 = center.X + (int)(radius * Math.Cos(angle));
                        int y1 = center.Y + (int)(radius * Math.Sin(angle));
                        int x2 = center.X + (int)((radius + 3) * Math.Cos(angle));
                        int y2 = center.Y + (int)((radius + 3) * Math.Sin(angle));
                        g.DrawLine(pen, x1, y1, x2, y2);
                    }
                }
            });

            SaveIcon(icon, "Settings.ico");
        }

        /// <summary>
        /// إنشاء أيقونة الإضافة
        /// </summary>
        private static void CreateAddIcon()
        {
            var icon = CreateIcon(24, 24, (g, rect) => {
                using (var brush = new SolidBrush(Color.FromArgb(46, 204, 113)))
                {
                    g.FillEllipse(brush, rect);
                }

                using (var pen = new Pen(Color.White, 2))
                {
                    g.DrawLine(pen, 12, 6, 12, 18);
                    g.DrawLine(pen, 6, 12, 18, 12);
                }
            });

            SaveIcon(icon, "Add.ico");
        }

        /// <summary>
        /// إنشاء أيقونة التعديل
        /// </summary>
        private static void CreateEditIcon()
        {
            var icon = CreateIcon(24, 24, (g, rect) => {
                using (var brush = new SolidBrush(Color.FromArgb(52, 152, 219)))
                {
                    g.FillEllipse(brush, rect);
                }

                // رسم قلم
                using (var pen = new Pen(Color.White, 2))
                {
                    g.DrawLine(pen, 8, 16, 16, 8);
                    g.DrawLine(pen, 6, 18, 8, 16);
                }
            });

            SaveIcon(icon, "Edit.ico");
        }

        /// <summary>
        /// إنشاء أيقونة الحذف
        /// </summary>
        private static void CreateDeleteIcon()
        {
            var icon = CreateIcon(24, 24, (g, rect) => {
                using (var brush = new SolidBrush(Color.FromArgb(231, 76, 60)))
                {
                    g.FillEllipse(brush, rect);
                }

                using (var pen = new Pen(Color.White, 2))
                {
                    g.DrawLine(pen, 8, 8, 16, 16);
                    g.DrawLine(pen, 16, 8, 8, 16);
                }
            });

            SaveIcon(icon, "Delete.ico");
        }

        /// <summary>
        /// إنشاء أيقونة العرض
        /// </summary>
        private static void CreateViewIcon()
        {
            var icon = CreateIcon(24, 24, (g, rect) => {
                using (var brush = new SolidBrush(Color.FromArgb(155, 89, 182)))
                {
                    g.FillEllipse(brush, rect);
                }

                // رسم عين
                using (var pen = new Pen(Color.White, 2))
                {
                    g.DrawEllipse(pen, 6, 10, 12, 4);
                    g.FillEllipse(Brushes.White, 10, 11, 4, 2);
                }
            });

            SaveIcon(icon, "View.ico");
        }

        /// <summary>
        /// إنشاء أيقونة مخصصة
        /// </summary>
        private static Bitmap CreateIcon(int width, int height, Action<Graphics, Rectangle> drawAction)
        {
            var bitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;

                var rect = new Rectangle(0, 0, width, height);
                drawAction(g, rect);
            }
            return bitmap;
        }

        /// <summary>
        /// حفظ الأيقونة كملف ICO
        /// </summary>
        private static void SaveIcon(Bitmap bitmap, string fileName)
        {
            try
            {
                string filePath = Path.Combine(_iconsPath, fileName);
                
                // تحويل Bitmap إلى Icon وحفظه
                using (var icon = Icon.FromHandle(bitmap.GetHicon()))
                {
                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        icon.Save(fileStream);
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الحفظ
            }
            finally
            {
                if (bitmap != null)
                    bitmap.Dispose();
            }
        }

        /// <summary>
        /// الحصول على أيقونة من الملف
        /// </summary>
        public static Icon GetIcon(string iconName)
        {
            try
            {
                string filePath = Path.Combine(_iconsPath, iconName);
                if (File.Exists(filePath))
                {
                    return new Icon(filePath);
                }
            }
            catch
            {
                // في حالة فشل تحميل الأيقونة، نعيد null
            }
            return null;
        }

        /// <summary>
        /// تطبيق الأيقونة على النافذة
        /// </summary>
        public static void SetFormIcon(Form form, string iconName)
        {
            try
            {
                var icon = GetIcon(iconName);
                if (icon != null)
                {
                    form.Icon = icon;
                }
            }
            catch
            {
                // تجاهل أخطاء تطبيق الأيقونة
            }
        }

        /// <summary>
        /// الحصول على مسار مجلد الأيقونات
        /// </summary>
        public static string GetIconsPath()
        {
            return _iconsPath;
        }
    }
}
