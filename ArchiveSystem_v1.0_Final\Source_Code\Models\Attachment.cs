using System;
using System.ComponentModel;
using System.IO;

namespace ArchiveSystem.Models
{
    public class Attachment : INotifyPropertyChanged
    {
        private int _attachmentId;
        private int _documentId;
        private string _fileName;
        private string _originalFileName;
        private string _filePath;
        private long _fileSize;
        private string _fileType;
        private string _mimeType;
        private DateTime _uploadDate;
        private string _uploadedBy;
        private bool _isActive;

        public int AttachmentId
        {
            get => _attachmentId;
            set { _attachmentId = value; OnPropertyChanged(nameof(AttachmentId)); }
        }

        public int DocumentId
        {
            get => _documentId;
            set { _documentId = value; OnPropertyChanged(nameof(DocumentId)); }
        }

        public string FileName
        {
            get => _fileName;
            set { _fileName = value; OnPropertyChanged(nameof(FileName)); }
        }

        public string OriginalFileName
        {
            get => _originalFileName;
            set { _originalFileName = value; OnPropertyChanged(nameof(OriginalFileName)); }
        }

        public string FilePath
        {
            get => _filePath;
            set { _filePath = value; OnPropertyChanged(nameof(FilePath)); }
        }

        public long FileSize
        {
            get => _fileSize;
            set { _fileSize = value; OnPropertyChanged(nameof(FileSize)); OnPropertyChanged(nameof(FileSizeFormatted)); }
        }

        public string FileType
        {
            get => _fileType;
            set { _fileType = value; OnPropertyChanged(nameof(FileType)); }
        }

        public string MimeType
        {
            get => _mimeType;
            set { _mimeType = value; OnPropertyChanged(nameof(MimeType)); }
        }

        public DateTime UploadDate
        {
            get => _uploadDate;
            set { _uploadDate = value; OnPropertyChanged(nameof(UploadDate)); }
        }

        public string UploadedBy
        {
            get => _uploadedBy;
            set { _uploadedBy = value; OnPropertyChanged(nameof(UploadedBy)); }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
        }

        // خصائص محسوبة
        public string FileSizeFormatted
        {
            get
            {
                if (FileSize < 1024)
                    return FileSize + " بايت";
                else if (FileSize < 1024 * 1024)
                    return (FileSize / 1024.0).ToString("F1") + " كيلوبايت";
                else if (FileSize < 1024 * 1024 * 1024)
                    return (FileSize / (1024.0 * 1024.0)).ToString("F1") + " ميجابايت";
                else
                    return (FileSize / (1024.0 * 1024.0 * 1024.0)).ToString("F1") + " جيجابايت";
            }
        }

        public string UploadDateFormatted => UploadDate.ToString("yyyy/MM/dd HH:mm");
        public string StatusText => IsActive ? "نشط" : "محذوف";
        public bool FileExists => !string.IsNullOrEmpty(FilePath) && File.Exists(FilePath);

        public string FileIcon
        {
            get
            {
                if (string.IsNullOrEmpty(FileType)) return "📄";
                
                string ext = FileType.ToLower();
                switch (ext)
                {
                    case ".pdf": return "📕";
                    case ".doc":
                    case ".docx": return "📘";
                    case ".xls":
                    case ".xlsx": return "📗";
                    case ".ppt":
                    case ".pptx": return "📙";
                    case ".jpg":
                    case ".jpeg":
                    case ".png":
                    case ".gif":
                    case ".bmp": return "🖼️";
                    case ".txt": return "📝";
                    case ".zip":
                    case ".rar":
                    case ".7z": return "🗜️";
                    default: return "📄";
                }
            }
        }

        public bool IsImage
        {
            get
            {
                if (string.IsNullOrEmpty(FileType)) return false;
                string ext = FileType.ToLower();
                return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif" || ext == ".bmp";
            }
        }

        public bool IsPdf => !string.IsNullOrEmpty(FileType) && FileType.ToLower() == ".pdf";

        public bool IsOfficeDocument
        {
            get
            {
                if (string.IsNullOrEmpty(FileType)) return false;
                string ext = FileType.ToLower();
                return ext == ".doc" || ext == ".docx" || ext == ".xls" || ext == ".xlsx" || 
                       ext == ".ppt" || ext == ".pptx";
            }
        }

        public Attachment()
        {
            UploadDate = DateTime.Now;
            IsActive = true;
            UploadedBy = "النظام";
        }

        public bool IsValid(out string error)
        {
            error = null;

            if (DocumentId <= 0)
            {
                error = "معرف الوثيقة غير صحيح";
                return false;
            }

            if (string.IsNullOrWhiteSpace(OriginalFileName))
            {
                error = "اسم الملف الأصلي مطلوب";
                return false;
            }

            if (string.IsNullOrWhiteSpace(FilePath))
            {
                error = "مسار الملف مطلوب";
                return false;
            }

            if (FileSize <= 0)
            {
                error = "حجم الملف غير صحيح";
                return false;
            }

            if (FileSize > 50 * 1024 * 1024) // 50MB
            {
                error = "حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)";
                return false;
            }

            return true;
        }

        public static string[] GetAllowedFileTypes()
        {
            return new string[]
            {
                ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".txt", ".zip", ".rar", ".7z"
            };
        }

        public static bool IsFileTypeAllowed(string fileExtension)
        {
            if (string.IsNullOrEmpty(fileExtension)) return false;
            
            string[] allowedTypes = GetAllowedFileTypes();
            return Array.Exists(allowedTypes, type => 
                type.Equals(fileExtension, StringComparison.OrdinalIgnoreCase));
        }

        public override string ToString()
        {
            return OriginalFileName ?? FileName ?? "مرفق";
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
