using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    public partial class SimpleDocumentsForm : Form
    {
        private ListView _documentsList;
        private ComboBox _typeComboBox;
        private ComboBox _yearComboBox;
        private TextBox _searchTextBox;
        private Button _searchButton;
        private Button _addButton;
        private Button _editButton;
        private Button _deleteButton;
        private Button _viewButton;
        private Button _closeButton;
        private List<DocumentSimple> _documents;
        private List<DocumentSimple> _filteredDocuments;
        private SimpleDataManager _dataManager;

        public SimpleDocumentsForm()
        {
            InitializeComponent();
            _dataManager = SimpleDataManager.Instance;
            SetupForm();
            CreateControls();
            LoadDocuments();

            // تطبيق إعدادات RTL والأيقونة
            RTLHelper.ApplyRTLSettings(this);
            IconManager.SetFormIcon(this, "Documents.ico");

            // إصلاح مشاكل المحاذاة
            RTLHelper.FixCommonAlignmentIssues(this);
        }

        private void SetupForm()
        {
            this.Text = "📄 إدارة الوثائق";
            this.Size = new Size(1100, 750);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(1000, 700);
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "إدارة الوثائق",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            CreateSearchControls();
            CreateDocumentsList();
            CreateControlButtons();
        }

        private void CreateSearchControls()
        {
            // لوحة البحث
            var searchPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(950, 60),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // نوع الوثيقة
            var typeLabel = new Label
            {
                Text = "النوع:",
                Location = new Point(850, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(typeLabel);

            _typeComboBox = new ComboBox
            {
                Location = new Point(720, 15),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            _typeComboBox.Items.AddRange(new object[] { "الكل", "صادر", "وارد" });
            _typeComboBox.SelectedIndex = 0;
            searchPanel.Controls.Add(_typeComboBox);

            // السنة
            var yearLabel = new Label
            {
                Text = "السنة:",
                Location = new Point(650, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(yearLabel);

            _yearComboBox = new ComboBox
            {
                Location = new Point(520, 15),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            _yearComboBox.Items.Add("الكل");
            for (int year = DateTime.Now.Year; year >= 2020; year--)
            {
                _yearComboBox.Items.Add(year.ToString());
            }
            _yearComboBox.SelectedIndex = 0;
            searchPanel.Controls.Add(_yearComboBox);

            // البحث النصي
            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(450, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(searchLabel);

            _searchTextBox = new TextBox
            {
                Location = new Point(200, 15),
                Size = new Size(240, 25),
                RightToLeft = RightToLeft.Yes
            };
            searchPanel.Controls.Add(_searchTextBox);

            _searchButton = new Button
            {
                Text = "بحث",
                Location = new Point(120, 12),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _searchButton.FlatAppearance.BorderSize = 0;
            _searchButton.Click += SearchButton_Click;
            searchPanel.Controls.Add(_searchButton);

            this.Controls.Add(searchPanel);
        }

        private void CreateDocumentsList()
        {
            _documentsList = new ListView
            {
                Location = new Point(20, 140),
                Size = new Size(750, 450),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false,
                Font = new Font("Tahoma", 10F),
                RightToLeftLayout = true
            };

            _documentsList.Columns.Add("رقم الكتاب", 100);
            _documentsList.Columns.Add("الموضوع", 200);
            _documentsList.Columns.Add("النوع", 80);
            _documentsList.Columns.Add("القسم", 120);
            _documentsList.Columns.Add("التاريخ", 100);
            _documentsList.Columns.Add("المرسل/المستقبل", 150);

            this.Controls.Add(_documentsList);
        }

        private void CreateControlButtons()
        {
            int buttonWidth = 120;
            int buttonHeight = 35;
            int startX = 800;
            int startY = 140;
            int spacing = 45;

            _addButton = new Button
            {
                Text = "إضافة وثيقة",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX, startY),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            _addButton.FlatAppearance.BorderSize = 0;
            _addButton.Click += AddButton_Click;
            this.Controls.Add(_addButton);

            _editButton = new Button
            {
                Text = "تعديل وثيقة",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX, startY + spacing),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false
            };
            _editButton.FlatAppearance.BorderSize = 0;
            _editButton.Click += EditButton_Click;
            this.Controls.Add(_editButton);

            _deleteButton = new Button
            {
                Text = "حذف وثيقة",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX, startY + spacing * 2),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false
            };
            _deleteButton.FlatAppearance.BorderSize = 0;
            _deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(_deleteButton);

            _viewButton = new Button
            {
                Text = "عرض التفاصيل",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX, startY + spacing * 3),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false
            };
            _viewButton.FlatAppearance.BorderSize = 0;
            _viewButton.Click += ViewButton_Click;
            this.Controls.Add(_viewButton);

            _closeButton = new Button
            {
                Text = "إغلاق",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX, startY + spacing * 5),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            _closeButton.FlatAppearance.BorderSize = 0;
            _closeButton.Click += delegate { this.Close(); };
            this.Controls.Add(_closeButton);

            _documentsList.SelectedIndexChanged += DocumentsList_SelectedIndexChanged;
        }

        private void LoadDocuments()
        {
            try
            {
                _documents = _dataManager.GetDocuments();
                _filteredDocuments = new List<DocumentSimple>(_documents);
                RefreshDocumentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الوثائق: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                _documents = new List<DocumentSimple>();
                _filteredDocuments = new List<DocumentSimple>();
            }
        }

        private void RefreshDocumentsList()
        {
            _documentsList.Items.Clear();

            foreach (var doc in _filteredDocuments)
            {
                var item = new ListViewItem(doc.DocumentNumber);
                item.SubItems.Add(doc.Subject);
                item.SubItems.Add(doc.TypeIcon + " " + doc.DocumentType);
                item.SubItems.Add(doc.DepartmentName ?? "غير محدد");
                item.SubItems.Add(doc.DocumentDateFormatted);
                item.SubItems.Add(doc.SenderReceiver ?? "");
                item.Tag = doc;

                _documentsList.Items.Add(item);
            }
        }

        private void DocumentsList_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool hasSelection = _documentsList.SelectedItems.Count > 0;
            _editButton.Enabled = hasSelection;
            _deleteButton.Enabled = hasSelection;
            _viewButton.Enabled = hasSelection;
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                string typeFilter = _typeComboBox.SelectedItem.ToString() == "الكل" ? null : _typeComboBox.SelectedItem.ToString();
                string searchText = string.IsNullOrEmpty(_searchTextBox.Text) ? null : _searchTextBox.Text.Trim();

                _filteredDocuments = _dataManager.GetDocuments(typeFilter, searchText);

                // تصفية حسب السنة
                if (_yearComboBox.SelectedItem.ToString() != "الكل")
                {
                    int selectedYear = int.Parse(_yearComboBox.SelectedItem.ToString());
                    var yearFiltered = new List<DocumentSimple>();
                    foreach (var doc in _filteredDocuments)
                    {
                        if (doc.DocumentDate.Year == selectedYear)
                            yearFiltered.Add(doc);
                    }
                    _filteredDocuments = yearFiltered;
                }

                RefreshDocumentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في البحث: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة وثيقة جديدة قريباً", "إضافة وثيقة", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_documentsList.SelectedItems.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsList.SelectedItems[0].Tag;
                MessageBox.Show("سيتم فتح نافذة تعديل الوثيقة: " + selectedDoc.Subject, "تعديل وثيقة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_documentsList.SelectedItems.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsList.SelectedItems[0].Tag;
                
                var result = MessageBox.Show("هل أنت متأكد من حذف الوثيقة '" + selectedDoc.Subject + "'؟", 
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        if (_dataManager.DeleteDocument(selectedDoc.DocumentId))
                        {
                            LoadDocuments(); // إعادة تحميل القائمة
                            MessageBox.Show("تم حذف الوثيقة بنجاح!", "نجح الحذف", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الوثيقة", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في حذف الوثيقة: " + ex.Message, "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ViewButton_Click(object sender, EventArgs e)
        {
            if (_documentsList.SelectedItems.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsList.SelectedItems[0].Tag;
                ShowDocumentDetails(selectedDoc);
            }
        }

        private void ShowDocumentDetails(DocumentSimple document)
        {
            string details = "تفاصيل الوثيقة:\n\n";
            details += "رقم الوثيقة: " + document.DocumentNumber + "\n";
            details += "الموضوع: " + document.Subject + "\n";
            details += "النوع: " + document.DocumentType + "\n";
            details += "القسم: " + document.DepartmentName + "\n";
            details += "التاريخ: " + document.DocumentDateFormatted + "\n";
            details += "المرسل/المستقبل: " + (document.SenderReceiver ?? "غير محدد") + "\n";
            details += "تاريخ الإنشاء: " + document.CreatedDate.ToString("yyyy/MM/dd HH:mm") + "\n";
            details += "أنشأ بواسطة: " + document.CreatedBy + "\n";

            MessageBox.Show(details, "تفاصيل الوثيقة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Name = "SimpleDocumentsForm";
            this.ResumeLayout(false);
        }
    }
}
