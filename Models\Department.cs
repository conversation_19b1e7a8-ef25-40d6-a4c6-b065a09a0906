using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace ArchiveSystem.Models
{
    public class Department : INotifyPropertyChanged
    {
        private int _departmentId;
        private string _departmentName;
        private string _description;
        private DateTime _createdDate;
        private bool _isActive;

        public int DepartmentId
        {
            get => _departmentId;
            set { _departmentId = value; OnPropertyChanged(nameof(DepartmentId)); }
        }

        public string DepartmentName
        {
            get => _departmentName;
            set { _departmentName = value; OnPropertyChanged(nameof(DepartmentName)); }
        }

        public string Description
        {
            get => _description;
            set { _description = value; OnPropertyChanged(nameof(Description)); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
        }

        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string CreatedDateFormatted => CreatedDate.ToString("yyyy/MM/dd");

        public Department()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
        }

        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrWhiteSpace(DepartmentName))
                errors.Add("اسم القسم مطلوب");
            else if (DepartmentName.Length < 3)
                errors.Add("اسم القسم يجب أن يكون 3 أحرف على الأقل");
            else if (DepartmentName.Length > 100)
                errors.Add("اسم القسم يجب أن يكون أقل من 100 حرف");

            if (!string.IsNullOrEmpty(Description) && Description.Length > 500)
                errors.Add("الوصف يجب أن يكون أقل من 500 حرف");

            return errors.Count == 0;
        }

        public override string ToString()
        {
            return DepartmentName ?? "قسم جديد";
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
