using System;
using System.Drawing;
using System.Windows.Forms;

namespace ArchiveSystem
{
    public partial class TestMainForm : Form
    {
        public TestMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Name = "TestMainForm";
            this.Text = "اختبار نظام الأرشفة الإلكترونية";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(236, 240, 241);
            
            // إضافة label بسيط للاختبار
            var testLabel = new Label();
            testLabel.Text = "مرحباً! التطبيق يعمل بنجاح";
            testLabel.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            testLabel.ForeColor = Color.FromArgb(52, 73, 94);
            testLabel.Location = new Point(200, 200);
            testLabel.Size = new Size(400, 50);
            testLabel.TextAlign = ContentAlignment.MiddleCenter;
            testLabel.RightToLeft = RightToLeft.Yes;
            
            this.Controls.Add(testLabel);
            
            // إضافة زر للاختبار
            var testButton = new Button();
            testButton.Text = "اختبار الزر";
            testButton.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            testButton.Size = new Size(150, 40);
            testButton.Location = new Point(325, 300);
            testButton.BackColor = Color.FromArgb(52, 152, 219);
            testButton.ForeColor = Color.White;
            testButton.FlatStyle = FlatStyle.Flat;
            testButton.FlatAppearance.BorderSize = 0;
            testButton.RightToLeft = RightToLeft.Yes;
            testButton.Click += TestButton_Click;
            
            this.Controls.Add(testButton);
            
            this.ResumeLayout(false);
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("الزر يعمل بشكل صحيح!", "اختبار ناجح", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }
    }

    static class TestProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var form = new TestMainForm();
                Application.Run(form);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في التطبيق: " + ex.Message + "\n\nتفاصيل:\n" + ex.ToString(), 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
