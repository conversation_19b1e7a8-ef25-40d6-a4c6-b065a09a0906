using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Helpers;
using ArchiveSystem.Controls;

namespace ArchiveSystem
{
    /// <summary>
    /// النافذة الرئيسية المحسنة مع شريط جانبي قابل للطي وتصميم حديث
    /// </summary>
    public partial class EnhancedMainForm : Form
    {
        #region Fields
        private CollapsibleSidebar _sidebar;
        private Panel _headerPanel;
        private Panel _mainContentPanel;
        private Panel _statisticsPanel;
        private Panel _quickActionsPanel;
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;
        private Timer _statisticsUpdateTimer;
        #endregion

        #region Constructor
        public EnhancedMainForm()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                SetupForm();
                CreateControls();
                SetupTimers();

                // تطبيق إعدادات RTL والأيقونة
                SetApplicationIcon();
                RTLHelper.ComprehensiveRTLFix(this);

                // تطبيق تحسينات Windows 7
                Windows7Helper.ApplyWindows7Enhancements(this);

                // تطبيق تأثيرات الانتقال
                RTLHelper.ApplyFormTransitions(this);
                Windows7Helper.ApplyFadeEffect(this, true, 500);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة النافذة الرئيسية: " + ex.Message +
                              "\n\nسيتم تشغيل النافذة بالإعدادات الافتراضية.",
                              "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                SetupBasicForm();
            }
        }
        #endregion

        #region Initialization Methods
        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void SetupForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية المتقدم - PIKA Enhanced";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.WindowState = FormWindowState.Maximized;
            this.MinimumSize = new Size(1200, 700);

            // تحسينات Windows 7
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | 
                         ControlStyles.UserPaint | 
                         ControlStyles.DoubleBuffer | 
                         ControlStyles.ResizeRedraw, true);
        }

        private void SetupBasicForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void SetupTimers()
        {
            // مؤقت تحديث الإحصائيات كل 30 ثانية
            _statisticsUpdateTimer = new Timer
            {
                Interval = 30000,
                Enabled = true
            };
            _statisticsUpdateTimer.Tick += (s, e) => RefreshStatistics();
        }
        #endregion

        #region UI Creation Methods
        private void CreateControls()
        {
            CreateHeader();
            CreateSidebar();
            CreateMainContent();
        }

        private void CreateHeader()
        {
            _headerPanel = RTLHelper.CreateGradientPanel(
                Point.Empty, 
                new Size(this.Width, 100), 
                RTLHelper.PrimaryColor, 
                RTLHelper.SecondaryColor, 
                false
            );
            _headerPanel.Dock = DockStyle.Top;
            _headerPanel.Height = 100;

            // عنوان رئيسي مع تأثيرات
            var titleLabel = RTLHelper.CreateStyledLabel(
                "نظام الأرشفة الإلكترونية المتقدم",
                new Point(50, 20),
                new Size(600, 35),
                RTLHelper.ArabicFontHeader,
                Color.White,
                ContentAlignment.MiddleRight
            );

            // عنوان فرعي
            var subtitleLabel = RTLHelper.CreateStyledLabel(
                "إدارة شاملة للوثائق والمراسلات الرسمية مع واجهة حديثة متوافقة مع Windows 7",
                new Point(50, 55),
                new Size(700, 25),
                RTLHelper.ArabicFontLarge,
                Color.FromArgb(200, 255, 255, 255),
                ContentAlignment.MiddleRight
            );

            // معلومات النظام
            var versionLabel = RTLHelper.CreateStyledLabel(
                "الإصدار 2.0 - محسن",
                new Point(this.Width - 200, 25),
                new Size(150, 20),
                RTLHelper.ArabicFont,
                Color.FromArgb(180, 255, 255, 255),
                ContentAlignment.MiddleLeft
            );

            var timeLabel = RTLHelper.CreateStyledLabel(
                DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
                new Point(this.Width - 200, 50),
                new Size(150, 20),
                RTLHelper.ArabicFont,
                Color.FromArgb(180, 255, 255, 255),
                ContentAlignment.MiddleLeft
            );

            _headerPanel.Controls.Add(titleLabel);
            _headerPanel.Controls.Add(subtitleLabel);
            _headerPanel.Controls.Add(versionLabel);
            _headerPanel.Controls.Add(timeLabel);

            this.Controls.Add(_headerPanel);
        }

        private void CreateSidebar()
        {
            _sidebar = new CollapsibleSidebar
            {
                SidebarTitle = "القائمة الرئيسية",
                ExpandedWidth = 300,
                CollapsedWidth = 70
            };

            // إضافة أزرار القائمة
            _sidebar.AddMenuButton("إدارة الأقسام", "📁", RTLHelper.AccentColor, (s, e) => OpenDepartmentsForm());
            _sidebar.AddMenuButton("إدارة الوثائق", "📄", RTLHelper.InfoColor, (s, e) => OpenDocumentsForm());
            _sidebar.AddMenuButton("البحث المتقدم", "🔍", RTLHelper.WarningColor, (s, e) => OpenSearchForm());
            _sidebar.AddMenuButton("التقارير", "📊", RTLHelper.SuccessColor, (s, e) => OpenReportsForm());
            _sidebar.AddMenuButton("الإعدادات", "⚙️", RTLHelper.DarkColor, (s, e) => OpenSettingsForm());
            _sidebar.AddMenuButton("حول البرنامج", "ℹ️", RTLHelper.DangerColor, (s, e) => ShowAboutDialog());

            // ربط حدث تغيير حالة الطي
            _sidebar.CollapsedChanged += Sidebar_CollapsedChanged;

            this.Controls.Add(_sidebar);
        }

        private void CreateMainContent()
        {
            _mainContentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(30),
                RightToLeft = RightToLeft.Yes
            };

            CreateWelcomeSection();
            CreateStatisticsSection();
            CreateQuickActionsSection();

            this.Controls.Add(_mainContentPanel);
        }

        private void CreateWelcomeSection()
        {
            var welcomePanel = new Panel
            {
                Location = new Point(30, 30),
                Size = new Size(_mainContentPanel.Width - 60, 100),
                BackColor = Color.Transparent,
                RightToLeft = RightToLeft.Yes
            };

            var welcomeLabel = RTLHelper.CreateStyledLabel(
                "مرحباً بك في نظام الأرشفة الإلكترونية المحسن",
                new Point(0, 10),
                new Size(600, 40),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            var descLabel = RTLHelper.CreateStyledLabel(
                "نظام متكامل ومحسن لإدارة الوثائق مع واجهة حديثة وشريط جانبي قابل للطي",
                new Point(0, 55),
                new Size(700, 30),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            welcomePanel.Controls.Add(welcomeLabel);
            welcomePanel.Controls.Add(descLabel);
            _mainContentPanel.Controls.Add(welcomePanel);
        }

        private void CreateStatisticsSection()
        {
            _statisticsPanel = new Panel
            {
                Location = new Point(30, 150),
                Size = new Size(_mainContentPanel.Width - 60, 150),
                BackColor = Color.Transparent,
                RightToLeft = RightToLeft.Yes
            };

            var statsTitle = RTLHelper.CreateStyledLabel(
                "إحصائيات النظام",
                new Point(0, 0),
                new Size(200, 30),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            _statisticsPanel.Controls.Add(statsTitle);
            RefreshStatistics();
            _mainContentPanel.Controls.Add(_statisticsPanel);
        }

        private void CreateQuickActionsSection()
        {
            _quickActionsPanel = new Panel
            {
                Location = new Point(30, 320),
                Size = new Size(_mainContentPanel.Width - 60, 200),
                BackColor = Color.Transparent,
                RightToLeft = RightToLeft.Yes
            };

            var actionsTitle = RTLHelper.CreateStyledLabel(
                "الإجراءات السريعة",
                new Point(0, 0),
                new Size(200, 30),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            // أزرار الإجراءات السريعة
            var addDocButton = RTLHelper.CreateModernButton(
                "📄 إضافة وثيقة جديدة",
                new Point(0, 40),
                new Size(250, 60),
                RTLHelper.SuccessColor,
                (s, e) => OpenDocumentsForm()
            );

            var viewDocsButton = RTLHelper.CreateModernButton(
                "📋 عرض جميع الوثائق",
                new Point(270, 40),
                new Size(250, 60),
                RTLHelper.AccentColor,
                (s, e) => OpenDocumentsForm()
            );

            var manageDepsButton = RTLHelper.CreateModernButton(
                "📁 إدارة الأقسام",
                new Point(540, 40),
                new Size(250, 60),
                RTLHelper.InfoColor,
                (s, e) => OpenDepartmentsForm()
            );

            var searchButton = RTLHelper.CreateModernButton(
                "🔍 البحث السريع",
                new Point(0, 120),
                new Size(250, 60),
                RTLHelper.WarningColor,
                (s, e) => OpenSearchForm()
            );

            var reportsButton = RTLHelper.CreateModernButton(
                "📊 التقارير والإحصائيات",
                new Point(270, 120),
                new Size(250, 60),
                RTLHelper.DangerColor,
                (s, e) => OpenReportsForm()
            );

            var settingsButton = RTLHelper.CreateModernButton(
                "⚙️ إعدادات النظام",
                new Point(540, 120),
                new Size(250, 60),
                RTLHelper.DarkColor,
                (s, e) => OpenSettingsForm()
            );

            _quickActionsPanel.Controls.Add(actionsTitle);
            _quickActionsPanel.Controls.Add(addDocButton);
            _quickActionsPanel.Controls.Add(viewDocsButton);
            _quickActionsPanel.Controls.Add(manageDepsButton);
            _quickActionsPanel.Controls.Add(searchButton);
            _quickActionsPanel.Controls.Add(reportsButton);
            _quickActionsPanel.Controls.Add(settingsButton);

            _mainContentPanel.Controls.Add(_quickActionsPanel);
        }
        #endregion

        #region Event Handlers
        private void Sidebar_CollapsedChanged(object sender, bool isCollapsed)
        {
            // تحديث تخطيط المحتوى الرئيسي عند تغيير حالة الشريط الجانبي
            _mainContentPanel.Invalidate();
        }
        #endregion

        #region Statistics Methods
        private void RefreshStatistics()
        {
            try
            {
                // مسح البطاقات الموجودة
                var existingCards = new Control[_statisticsPanel.Controls.Count];
                _statisticsPanel.Controls.CopyTo(existingCards, 0);

                foreach (Control control in existingCards)
                {
                    if (control.Tag?.ToString() == "StatCard")
                    {
                        _statisticsPanel.Controls.Remove(control);
                        control.Dispose();
                    }
                }

                // الحصول على الإحصائيات الحقيقية
                var stats = GetRealStatistics();

                var statisticsData = new[]
                {
                    new { Title = "عدد الأقسام", Value = stats.DepartmentsCount.ToString(), Icon = "📁", Color = RTLHelper.AccentColor },
                    new { Title = "عدد الأضابير", Value = stats.FoldersCount.ToString(), Icon = "📂", Color = RTLHelper.SuccessColor },
                    new { Title = "الكتب الصادرة", Value = stats.OutgoingDocuments.ToString(), Icon = "📤", Color = RTLHelper.DangerColor },
                    new { Title = "الكتب الواردة", Value = stats.IncomingDocuments.ToString(), Icon = "📥", Color = RTLHelper.InfoColor }
                };

                int cardWidth = 220;
                int cardHeight = 100;
                int startX = 0;
                int startY = 40;
                int spacing = 240;

                for (int i = 0; i < statisticsData.Length; i++)
                {
                    var stat = statisticsData[i];
                    var card = RTLHelper.CreateModernStatCard(
                        stat.Title,
                        stat.Value,
                        stat.Icon,
                        stat.Color,
                        new Size(cardWidth, cardHeight)
                    );

                    card.Location = new Point(startX + (i * spacing), startY);
                    card.Tag = "StatCard";
                    _statisticsPanel.Controls.Add(card);
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، عرض إحصائيات افتراضية
                ShowDefaultStatistics();
            }
        }

        private void ShowDefaultStatistics()
        {
            var defaultStats = new[]
            {
                new { Title = "عدد الأقسام", Value = "0", Icon = "📁", Color = RTLHelper.AccentColor },
                new { Title = "عدد الأضابير", Value = "0", Icon = "📂", Color = RTLHelper.SuccessColor },
                new { Title = "الكتب الصادرة", Value = "0", Icon = "📤", Color = RTLHelper.DangerColor },
                new { Title = "الكتب الواردة", Value = "0", Icon = "📥", Color = RTLHelper.InfoColor }
            };

            int cardWidth = 220;
            int cardHeight = 100;
            int startX = 0;
            int startY = 40;
            int spacing = 240;

            for (int i = 0; i < defaultStats.Length; i++)
            {
                var stat = defaultStats[i];
                var card = RTLHelper.CreateModernStatCard(
                    stat.Title,
                    stat.Value,
                    stat.Icon,
                    stat.Color,
                    new Size(cardWidth, cardHeight)
                );

                card.Location = new Point(startX + (i * spacing), startY);
                card.Tag = "StatCard";
                _statisticsPanel.Controls.Add(card);
            }
        }

        private StatisticsData GetRealStatistics()
        {
            try
            {
                int departmentsCount = _departmentRepo.GetTotalCount();
                int foldersCount = _dataManager.GetFoldersCount();
                int outgoingDocs = _dataManager.GetDocumentsCount("صادر");
                int incomingDocs = _dataManager.GetDocumentsCount("وارد");

                return new StatisticsData
                {
                    DepartmentsCount = departmentsCount,
                    FoldersCount = foldersCount,
                    OutgoingDocuments = outgoingDocs,
                    IncomingDocuments = incomingDocs
                };
            }
            catch
            {
                return new StatisticsData { DepartmentsCount = 0, FoldersCount = 0, OutgoingDocuments = 0, IncomingDocuments = 0 };
            }
        }
        #endregion

        #region Navigation Methods
        private void OpenDepartmentsForm()
        {
            try
            {
                var departmentsForm = new SimpleDepartmentsForm();
                departmentsForm.ShowDialog();
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenDocumentsForm()
        {
            try
            {
                var documentsForm = new SimpleDocumentsForm();
                documentsForm.ShowDialog();
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSearchForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة البحث المتقدم قريباً\nهذه الميزة قيد التطوير", "البحث المتقدم",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OpenReportsForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة التقارير قريباً\nهذه الميزة قيد التطوير", "التقارير",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OpenSettingsForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة الإعدادات قريباً\nهذه الميزة قيد التطوير", "إعدادات النظام",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAboutDialog()
        {
            var aboutMessage = "نظام الأرشفة الإلكترونية المحسن - PIKA Enhanced\n\n" +
                              "الإصدار: 2.0\n" +
                              "متوافق مع: Windows 7 وما بعده\n" +
                              "المطور: فريق PIKA\n\n" +
                              "المميزات الجديدة:\n" +
                              "• شريط جانبي قابل للطي\n" +
                              "• تصميم حديث ومتجاوب\n" +
                              "• تحسينات في الأداء\n" +
                              "• توافق محسن مع Windows 7\n" +
                              "• تأثيرات بصرية متقدمة";

            RTLHelper.ShowRTLMessageBox(aboutMessage, "حول البرنامج",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        #endregion

        #region Utility Methods
        private void SetApplicationIcon()
        {
            try
            {
                if (File.Exists("AppIcon.ico"))
                {
                    this.Icon = new Icon("AppIcon.ico");
                }
                else
                {
                    IconManager.SetFormIcon(this, "MainApp.ico");
                }
            }
            catch
            {
                // تجاهل أخطاء الأيقونة
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);

            // تحديث أحجام اللوحات عند تغيير حجم النافذة
            if (_mainContentPanel != null)
            {
                _mainContentPanel.Invalidate();
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // تنظيف الموارد
            _statisticsUpdateTimer?.Stop();
            _statisticsUpdateTimer?.Dispose();

            base.OnFormClosing(e);
        }
        #endregion

        #region Component Designer
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 900);
            this.Name = "EnhancedMainForm";
            this.ResumeLayout(false);
        }
        #endregion
    }

    /// <summary>
    /// بيانات الإحصائيات
    /// </summary>
    public class StatisticsData
    {
        public int DepartmentsCount { get; set; }
        public int FoldersCount { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
    }
}
