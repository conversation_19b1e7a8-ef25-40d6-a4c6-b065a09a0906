using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة عرض الوثائق
    /// </summary>
    public partial class ViewDocumentsForm : Form
    {
        private readonly DocumentRepository _documentRepository;
        private readonly DepartmentRepository _departmentRepository;
        private readonly FolderRepository _folderRepository;

        private ComboBox _yearCombo;
        private ComboBox _documentTypeCombo;
        private ComboBox _departmentCombo;
        private ComboBox _folderCombo;
        private TextBox _searchText;
        private DataGridView _documentsGrid;
        private Label _totalLabel;

        public ViewDocumentsForm()
        {
            InitializeComponent();
            InitializeServices();
            InitializeControls();
            LoadInitialData();
            LoadDocuments();
        }

        private void InitializeServices()
        {
            _documentRepository = new DocumentRepository();
            _departmentRepository = new DepartmentRepository();
            _folderRepository = new FolderRepository();
        }

        private void InitializeControls()
        {
            this.Text = "عرض الوثائق";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 242, 247);

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "عرض وإدارة الوثائق",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(50, 30),
                Size = new Size(300, 40),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // شريط البحث والتصفية
            CreateFilterPanel();

            // جدول الوثائق
            CreateDocumentsGrid();

            // شريط المعلومات
            CreateStatusBar();
        }

        private void CreateFilterPanel()
        {
            var filterPanel = new Panel
            {
                Location = new Point(50, 80),
                Size = new Size(1100, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // السنة
            var yearLabel = new Label
            {
                Text = "السنة:",
                Location = new Point(1020, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            filterPanel.Controls.Add(yearLabel);

            _yearCombo = new ComboBox
            {
                Location = new Point(900, 15),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _yearCombo.SelectedIndexChanged += FilterChanged;
            filterPanel.Controls.Add(_yearCombo);

            // نوع الكتاب
            var typeLabel = new Label
            {
                Text = "النوع:",
                Location = new Point(820, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            filterPanel.Controls.Add(typeLabel);

            _documentTypeCombo = new ComboBox
            {
                Location = new Point(700, 15),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _documentTypeCombo.Items.AddRange(new[] { "الكل", "صادر", "وارد" });
            _documentTypeCombo.SelectedIndex = 0;
            _documentTypeCombo.SelectedIndexChanged += FilterChanged;
            filterPanel.Controls.Add(_documentTypeCombo);

            // القسم
            var deptLabel = new Label
            {
                Text = "القسم:",
                Location = new Point(620, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            filterPanel.Controls.Add(deptLabel);

            _departmentCombo = new ComboBox
            {
                Location = new Point(450, 15),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _departmentCombo.SelectedIndexChanged += DepartmentCombo_SelectedIndexChanged;
            filterPanel.Controls.Add(_departmentCombo);

            // الأضبارة
            var folderLabel = new Label
            {
                Text = "الأضبارة:",
                Location = new Point(370, 15),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            filterPanel.Controls.Add(folderLabel);

            _folderCombo = new ComboBox
            {
                Location = new Point(200, 15),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _folderCombo.SelectedIndexChanged += FilterChanged;
            filterPanel.Controls.Add(_folderCombo);

            // البحث النصي
            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(1020, 50),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            filterPanel.Controls.Add(searchLabel);

            _searchText = new TextBox
            {
                Location = new Point(700, 50),
                Size = new Size(300, 25),
                Font = new Font("Tahoma", 10F)
            };
            _searchText.TextChanged += SearchText_TextChanged;
            filterPanel.Controls.Add(_searchText);

            // زر البحث
            var searchButton = new Button
            {
                Text = "🔍 بحث",
                Location = new Point(600, 50),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            searchButton.Click += (s, e) => LoadDocuments();
            filterPanel.Controls.Add(searchButton);

            // زر إعادة تعيين
            var resetButton = new Button
            {
                Text = "إعادة تعيين",
                Location = new Point(500, 50),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            resetButton.Click += ResetButton_Click;
            filterPanel.Controls.Add(resetButton);

            this.Controls.Add(filterPanel);
        }

        private void CreateDocumentsGrid()
        {
            _documentsGrid = new DataGridView
            {
                Location = new Point(50, 180),
                Size = new Size(1100, 500),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                RightToLeft = RightToLeft.Yes,
                Font = new Font("Tahoma", 9F),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // إضافة أعمدة الإجراءات
            var viewColumn = new DataGridViewButtonColumn
            {
                Name = "ViewColumn",
                HeaderText = "عرض",
                Text = "عرض المرفقات",
                UseColumnTextForButtonValue = true,
                Width = 100
            };
            _documentsGrid.Columns.Add(viewColumn);

            var editColumn = new DataGridViewButtonColumn
            {
                Name = "EditColumn",
                HeaderText = "تعديل",
                Text = "تعديل",
                UseColumnTextForButtonValue = true,
                Width = 80
            };
            _documentsGrid.Columns.Add(editColumn);

            var deleteColumn = new DataGridViewButtonColumn
            {
                Name = "DeleteColumn",
                HeaderText = "حذف",
                Text = "حذف",
                UseColumnTextForButtonValue = true,
                Width = 80
            };
            _documentsGrid.Columns.Add(deleteColumn);

            _documentsGrid.CellClick += DocumentsGrid_CellClick;
            _documentsGrid.DoubleClick += DocumentsGrid_DoubleClick;

            this.Controls.Add(_documentsGrid);
        }

        private void CreateStatusBar()
        {
            _totalLabel = new Label
            {
                Location = new Point(50, 700),
                Size = new Size(300, 25),
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(_totalLabel);
        }

        private void LoadInitialData()
        {
            // تحميل السنوات
            LoadYears();

            // تحميل الأقسام
            LoadDepartments();
        }

        private void LoadYears()
        {
            _yearCombo.Items.Clear();
            _yearCombo.Items.Add("الكل");

            // إضافة السنوات من 2020 إلى السنة الحالية + 2
            int currentYear = DateTime.Now.Year;
            for (int year = 2020; year <= currentYear + 2; year++)
            {
                _yearCombo.Items.Add(year.ToString());
            }

            _yearCombo.SelectedIndex = 0;
        }

        private void LoadDepartments()
        {
            var departments = _departmentRepository.GetAll();
            departments.Insert(0, new Department { DepartmentId = 0, DepartmentName = "الكل" });

            _departmentCombo.DataSource = departments;
            _departmentCombo.DisplayMember = "DepartmentName";
            _departmentCombo.ValueMember = "DepartmentId";
        }

        private void DepartmentCombo_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadFolders();
            LoadDocuments();
        }

        private void LoadFolders()
        {
            _folderCombo.DataSource = null;
            _folderCombo.Items.Clear();

            if (_departmentCombo.SelectedValue != null)
            {
                int departmentId = (int)_departmentCombo.SelectedValue;
                
                if (departmentId == 0)
                {
                    var allFolders = _folderRepository.GetAll();
                    allFolders.Insert(0, new Folder { FolderId = 0, FolderName = "الكل" });
                    _folderCombo.DataSource = allFolders;
                }
                else
                {
                    var folders = _folderRepository.GetByDepartmentId(departmentId);
                    folders.Insert(0, new Folder { FolderId = 0, FolderName = "الكل" });
                    _folderCombo.DataSource = folders;
                }

                _folderCombo.DisplayMember = "FolderName";
                _folderCombo.ValueMember = "FolderId";
            }
        }

        private void FilterChanged(object sender, EventArgs e)
        {
            LoadDocuments();
        }

        private void SearchText_TextChanged(object sender, EventArgs e)
        {
            // تأخير البحث لتحسين الأداء
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
                _searchTimer.Dispose();
            }

            _searchTimer = new Timer();
            _searchTimer.Interval = 500; // نصف ثانية
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                LoadDocuments();
            };
            _searchTimer.Start();
        }

        private Timer _searchTimer;

        private void ResetButton_Click(object sender, EventArgs e)
        {
            _yearCombo.SelectedIndex = 0;
            _documentTypeCombo.SelectedIndex = 0;
            _departmentCombo.SelectedIndex = 0;
            _searchText.Clear();
            LoadDocuments();
        }

        private void LoadDocuments()
        {
            try
            {
                // تحديد معايير البحث
                string searchText = _searchText.Text.Trim();
                int? departmentId = null;
                int? folderId = null;
                string documentType = null;
                int? year = null;

                if (_departmentCombo.SelectedValue != null && (int)_departmentCombo.SelectedValue > 0)
                    departmentId = (int)_departmentCombo.SelectedValue;

                if (_folderCombo.SelectedValue != null && (int)_folderCombo.SelectedValue > 0)
                    folderId = (int)_folderCombo.SelectedValue;

                if (_documentTypeCombo.SelectedIndex > 0)
                    documentType = _documentTypeCombo.SelectedItem.ToString();

                if (_yearCombo.SelectedIndex > 0)
                    year = int.Parse(_yearCombo.SelectedItem.ToString());

                // البحث في الوثائق
                var documents = _documentRepository.Search(searchText, departmentId, folderId, documentType, year);

                // تحويل البيانات للعرض
                var displayData = documents.Select(d => new
                {
                    DocumentId = d.DocumentId,
                    تسلسل_الكتاب = d.DocumentSequence,
                    تاريخ_الإدخال = d.EntryDate.ToString("yyyy/MM/dd"),
                    نوع_الكتاب = d.DocumentType,
                    تاريخ_الكتاب = d.DocumentDate.ToString("yyyy/MM/dd"),
                    رقم_الكتاب = d.DocumentNumber,
                    الموضوع = d.Subject.Length > 50 ? d.Subject.Substring(0, 50) + "..." : d.Subject,
                    القسم = d.Department?.DepartmentName,
                    الأضبارة = d.Folder?.FolderName,
                    عدد_المرفقات = d.AttachmentsCount
                }).ToList();

                _documentsGrid.DataSource = displayData;

                // إخفاء عمود المعرف
                if (_documentsGrid.Columns["DocumentId"] != null)
                    _documentsGrid.Columns["DocumentId"].Visible = false;

                // تحديث عداد النتائج
                _totalLabel.Text = "إجمالي الوثائق: " + documents.Count;
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DocumentsGrid_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var grid = sender as DataGridView;
                int documentId = Convert.ToInt32(grid.Rows[e.RowIndex].Cells["DocumentId"].Value);

                if (e.ColumnIndex == grid.Columns["ViewColumn"].Index)
                {
                    // عرض المرفقات
                    ShowDocumentAttachments(documentId);
                }
                else if (e.ColumnIndex == grid.Columns["EditColumn"].Index)
                {
                    // تعديل الوثيقة
                    EditDocument(documentId);
                }
                else if (e.ColumnIndex == grid.Columns["DeleteColumn"].Index)
                {
                    // حذف الوثيقة
                    DeleteDocument(documentId);
                }
            }
        }

        private void DocumentsGrid_DoubleClick(object sender, EventArgs e)
        {
            if (_documentsGrid.SelectedRows.Count > 0)
            {
                int documentId = Convert.ToInt32(_documentsGrid.SelectedRows[0].Cells["DocumentId"].Value);
                ShowDocumentDetails(documentId);
            }
        }

        private void ShowDocumentAttachments(int documentId)
        {
            try
            {
                var document = _documentRepository.GetById(documentId);
                if (document != null)
                {
                    var attachmentsForm = new DocumentAttachmentsForm(document);
                    attachmentsForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في عرض المرفقات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditDocument(int documentId)
        {
            try
            {
                var document = _documentRepository.GetById(documentId);
                if (document != null)
                {
                    var editForm = new EditDocumentForm(document);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadDocuments(); // إعادة تحميل البيانات
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تعديل الوثيقة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteDocument(int documentId)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذه الوثيقة؟\nسيتم حذف جميع المرفقات المرتبطة بها.",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // حذف الوثيقة والمرفقات
                    var attachmentRepository = new AttachmentRepository();
                    attachmentRepository.DeleteByDocumentId(documentId);

                    if (_documentRepository.Delete(documentId))
                    {
                        MessageBox.Show("تم حذف الوثيقة بنجاح", "تم الحذف",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadDocuments(); // إعادة تحميل البيانات
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الوثيقة", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حذف الوثيقة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowDocumentDetails(int documentId)
        {
            try
            {
                var document = _documentRepository.GetById(documentId);
                if (document != null)
                {
                    var detailsForm = new DocumentDetailsForm(document);
                    detailsForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في عرض تفاصيل الوثيقة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _searchTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
