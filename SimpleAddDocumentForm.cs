using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// نافذة إضافة وثيقة جديدة
    /// </summary>
    public partial class SimpleAddDocumentForm : Form
    {
        #region Fields
        private TextBox _documentNumberTextBox;
        private TextBox _subjectTextBox;
        private ComboBox _typeComboBox;
        private ComboBox _departmentComboBox;
        private DateTimePicker _documentDatePicker;
        private DateTimePicker _receivedDatePicker;
        private TextBox _senderReceiverTextBox;
        private TextBox _notesTextBox;
        private Button _saveButton;
        private Button _cancelButton;
        private CheckBox _receivedDateCheckBox;
        
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;
        #endregion

        #region Constructor
        public SimpleAddDocumentForm()
        {
            InitializeComponent();
            InitializeData();
            SetupForm();
            CreateControls();
            LoadDepartments();

            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Add.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Initialization
        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        private void SetupForm()
        {
            this.Text = "📄 إضافة وثيقة جديدة";
            this.Size = new Size(600, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
        #endregion

        #region UI Creation
        private void CreateControls()
        {
            CreateHeader();
            CreateFormFields();
            CreateButtons();
        }

        private void CreateHeader()
        {
            var headerPanel = new Panel();
            headerPanel.Dock = DockStyle.Top;
            headerPanel.Height = 80;
            headerPanel.BackColor = RTLHelper.PrimaryColor;

            var titleLabel = RTLHelper.CreateStyledLabel(
                "إضافة وثيقة جديدة",
                new Point(20, 20),
                new Size(400, 30),
                RTLHelper.ArabicFontHeader,
                Color.White,
                ContentAlignment.MiddleRight
            );

            var subtitleLabel = RTLHelper.CreateStyledLabel(
                "يرجى ملء جميع الحقول المطلوبة لإضافة الوثيقة",
                new Point(20, 50),
                new Size(500, 20),
                RTLHelper.ArabicFont,
                Color.FromArgb(200, 255, 255, 255),
                ContentAlignment.MiddleRight
            );

            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(subtitleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreateFormFields()
        {
            int labelWidth = 120;
            int fieldWidth = 400;
            int fieldHeight = 25;
            int startX = 50;
            int startY = 100;
            int spacing = 45;
            int currentY = startY;

            // رقم الكتاب
            var docNumberLabel = RTLHelper.CreateStyledLabel(
                "رقم الكتاب *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _documentNumberTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(docNumberLabel);
            this.Controls.Add(_documentNumberTextBox);
            currentY += spacing;

            // الموضوع
            var subjectLabel = RTLHelper.CreateStyledLabel(
                "الموضوع *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _subjectTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(subjectLabel);
            this.Controls.Add(_subjectTextBox);
            currentY += spacing;

            // نوع الوثيقة
            var typeLabel = RTLHelper.CreateStyledLabel(
                "نوع الوثيقة *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _typeComboBox = RTLHelper.CreateStyledComboBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                new string[] { "صادر", "وارد" }
            );

            this.Controls.Add(typeLabel);
            this.Controls.Add(_typeComboBox);
            currentY += spacing;

            // القسم
            var departmentLabel = RTLHelper.CreateStyledLabel(
                "القسم *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _departmentComboBox = RTLHelper.CreateStyledComboBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                null
            );

            this.Controls.Add(departmentLabel);
            this.Controls.Add(_departmentComboBox);
            currentY += spacing;

            // تاريخ الوثيقة
            var docDateLabel = RTLHelper.CreateStyledLabel(
                "تاريخ الوثيقة *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _documentDatePicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight)
            );

            this.Controls.Add(docDateLabel);
            this.Controls.Add(_documentDatePicker);
            currentY += spacing;

            // تاريخ الاستلام (اختياري)
            _receivedDateCheckBox = RTLHelper.CreateStyledCheckBox(
                "تاريخ الاستلام",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                false
            );

            _receivedDatePicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight)
            );
            _receivedDatePicker.Enabled = false;

            _receivedDateCheckBox.CheckedChanged += (s, e) =>
            {
                _receivedDatePicker.Enabled = _receivedDateCheckBox.Checked;
            };

            this.Controls.Add(_receivedDateCheckBox);
            this.Controls.Add(_receivedDatePicker);
            currentY += spacing;

            // المرسل/المستقبل
            var senderLabel = RTLHelper.CreateStyledLabel(
                "المرسل/المستقبل",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _senderReceiverTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(senderLabel);
            this.Controls.Add(_senderReceiverTextBox);
            currentY += spacing;

            // الملاحظات
            var notesLabel = RTLHelper.CreateStyledLabel(
                "الملاحظات",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _notesTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, 80),
                true
            );

            this.Controls.Add(notesLabel);
            this.Controls.Add(_notesTextBox);
        }

        private void CreateButtons()
        {
            _saveButton = RTLHelper.CreateStyledButton(
                "💾 حفظ الوثيقة",
                new Point(350, 550),
                new Size(120, 45),
                RTLHelper.SuccessColor,
                SaveButton_Click
            );

            _cancelButton = RTLHelper.CreateStyledButton(
                "❌ إلغاء",
                new Point(220, 550),
                new Size(120, 45),
                RTLHelper.DangerColor,
                delegate { this.DialogResult = DialogResult.Cancel; }
            );

            RTLHelper.ApplyButtonEffects(_saveButton);
            RTLHelper.ApplyButtonEffects(_cancelButton);

            this.Controls.Add(_saveButton);
            this.Controls.Add(_cancelButton);
        }
        #endregion

        #region Data Loading
        private void LoadDepartments()
        {
            try
            {
                var departments = _departmentRepo.GetAll();
                _departmentComboBox.Items.Clear();

                foreach (var dept in departments)
                {
                    _departmentComboBox.Items.Add(new ComboBoxItem
                    {
                        Text = dept.DepartmentName,
                        Value = dept.DepartmentId
                    });
                }

                if (_departmentComboBox.Items.Count > 0)
                {
                    _departmentComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Event Handlers
        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    var document = new DocumentSimple
                    {
                        DocumentNumber = _documentNumberTextBox.Text.Trim(),
                        Subject = _subjectTextBox.Text.Trim(),
                        DocumentType = _typeComboBox.SelectedItem.ToString(),
                        DepartmentId = ((ComboBoxItem)_departmentComboBox.SelectedItem).Value,
                        DepartmentName = ((ComboBoxItem)_departmentComboBox.SelectedItem).Text,
                        DocumentDate = _documentDatePicker.Value,
                        ReceivedDate = _receivedDateCheckBox.Checked ? (DateTime?)_receivedDatePicker.Value : null,
                        SenderReceiver = _senderReceiverTextBox.Text.Trim(),
                        Notes = _notesTextBox.Text.Trim(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = "المستخدم الحالي",
                        IsActive = true
                    };

                    if (_dataManager.AddDocument(document))
                    {
                        this.DialogResult = DialogResult.OK;
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في حفظ الوثيقة", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في حفظ الوثيقة: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private bool ValidateInput()
        {
            // التحقق من رقم الكتاب
            if (string.IsNullOrEmpty(_documentNumberTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال رقم الكتاب", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberTextBox.Focus();
                return false;
            }

            // التحقق من تكرار رقم الكتاب
            if (_dataManager.IsDocumentNumberExists(_documentNumberTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("رقم الكتاب موجود مسبقاً. يرجى إدخال رقم مختلف", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberTextBox.Focus();
                return false;
            }

            // التحقق من الموضوع
            if (string.IsNullOrEmpty(_subjectTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال موضوع الوثيقة", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _subjectTextBox.Focus();
                return false;
            }

            // التحقق من نوع الوثيقة
            if (_typeComboBox.SelectedIndex == -1)
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار نوع الوثيقة", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _typeComboBox.Focus();
                return false;
            }

            // التحقق من القسم
            if (_departmentComboBox.SelectedIndex == -1)
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار القسم", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _departmentComboBox.Focus();
                return false;
            }

            return true;
        }
        #endregion

        #region Helper Classes
        public class ComboBoxItem
        {
            public string Text { get; set; }
            public int Value { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
        #endregion

        #region Component Designer
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 650);
            this.Name = "SimpleAddDocumentForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
