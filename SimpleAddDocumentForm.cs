using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// نافذة إضافة وثيقة جديدة
    /// </summary>
    public partial class SimpleAddDocumentForm : Form
    {
        #region Fields
        private TextBox _documentNumberTextBox;
        private TextBox _subjectTextBox;
        private ComboBox _typeComboBox;
        private ComboBox _departmentComboBox;
        private DateTimePicker _documentDatePicker;
        private DateTimePicker _receivedDatePicker;
        private TextBox _folderNumberTextBox;
        private Label _senderReceiverLabel;
        private TextBox _senderReceiverTextBox;
        private TextBox _notesTextBox;
        private Button _saveButton;
        private Button _cancelButton;
        private CheckBox _receivedDateCheckBox;
        private GroupBox _attachmentsGroupBox;
        private Button _addFileButton;
        private DataGridView _attachmentsDataGridView;
        private Button _previewButton;
        private Button _deleteFileButton;
        private Label _attachmentsSummaryLabel;
        private ProgressBar _fileProgressBar;
        
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;
        #endregion

        #region Constructor
        public SimpleAddDocumentForm()
        {
            InitializeComponent();
            InitializeData();
            SetupForm();
            CreateControls();
            LoadDepartments();

            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Add.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Initialization
        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        private void SetupForm()
        {
            this.Text = "📄 إضافة وثيقة جديدة";
            this.Size = new Size(700, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
        #endregion

        #region UI Creation
        private void CreateControls()
        {
            CreateHeader();
            CreateFormFields();
            CreateButtons();
        }

        private void CreateHeader()
        {
            var headerPanel = new Panel();
            headerPanel.Dock = DockStyle.Top;
            headerPanel.Height = 80;
            headerPanel.BackColor = RTLHelper.PrimaryColor;

            var titleLabel = RTLHelper.CreateStyledLabel(
                "إضافة وثيقة جديدة",
                new Point(20, 20),
                new Size(400, 30),
                RTLHelper.ArabicFontHeader,
                Color.White,
                ContentAlignment.MiddleRight
            );

            var subtitleLabel = RTLHelper.CreateStyledLabel(
                "يرجى ملء جميع الحقول المطلوبة لإضافة الوثيقة",
                new Point(20, 50),
                new Size(500, 20),
                RTLHelper.ArabicFont,
                Color.FromArgb(200, 255, 255, 255),
                ContentAlignment.MiddleRight
            );

            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(subtitleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreateFormFields()
        {
            int labelWidth = 120;
            int fieldWidth = 400;
            int fieldHeight = 25;
            int startX = 50;
            int startY = 100;
            int spacing = 45;
            int currentY = startY;

            // رقم الكتاب
            var docNumberLabel = RTLHelper.CreateStyledLabel(
                "رقم الكتاب *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _documentNumberTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(docNumberLabel);
            this.Controls.Add(_documentNumberTextBox);
            currentY += spacing;

            // الموضوع
            var subjectLabel = RTLHelper.CreateStyledLabel(
                "الموضوع *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _subjectTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(subjectLabel);
            this.Controls.Add(_subjectTextBox);
            currentY += spacing;

            // نوع الوثيقة
            var typeLabel = RTLHelper.CreateStyledLabel(
                "نوع الوثيقة *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _typeComboBox = RTLHelper.CreateStyledComboBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                new string[] { "صادر", "وارد" }
            );

            // ربط حدث تغيير النوع
            _typeComboBox.SelectedIndexChanged += TypeComboBox_SelectedIndexChanged;

            this.Controls.Add(typeLabel);
            this.Controls.Add(_typeComboBox);
            currentY += spacing;

            // القسم
            var departmentLabel = RTLHelper.CreateStyledLabel(
                "القسم *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _departmentComboBox = RTLHelper.CreateStyledComboBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                null
            );

            this.Controls.Add(departmentLabel);
            this.Controls.Add(_departmentComboBox);
            currentY += spacing;

            // رقم الأضبارة
            var folderNumberLabel = RTLHelper.CreateStyledLabel(
                "رقم الأضبارة *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _folderNumberTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(folderNumberLabel);
            this.Controls.Add(_folderNumberTextBox);
            currentY += spacing;

            // تاريخ الوثيقة
            var docDateLabel = RTLHelper.CreateStyledLabel(
                "تاريخ الوثيقة *",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _documentDatePicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight)
            );

            this.Controls.Add(docDateLabel);
            this.Controls.Add(_documentDatePicker);
            currentY += spacing;

            // تاريخ الاستلام (اختياري)
            _receivedDateCheckBox = RTLHelper.CreateStyledCheckBox(
                "تاريخ الاستلام",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                false
            );

            _receivedDatePicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight)
            );
            _receivedDatePicker.Enabled = false;

            _receivedDateCheckBox.CheckedChanged += (s, e) =>
            {
                _receivedDatePicker.Enabled = _receivedDateCheckBox.Checked;
            };

            this.Controls.Add(_receivedDateCheckBox);
            this.Controls.Add(_receivedDatePicker);
            currentY += spacing;

            // المرسل/المستقبل (ديناميكي)
            _senderReceiverLabel = RTLHelper.CreateStyledLabel(
                "المرسل/المستقبل",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _senderReceiverTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(_senderReceiverLabel);
            this.Controls.Add(_senderReceiverTextBox);
            currentY += spacing;

            // الملاحظات
            var notesLabel = RTLHelper.CreateStyledLabel(
                "الملاحظات",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _notesTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, 80),
                true
            );

            this.Controls.Add(notesLabel);
            this.Controls.Add(_notesTextBox);
            currentY += 100;

            // قسم المرفقات
            CreateAttachmentsSection(startX, currentY, fieldWidth + labelWidth + 10);
        }

        private void CreateAttachmentsSection(int startX, int startY, int sectionWidth)
        {
            _attachmentsGroupBox = new GroupBox();
            _attachmentsGroupBox.Text = "المرفقات";
            _attachmentsGroupBox.Location = new Point(startX, startY);
            _attachmentsGroupBox.Size = new Size(sectionWidth, 200);
            _attachmentsGroupBox.Font = RTLHelper.ArabicFontBold;
            _attachmentsGroupBox.RightToLeft = RightToLeft.Yes;

            // زر إضافة ملف
            _addFileButton = RTLHelper.CreateStyledButton(
                "📎 إضافة ملف",
                new Point(10, 25),
                new Size(120, 35),
                RTLHelper.AccentColor,
                AddFileButton_Click
            );

            // أزرار المعاينة والحذف
            _previewButton = RTLHelper.CreateStyledButton(
                "👁️ معاينة",
                new Point(140, 25),
                new Size(100, 35),
                RTLHelper.InfoColor,
                PreviewButton_Click
            );

            _deleteFileButton = RTLHelper.CreateStyledButton(
                "🗑️ حذف",
                new Point(250, 25),
                new Size(100, 35),
                RTLHelper.DangerColor,
                DeleteFileButton_Click
            );

            // جدول المرفقات
            _attachmentsDataGridView = new DataGridView();
            _attachmentsDataGridView.Location = new Point(10, 70);
            _attachmentsDataGridView.Size = new Size(sectionWidth - 20, 100);
            _attachmentsDataGridView.RightToLeft = RightToLeft.Yes;
            _attachmentsDataGridView.AllowUserToAddRows = false;
            _attachmentsDataGridView.AllowUserToDeleteRows = false;
            _attachmentsDataGridView.ReadOnly = true;
            _attachmentsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            _attachmentsDataGridView.MultiSelect = false;

            // إعداد أعمدة الجدول
            _attachmentsDataGridView.Columns.Add("FileName", "اسم الملف");
            _attachmentsDataGridView.Columns.Add("FileSize", "حجم الملف");
            _attachmentsDataGridView.Columns.Add("FileType", "نوع الملف");

            _attachmentsDataGridView.Columns[0].Width = 200;
            _attachmentsDataGridView.Columns[1].Width = 100;
            _attachmentsDataGridView.Columns[2].Width = 100;

            // ملخص المرفقات
            _attachmentsSummaryLabel = RTLHelper.CreateStyledLabel(
                "عدد الملفات: 0 | الحجم الإجمالي: 0 بايت",
                new Point(10, 175),
                new Size(400, 20),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            // شريط التقدم
            _fileProgressBar = new ProgressBar();
            _fileProgressBar.Location = new Point(420, 175);
            _fileProgressBar.Size = new Size(150, 20);
            _fileProgressBar.Visible = false;

            RTLHelper.ApplyButtonEffects(_addFileButton);
            RTLHelper.ApplyButtonEffects(_previewButton);
            RTLHelper.ApplyButtonEffects(_deleteFileButton);

            _attachmentsGroupBox.Controls.Add(_addFileButton);
            _attachmentsGroupBox.Controls.Add(_previewButton);
            _attachmentsGroupBox.Controls.Add(_deleteFileButton);
            _attachmentsGroupBox.Controls.Add(_attachmentsDataGridView);
            _attachmentsGroupBox.Controls.Add(_attachmentsSummaryLabel);
            _attachmentsGroupBox.Controls.Add(_fileProgressBar);

            this.Controls.Add(_attachmentsGroupBox);
        }

        private void CreateButtons()
        {
            _saveButton = RTLHelper.CreateStyledButton(
                "💾 حفظ الوثيقة",
                new Point(450, 720),
                new Size(120, 45),
                RTLHelper.SuccessColor,
                SaveButton_Click
            );

            _cancelButton = RTLHelper.CreateStyledButton(
                "❌ إلغاء",
                new Point(320, 720),
                new Size(120, 45),
                RTLHelper.DangerColor,
                delegate { this.DialogResult = DialogResult.Cancel; }
            );

            RTLHelper.ApplyButtonEffects(_saveButton);
            RTLHelper.ApplyButtonEffects(_cancelButton);

            this.Controls.Add(_saveButton);
            this.Controls.Add(_cancelButton);
        }
        #endregion

        #region Data Loading
        private void LoadDepartments()
        {
            try
            {
                var departments = _departmentRepo.GetAll();
                _departmentComboBox.Items.Clear();

                foreach (var dept in departments)
                {
                    _departmentComboBox.Items.Add(new ComboBoxItem
                    {
                        Text = dept.DepartmentName,
                        Value = dept.DepartmentId
                    });
                }

                if (_departmentComboBox.Items.Count > 0)
                {
                    _departmentComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Event Handlers
        private void TypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_typeComboBox.SelectedItem != null)
            {
                string selectedType = _typeComboBox.SelectedItem.ToString();
                if (selectedType == "صادر")
                {
                    _senderReceiverLabel.Text = "الجهة المستقبلة";
                    _senderReceiverTextBox.PlaceholderText = "اسم الجهة أو المؤسسة المستقبلة";
                }
                else if (selectedType == "وارد")
                {
                    _senderReceiverLabel.Text = "الجهة المرسلة";
                    _senderReceiverTextBox.PlaceholderText = "اسم الجهة أو المؤسسة المرسلة";
                }
            }
        }

        private void AddFileButton_Click(object sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pptx;*.jpg;*.jpeg;*.png;*.gif;*.txt;*.rtf|" +
                                          "ملفات PDF|*.pdf|" +
                                          "مستندات Word|*.doc;*.docx|" +
                                          "جداول Excel|*.xls;*.xlsx|" +
                                          "عروض PowerPoint|*.ppt;*.pptx|" +
                                          "الصور|*.jpg;*.jpeg;*.png;*.gif|" +
                                          "ملفات نصية|*.txt;*.rtf";
                    openFileDialog.Multiselect = true;
                    openFileDialog.Title = "اختيار الملفات المراد إرفاقها";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        foreach (string fileName in openFileDialog.FileNames)
                        {
                            AddFileToAttachments(fileName);
                        }
                        UpdateAttachmentsSummary();
                    }
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في إضافة الملف: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PreviewButton_Click(object sender, EventArgs e)
        {
            if (_attachmentsDataGridView.SelectedRows.Count > 0)
            {
                string fileName = _attachmentsDataGridView.SelectedRows[0].Cells["FileName"].Value.ToString();
                RTLHelper.ShowRTLMessageBox("معاينة الملف: " + fileName + "\n\nهذه الميزة قيد التطوير", "معاينة الملف",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار ملف للمعاينة", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void DeleteFileButton_Click(object sender, EventArgs e)
        {
            if (_attachmentsDataGridView.SelectedRows.Count > 0)
            {
                var result = RTLHelper.ShowRTLMessageBox("هل تريد حذف الملف المحدد؟", "تأكيد الحذف",
                              MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _attachmentsDataGridView.Rows.RemoveAt(_attachmentsDataGridView.SelectedRows[0].Index);
                    UpdateAttachmentsSummary();
                }
            }
            else
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار ملف للحذف", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    var document = new DocumentSimple
                    {
                        DocumentNumber = _documentNumberTextBox.Text.Trim(),
                        Subject = _subjectTextBox.Text.Trim(),
                        DocumentType = _typeComboBox.SelectedItem.ToString(),
                        DepartmentId = ((ComboBoxItem)_departmentComboBox.SelectedItem).Value,
                        DepartmentName = ((ComboBoxItem)_departmentComboBox.SelectedItem).Text,
                        FolderNumber = _folderNumberTextBox.Text.Trim(),
                        DocumentDate = _documentDatePicker.Value,
                        ReceivedDate = _receivedDateCheckBox.Checked ? (DateTime?)_receivedDatePicker.Value : null,
                        SenderReceiver = _senderReceiverTextBox.Text.Trim(),
                        Notes = _notesTextBox.Text.Trim(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = "المستخدم الحالي",
                        IsActive = true
                    };

                    if (_dataManager.AddDocument(document))
                    {
                        this.DialogResult = DialogResult.OK;
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في حفظ الوثيقة", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في حفظ الوثيقة: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        #region Helper Methods
        private void AddFileToAttachments(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);

                // التحقق من حجم الملف (10 MB max)
                if (fileInfo.Length > 10 * 1024 * 1024)
                {
                    RTLHelper.ShowRTLMessageBox("حجم الملف " + fileInfo.Name + " يتجاوز 10 ميجابايت", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // التحقق من الحجم الإجمالي (50 MB max)
                long totalSize = GetTotalAttachmentsSize() + fileInfo.Length;
                if (totalSize > 50 * 1024 * 1024)
                {
                    RTLHelper.ShowRTLMessageBox("الحجم الإجمالي للمرفقات يتجاوز 50 ميجابايت", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // التحقق من نوع الملف
                string extension = fileInfo.Extension.ToLower();
                string[] supportedTypes = { ".pdf", ".doc", ".docx", ".xls", ".xlsx",
                                          ".ppt", ".pptx", ".jpg", ".jpeg", ".png",
                                          ".gif", ".txt", ".rtf" };

                bool isSupported = false;
                foreach (string type in supportedTypes)
                {
                    if (extension == type)
                    {
                        isSupported = true;
                        break;
                    }
                }

                if (!isSupported)
                {
                    RTLHelper.ShowRTLMessageBox("نوع الملف " + extension + " غير مدعوم", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // إضافة الملف إلى الجدول
                string fileSize = FormatFileSize(fileInfo.Length);
                _attachmentsDataGridView.Rows.Add(fileInfo.Name, fileSize, extension);
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في إضافة الملف: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private long GetTotalAttachmentsSize()
        {
            long totalSize = 0;
            foreach (DataGridViewRow row in _attachmentsDataGridView.Rows)
            {
                if (row.Cells["FileSize"].Value != null)
                {
                    string sizeText = row.Cells["FileSize"].Value.ToString();
                    // تحويل النص إلى حجم بالبايت (تقريبي)
                    if (sizeText.Contains("كيلوبايت"))
                    {
                        double kb = double.Parse(sizeText.Replace(" كيلوبايت", ""));
                        totalSize += (long)(kb * 1024);
                    }
                    else if (sizeText.Contains("ميجابايت"))
                    {
                        double mb = double.Parse(sizeText.Replace(" ميجابايت", ""));
                        totalSize += (long)(mb * 1024 * 1024);
                    }
                    else if (sizeText.Contains("بايت"))
                    {
                        totalSize += long.Parse(sizeText.Replace(" بايت", ""));
                    }
                }
            }
            return totalSize;
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024)
                return bytes + " بايت";
            else if (bytes < 1024 * 1024)
                return Math.Round((double)bytes / 1024, 2) + " كيلوبايت";
            else
                return Math.Round((double)bytes / (1024 * 1024), 2) + " ميجابايت";
        }

        private void UpdateAttachmentsSummary()
        {
            int fileCount = _attachmentsDataGridView.Rows.Count;
            long totalSize = GetTotalAttachmentsSize();
            string totalSizeText = FormatFileSize(totalSize);

            _attachmentsSummaryLabel.Text = "عدد الملفات: " + fileCount + " | الحجم الإجمالي: " + totalSizeText;
        }
        #endregion

        #region Validation
        private bool ValidateInput()
        {
            // التحقق من رقم الكتاب
            if (string.IsNullOrEmpty(_documentNumberTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال رقم الكتاب", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberTextBox.Focus();
                return false;
            }

            // التحقق من تكرار رقم الكتاب
            if (_dataManager.IsDocumentNumberExists(_documentNumberTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("رقم الكتاب موجود مسبقاً. يرجى إدخال رقم مختلف", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberTextBox.Focus();
                return false;
            }

            // التحقق من الموضوع
            if (string.IsNullOrEmpty(_subjectTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال موضوع الوثيقة", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _subjectTextBox.Focus();
                return false;
            }

            // التحقق من نوع الوثيقة
            if (_typeComboBox.SelectedIndex == -1)
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار نوع الوثيقة", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _typeComboBox.Focus();
                return false;
            }

            // التحقق من القسم
            if (_departmentComboBox.SelectedIndex == -1)
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار القسم", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _departmentComboBox.Focus();
                return false;
            }

            // التحقق من رقم الأضبارة
            if (string.IsNullOrEmpty(_folderNumberTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال رقم الأضبارة", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderNumberTextBox.Focus();
                return false;
            }

            // التحقق من تكرار رقم الأضبارة في نفس القسم
            int departmentId = ((ComboBoxItem)_departmentComboBox.SelectedItem).Value;
            if (_dataManager.IsFolderNumberExists(_folderNumberTextBox.Text.Trim(), departmentId))
            {
                RTLHelper.ShowRTLMessageBox("رقم الأضبارة موجود مسبقاً في هذا القسم. يرجى إدخال رقم مختلف", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderNumberTextBox.Focus();
                return false;
            }

            return true;
        }
        #endregion

        #region Helper Classes
        public class ComboBoxItem
        {
            public string Text { get; set; }
            public int Value { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
        #endregion

        #region Component Designer
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 650);
            this.Name = "SimpleAddDocumentForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
