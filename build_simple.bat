@echo off
echo Building PIKA Enhanced Application...

C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe PIKA_Enhanced.csproj /p:Configuration=Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build Successful!
    echo Created file: bin\Release\PIKA_Enhanced.exe
    echo.
    echo Features:
    echo - Collapsible right sidebar
    echo - Windows 7 optimizations
    echo - Modern UI design
    echo - Centered content layout
    echo - Add document functionality
    echo.
) else (
    echo.
    echo Build Failed!
    echo Please check the error messages above.
    echo.
)

pause
