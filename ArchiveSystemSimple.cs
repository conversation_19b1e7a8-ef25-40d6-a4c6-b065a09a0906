using System;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using System.Xml;

namespace ArchiveSystem
{
    public partial class MainForm : Form
    {
        private Button departmentsButton;
        private Button documentsButton;
        private Button searchButton;
        private Button settingsButton;
        private Label statsLabel;

        public MainForm()
        {
            InitializeComponent();
            SetupForm();
            CreateControls();
            LoadStatistics();
        }

        private void SetupForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية المتقدم";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.WindowState = FormWindowState.Normal;
        }

        private void CreateControls()
        {
            // إنشاء العنوان
            var titleLabel = new Label();
            titleLabel.Text = "نظام الأرشفة الإلكترونية المتقدم";
            titleLabel.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(50, 30);
            titleLabel.Size = new Size(500, 40);
            titleLabel.TextAlign = ContentAlignment.MiddleRight;
            titleLabel.RightToLeft = RightToLeft.Yes;
            this.Controls.Add(titleLabel);

            // إنشاء الوصف
            var descLabel = new Label();
            descLabel.Text = "نظام متكامل لإدارة الوثائق والمراسلات الرسمية";
            descLabel.Font = new Font("Tahoma", 12F);
            descLabel.ForeColor = Color.FromArgb(127, 140, 141);
            descLabel.Location = new Point(50, 80);
            descLabel.Size = new Size(600, 30);
            descLabel.TextAlign = ContentAlignment.MiddleRight;
            descLabel.RightToLeft = RightToLeft.Yes;
            this.Controls.Add(descLabel);

            // إنشاء الأزرار
            CreateMenuButtons();

            // إنشاء منطقة الإحصائيات
            CreateStatisticsArea();
        }

        private void CreateMenuButtons()
        {
            int buttonWidth = 200;
            int buttonHeight = 50;
            int startX = 50;
            int startY = 150;
            int spacing = 220;

            // زر إدارة الأقسام
            departmentsButton = CreateButton("📁 إدارة الأقسام", 
                new Point(startX, startY), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(52, 152, 219));
            departmentsButton.Click += DepartmentsButton_Click;
            this.Controls.Add(departmentsButton);

            // زر إدارة الوثائق
            documentsButton = CreateButton("📄 إدارة الوثائق", 
                new Point(startX + spacing, startY), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(155, 89, 182));
            documentsButton.Click += DocumentsButton_Click;
            this.Controls.Add(documentsButton);

            // زر البحث
            searchButton = CreateButton("🔍 البحث المتقدم", 
                new Point(startX, startY + 80), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(243, 156, 18));
            searchButton.Click += SearchButton_Click;
            this.Controls.Add(searchButton);

            // زر الإعدادات
            settingsButton = CreateButton("⚙️ الإعدادات", 
                new Point(startX + spacing, startY + 80), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(127, 140, 141));
            settingsButton.Click += SettingsButton_Click;
            this.Controls.Add(settingsButton);
        }

        private Button CreateButton(string text, Point location, Size size, Color backgroundColor)
        {
            var button = new Button();
            button.Text = text;
            button.Location = location;
            button.Size = size;
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleCenter;
            button.Cursor = Cursors.Hand;
            button.RightToLeft = RightToLeft.Yes;
            button.FlatAppearance.BorderSize = 0;

            // إضافة تأثيرات بصرية
            var originalColor = backgroundColor;
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Light(originalColor, 0.2f);
            button.MouseLeave += (s, e) => button.BackColor = originalColor;

            return button;
        }

        private void CreateStatisticsArea()
        {
            var statsPanel = new Panel();
            statsPanel.Location = new Point(50, 320);
            statsPanel.Size = new Size(600, 200);
            statsPanel.BackColor = Color.White;
            statsPanel.BorderStyle = BorderStyle.FixedSingle;
            statsPanel.RightToLeft = RightToLeft.Yes;

            var statsTitle = new Label();
            statsTitle.Text = "إحصائيات النظام";
            statsTitle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            statsTitle.ForeColor = Color.FromArgb(52, 73, 94);
            statsTitle.Location = new Point(20, 20);
            statsTitle.Size = new Size(200, 30);
            statsTitle.TextAlign = ContentAlignment.MiddleRight;
            statsTitle.RightToLeft = RightToLeft.Yes;
            statsPanel.Controls.Add(statsTitle);

            statsLabel = new Label();
            statsLabel.Font = new Font("Tahoma", 11F);
            statsLabel.ForeColor = Color.FromArgb(127, 140, 141);
            statsLabel.Location = new Point(20, 60);
            statsLabel.Size = new Size(550, 120);
            statsLabel.TextAlign = ContentAlignment.TopRight;
            statsLabel.RightToLeft = RightToLeft.Yes;
            statsPanel.Controls.Add(statsLabel);

            this.Controls.Add(statsPanel);
        }

        private void LoadStatistics()
        {
            try
            {
                int departmentsCount = GetDepartmentsCount();
                int documentsCount = GetDocumentsCount();

                statsLabel.Text = string.Format(
                    "عدد الأقسام: {0}\n" +
                    "عدد الوثائق: {1}\n" +
                    "آخر تحديث: {2}",
                    departmentsCount,
                    documentsCount,
                    DateTime.Now.ToString("yyyy/MM/dd HH:mm")
                );
            }
            catch (Exception ex)
            {
                statsLabel.Text = "خطأ في تحميل الإحصائيات: " + ex.Message;
            }
        }

        private int GetDepartmentsCount()
        {
            try
            {
                if (!File.Exists("Data\\departments.xml"))
                    return 0;

                var doc = new XmlDocument();
                doc.Load("Data\\departments.xml");
                return doc.SelectNodes("//Department").Count;
            }
            catch
            {
                return 0;
            }
        }

        private int GetDocumentsCount()
        {
            try
            {
                if (!File.Exists("Data\\documents.xml"))
                    return 0;

                var doc = new XmlDocument();
                doc.Load("Data\\documents.xml");
                return doc.SelectNodes("//Document").Count;
            }
            catch
            {
                return 0;
            }
        }

        private void DepartmentsButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إدارة الأقسام", "إدارة الأقسام",
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        private void DocumentsButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إدارة الوثائق", "إدارة الوثائق",
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة البحث المتقدم قريباً\nهذه الميزة قيد التطوير", "البحث المتقدم",
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        private void SettingsButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة الإعدادات قريباً\nهذه الميزة قيد التطوير", "إعدادات النظام",
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Name = "MainForm";
            this.ResumeLayout(false);
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // إنشاء مجلد البيانات إذا لم يكن موجوداً
                if (!Directory.Exists("Data"))
                {
                    Directory.CreateDirectory("Data");
                }

                // إنشاء ملفات البيانات التجريبية إذا لم تكن موجودة
                CreateSampleData();

                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تشغيل التطبيق: " + ex.Message + "\n\nتفاصيل:\n" + ex.ToString(), 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        static void CreateSampleData()
        {
            try
            {
                // إنشاء ملف الأقسام
                if (!File.Exists("Data\\departments.xml"))
                {
                    var deptXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Departments>
  <Department>
    <Id>1</Id>
    <Name>الإدارة العامة</Name>
    <Description>القسم الرئيسي للإدارة العامة والتنسيق</Description>
    <CreatedDate>2024-01-01</CreatedDate>
    <IsActive>true</IsActive>
  </Department>
  <Department>
    <Id>2</Id>
    <Name>الشؤون المالية</Name>
    <Description>قسم الشؤون المالية والمحاسبة والميزانية</Description>
    <CreatedDate>2024-01-01</CreatedDate>
    <IsActive>true</IsActive>
  </Department>
</Departments>";
                    File.WriteAllText("Data\\departments.xml", deptXml);
                }

                // إنشاء ملف الوثائق
                if (!File.Exists("Data\\documents.xml"))
                {
                    var docXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
<Documents>
  <Document>
    <Id>1</Id>
    <Number>2024/001</Number>
    <Subject>تعميم إداري</Subject>
    <Type>صادر</Type>
    <Date>2024-01-15</Date>
    <DepartmentId>1</DepartmentId>
  </Document>
  <Document>
    <Id>2</Id>
    <Number>2024/002</Number>
    <Subject>طلب معلومات</Subject>
    <Type>وارد</Type>
    <Date>2024-01-20</Date>
    <DepartmentId>2</DepartmentId>
  </Document>
</Documents>";
                    File.WriteAllText("Data\\documents.xml", docXml);
                }
            }
            catch
            {
                // تجاهل أخطاء إنشاء البيانات التجريبية
            }
        }
    }
}
