using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using ArchiveSystem.Helpers;

namespace ArchiveSystem.Controls
{
    /// <summary>
    /// شريط جانبي قابل للطي مع تأثيرات بصرية متقدمة
    /// </summary>
    public partial class CollapsibleSidebar : UserControl
    {
        #region Fields
        private bool _isCollapsed = false;
        private int _expandedWidth = 280;
        private int _collapsedWidth = 60;
        private Timer _animationTimer;
        private int _targetWidth;
        private int _animationStep = 15;
        private Panel _headerPanel;
        private Panel _contentPanel;
        private Button _toggleButton;
        private Label _titleLabel;
        private bool _isAnimating = false;
        #endregion

        #region Properties
        /// <summary>
        /// حالة الطي
        /// </summary>
        public bool IsCollapsed
        {
            get { return _isCollapsed; }
            set
            {
                if (_isCollapsed != value)
                {
                    _isCollapsed = value;
                    ToggleSidebar();
                }
            }
        }

        /// <summary>
        /// العرض عند التوسع
        /// </summary>
        public int ExpandedWidth
        {
            get { return _expandedWidth; }
            set
            {
                _expandedWidth = value;
                if (!_isCollapsed)
                {
                    this.Width = _expandedWidth;
                }
            }
        }

        /// <summary>
        /// العرض عند الطي
        /// </summary>
        public int CollapsedWidth
        {
            get { return _collapsedWidth; }
            set
            {
                _collapsedWidth = value;
                if (_isCollapsed)
                {
                    this.Width = _collapsedWidth;
                }
            }
        }

        /// <summary>
        /// عنوان الشريط الجانبي
        /// </summary>
        public string SidebarTitle
        {
            get { return _titleLabel?.Text ?? string.Empty; }
            set
            {
                if (_titleLabel != null)
                {
                    _titleLabel.Text = value;
                }
            }
        }

        /// <summary>
        /// لوحة المحتوى للإضافة عليها
        /// </summary>
        public Panel ContentPanel
        {
            get { return _contentPanel; }
        }
        #endregion

        #region Events
        /// <summary>
        /// حدث تغيير حالة الطي
        /// </summary>
        public event EventHandler<bool> CollapsedChanged;
        #endregion

        #region Constructor
        public CollapsibleSidebar()
        {
            InitializeComponent();
            SetupControl();
            CreateControls();
            SetupAnimation();
        }
        #endregion

        #region Setup Methods
        private void SetupControl()
        {
            this.Size = new Size(_expandedWidth, 600);
            this.BackColor = RTLHelper.SecondaryColor;
            this.Dock = DockStyle.Right;
            this.RightToLeft = RightToLeft.Yes;
            this.Font = RTLHelper.ArabicFont;

            // تطبيق تأثيرات بصرية متقدمة
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | 
                         ControlStyles.UserPaint | 
                         ControlStyles.DoubleBuffer | 
                         ControlStyles.ResizeRedraw, true);
        }

        private void CreateControls()
        {
            CreateHeaderPanel();
            CreateContentPanel();
        }

        private void CreateHeaderPanel()
        {
            _headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = RTLHelper.PrimaryColor,
                RightToLeft = RightToLeft.Yes
            };

            // زر التبديل
            _toggleButton = new Button
            {
                Size = new Size(40, 40),
                Location = new Point(10, 20),
                Text = "◀",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            _toggleButton.FlatAppearance.BorderSize = 0;
            _toggleButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(50, 255, 255, 255);
            _toggleButton.FlatAppearance.MouseDownBackColor = Color.FromArgb(100, 255, 255, 255);
            _toggleButton.Click += ToggleButton_Click;

            // تسمية العنوان
            _titleLabel = new Label
            {
                Text = "القائمة الرئيسية",
                Font = RTLHelper.ArabicFontLargeBold,
                ForeColor = Color.White,
                Location = new Point(60, 25),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _headerPanel.Controls.Add(_toggleButton);
            _headerPanel.Controls.Add(_titleLabel);
            this.Controls.Add(_headerPanel);
        }

        private void CreateContentPanel()
        {
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = RTLHelper.SecondaryColor,
                Padding = new Padding(10),
                RightToLeft = RightToLeft.Yes,
                AutoScroll = true
            };

            this.Controls.Add(_contentPanel);
        }

        private void SetupAnimation()
        {
            _animationTimer = new Timer
            {
                Interval = 20 // 50 FPS for smooth animation
            };
            _animationTimer.Tick += AnimationTimer_Tick;
        }
        #endregion

        #region Animation Methods
        private void ToggleSidebar()
        {
            if (_isAnimating) return;

            _targetWidth = _isCollapsed ? _collapsedWidth : _expandedWidth;
            _isAnimating = true;
            _animationTimer.Start();

            // تحديث نص زر التبديل
            _toggleButton.Text = _isCollapsed ? "▶" : "◀";

            // إخفاء/إظهار العنوان
            _titleLabel.Visible = !_isCollapsed;

            // إخفاء/إظهار محتوى الشريط الجانبي
            foreach (Control control in _contentPanel.Controls)
            {
                if (control is Button btn)
                {
                    btn.Text = _isCollapsed ? "" : GetButtonOriginalText(btn);
                }
            }

            // إثارة الحدث
            CollapsedChanged?.Invoke(this, _isCollapsed);
        }

        private string GetButtonOriginalText(Button button)
        {
            // يمكن تحسين هذا بحفظ النصوص الأصلية
            if (button.Tag is string originalText)
                return originalText;
            return button.Text;
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            int currentWidth = this.Width;
            int difference = _targetWidth - currentWidth;

            if (Math.Abs(difference) <= _animationStep)
            {
                this.Width = _targetWidth;
                _animationTimer.Stop();
                _isAnimating = false;

                // إظهار العنوان بعد التوسع
                if (!_isCollapsed)
                {
                    _titleLabel.Visible = true;
                }
            }
            else
            {
                int step = difference > 0 ? _animationStep : -_animationStep;
                this.Width = currentWidth + step;
            }

            this.Invalidate();
        }

        private void ToggleButton_Click(object sender, EventArgs e)
        {
            IsCollapsed = !IsCollapsed;
        }
        #endregion

        #region Paint Methods
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            // رسم حدود متدرجة
            using (var brush = new LinearGradientBrush(
                this.ClientRectangle,
                RTLHelper.SecondaryColor,
                RTLHelper.PrimaryColor,
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, this.ClientRectangle);
            }

            // رسم خط فاصل
            using (var pen = new Pen(Color.FromArgb(100, 255, 255, 255), 1))
            {
                e.Graphics.DrawLine(pen, 0, 0, 0, this.Height);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// إضافة زر إلى الشريط الجانبي
        /// </summary>
        public Button AddMenuButton(string text, string icon, Color backgroundColor, EventHandler clickHandler)
        {
            var button = RTLHelper.CreateStyledButton(
                icon + " " + text,
                Point.Empty,
                new Size(_expandedWidth - 40, 55),
                backgroundColor,
                clickHandler
            );

            // حفظ النص الأصلي
            button.Tag = icon + " " + text;
            button.Dock = DockStyle.Top;
            button.Margin = new Padding(5);

            // تطبيق تأثيرات إضافية
            RTLHelper.ApplyButtonEffects(button);

            _contentPanel.Controls.Add(button);
            _contentPanel.Controls.SetChildIndex(button, 0);

            return button;
        }

        /// <summary>
        /// مسح جميع الأزرار
        /// </summary>
        public void ClearMenuButtons()
        {
            _contentPanel.Controls.Clear();
        }
        #endregion

        #region Component Designer
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.Name = "CollapsibleSidebar";
            this.Size = new Size(280, 600);
            this.ResumeLayout(false);
        }
        #endregion
    }
}
