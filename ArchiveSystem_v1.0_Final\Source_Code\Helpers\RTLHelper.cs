using System;
using System.Drawing;
using System.Windows.Forms;

namespace ArchiveSystem.Helpers
{
    /// <summary>
    /// مساعد لإعدادات RTL والواجهة العربية
    /// </summary>
    public static class RTLHelper
    {
        // الخط العربي الموحد للتطبيق
        public static readonly Font ArabicFont = new Font("Segoe UI", 10F, FontStyle.Regular);
        public static readonly Font ArabicFontBold = new Font("Segoe UI", 10F, FontStyle.Bold);
        public static readonly Font ArabicFontLarge = new Font("Segoe UI", 12F, FontStyle.Regular);
        public static readonly Font ArabicFontLargeBold = new Font("Segoe UI", 12F, FontStyle.Bold);
        public static readonly Font ArabicFontTitle = new Font("Segoe UI", 16F, FontStyle.Bold);
        public static readonly Font ArabicFontHeader = new Font("Segoe UI", 20F, FontStyle.Bold);

        // الألوان الموحدة للتطبيق
        public static readonly Color PrimaryColor = Color.FromArgb(52, 73, 94);
        public static readonly Color SecondaryColor = Color.FromArgb(44, 62, 80);
        public static readonly Color AccentColor = Color.FromArgb(52, 152, 219);
        public static readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        public static readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        public static readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        public static readonly Color InfoColor = Color.FromArgb(155, 89, 182);
        public static readonly Color LightColor = Color.FromArgb(236, 240, 241);
        public static readonly Color DarkColor = Color.FromArgb(127, 140, 141);

        /// <summary>
        /// تطبيق إعدادات RTL على النافذة
        /// </summary>
        public static void ApplyRTLSettings(Form form)
        {
            if (form == null) return;

            form.RightToLeft = RightToLeft.Yes;
            form.RightToLeftLayout = true;
            form.Font = ArabicFont;

            // تطبيق الإعدادات على جميع العناصر الفرعية
            ApplyRTLToControls(form.Controls);
        }

        /// <summary>
        /// تطبيق إعدادات RTL على مجموعة من العناصر
        /// </summary>
        private static void ApplyRTLToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                // تطبيق RTL على العنصر
                control.RightToLeft = RightToLeft.Yes;
                
                // تطبيق الخط العربي
                if (control.Font.Name != ArabicFont.Name)
                {
                    control.Font = ArabicFont;
                }

                // إعدادات خاصة لأنواع مختلفة من العناصر
                ApplySpecificRTLSettings(control);

                // تطبيق الإعدادات على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyRTLToControls(control.Controls);
                }
            }
        }

        /// <summary>
        /// تطبيق إعدادات RTL خاصة لأنواع مختلفة من العناصر
        /// </summary>
        private static void ApplySpecificRTLSettings(Control control)
        {
            if (control is TextBox)
            {
                var textBox = (TextBox)control;
                textBox.TextAlign = HorizontalAlignment.Right;
                textBox.RightToLeft = RightToLeft.Yes;
            }
            else if (control is ComboBox)
            {
                var comboBox = (ComboBox)control;
                comboBox.RightToLeft = RightToLeft.Yes;
                // تطبيق RTL على العناصر داخل ComboBox
                comboBox.DrawMode = DrawMode.Normal;
            }
            else if (control is ListView)
            {
                var listView = (ListView)control;
                listView.RightToLeftLayout = true;
                listView.RightToLeft = RightToLeft.Yes;
                // ضبط محاذاة الأعمدة
                foreach (ColumnHeader column in listView.Columns)
                {
                    column.TextAlign = HorizontalAlignment.Right;
                }
                // إعادة تطبيق RTL على الأعمدة الجديدة عند الحاجة
            }
            else if (control is Label)
            {
                var label = (Label)control;
                // تحديد المحاذاة حسب موقع Label
                if (label.TextAlign == ContentAlignment.TopLeft ||
                    label.TextAlign == ContentAlignment.MiddleLeft ||
                    label.TextAlign == ContentAlignment.BottomLeft)
                {
                    // تحويل المحاذاة اليسرى إلى يمنى
                    if (label.TextAlign == ContentAlignment.TopLeft)
                        label.TextAlign = ContentAlignment.TopRight;
                    else if (label.TextAlign == ContentAlignment.MiddleLeft)
                        label.TextAlign = ContentAlignment.MiddleRight;
                    else if (label.TextAlign == ContentAlignment.BottomLeft)
                        label.TextAlign = ContentAlignment.BottomRight;
                }
                else if (label.TextAlign == ContentAlignment.TopCenter ||
                         label.TextAlign == ContentAlignment.MiddleCenter ||
                         label.TextAlign == ContentAlignment.BottomCenter)
                {
                    // الاحتفاظ بالمحاذاة الوسطى
                }
                else
                {
                    // المحاذاة الافتراضية للنصوص العربية
                    label.TextAlign = ContentAlignment.MiddleRight;
                }
            }
            else if (control is Button)
            {
                var button = (Button)control;
                button.TextAlign = ContentAlignment.MiddleCenter;
                button.RightToLeft = RightToLeft.Yes;
            }
            else if (control is GroupBox)
            {
                var groupBox = (GroupBox)control;
                groupBox.RightToLeft = RightToLeft.Yes;
            }
            else if (control is Panel)
            {
                var panel = (Panel)control;
                panel.RightToLeft = RightToLeft.Yes;
            }
            else if (control is TabControl)
            {
                var tabControl = (TabControl)control;
                tabControl.RightToLeft = RightToLeft.Yes;
                tabControl.RightToLeftLayout = true;
            }
            else if (control is TabPage)
            {
                var tabPage = (TabPage)control;
                tabPage.RightToLeft = RightToLeft.Yes;
            }
            else if (control is CheckBox)
            {
                var checkBox = (CheckBox)control;
                checkBox.RightToLeft = RightToLeft.Yes;
                checkBox.CheckAlign = ContentAlignment.MiddleRight;
                checkBox.TextAlign = ContentAlignment.MiddleLeft;
            }
            else if (control is RadioButton)
            {
                var radioButton = (RadioButton)control;
                radioButton.RightToLeft = RightToLeft.Yes;
                radioButton.CheckAlign = ContentAlignment.MiddleRight;
                radioButton.TextAlign = ContentAlignment.MiddleLeft;
            }
            else if (control is DateTimePicker)
            {
                var dateTimePicker = (DateTimePicker)control;
                dateTimePicker.RightToLeft = RightToLeft.Yes;
            }
            else if (control is NumericUpDown)
            {
                var numericUpDown = (NumericUpDown)control;
                numericUpDown.RightToLeft = RightToLeft.Yes;
                numericUpDown.TextAlign = HorizontalAlignment.Right;
            }
        }



        /// <summary>
        /// إنشاء زر بتصميم موحد
        /// </summary>
        public static Button CreateStyledButton(string text, Point location, Size size, Color backgroundColor, EventHandler clickHandler)
        {
            var button = new Button();
            button.Text = text;
            button.Location = location;
            button.Size = size;
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.Font = ArabicFontBold;
            button.TextAlign = ContentAlignment.MiddleCenter;
            button.Cursor = Cursors.Hand;
            button.RightToLeft = RightToLeft.Yes;

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.BorderColor = backgroundColor;

            // تأثير hover
            button.MouseEnter += delegate(object s, EventArgs e) {
                button.BackColor = ControlPaint.Light(backgroundColor, 0.1f);
            };
            button.MouseLeave += delegate(object s, EventArgs e) {
                button.BackColor = backgroundColor;
            };

            if (clickHandler != null)
            {
                button.Click += clickHandler;
            }

            return button;
        }

        /// <summary>
        /// إنشاء تسمية بتصميم موحد
        /// </summary>
        public static Label CreateStyledLabel(string text, Point location, Size size, Font font, Color foreColor, ContentAlignment textAlign)
        {
            var label = new Label();
            label.Text = text;
            label.Location = location;
            label.Size = size;
            label.Font = font != null ? font : ArabicFont;
            label.ForeColor = foreColor;
            label.TextAlign = textAlign;
            label.RightToLeft = RightToLeft.Yes;
            return label;
        }

        /// <summary>
        /// إنشاء حقل نص بتصميم موحد
        /// </summary>
        public static TextBox CreateStyledTextBox(Point location, Size size, bool multiline)
        {
            var textBox = new TextBox();
            textBox.Location = location;
            textBox.Size = size;
            textBox.Font = ArabicFont;
            textBox.RightToLeft = RightToLeft.Yes;
            textBox.TextAlign = HorizontalAlignment.Right;
            textBox.Multiline = multiline;

            if (multiline)
            {
                textBox.ScrollBars = ScrollBars.Vertical;
            }

            return textBox;
        }

        /// <summary>
        /// إنشاء قائمة منسدلة بتصميم موحد
        /// </summary>
        public static ComboBox CreateStyledComboBox(Point location, Size size, string[] items)
        {
            var comboBox = new ComboBox();
            comboBox.Location = location;
            comboBox.Size = size;
            comboBox.Font = ArabicFont;
            comboBox.RightToLeft = RightToLeft.Yes;
            comboBox.DropDownStyle = ComboBoxStyle.DropDownList;

            if (items != null)
            {
                comboBox.Items.AddRange(items);
                if (items.Length > 0)
                {
                    comboBox.SelectedIndex = 0;
                }
            }

            return comboBox;
        }

        /// <summary>
        /// إنشاء ListView بتصميم موحد
        /// </summary>
        public static ListView CreateStyledListView(Point location, Size size, string[] columnHeaders, int[] columnWidths)
        {
            var listView = new ListView();
            listView.Location = location;
            listView.Size = size;
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = true;
            listView.MultiSelect = false;
            listView.Font = ArabicFont;
            listView.RightToLeftLayout = true;
            listView.RightToLeft = RightToLeft.Yes;

            if (columnHeaders != null)
            {
                for (int i = 0; i < columnHeaders.Length; i++)
                {
                    int width = (columnWidths != null && i < columnWidths.Length) ? columnWidths[i] : 100;
                    var column = listView.Columns.Add(columnHeaders[i], width);
                    column.TextAlign = HorizontalAlignment.Right;
                }
            }

            return listView;
        }

        /// <summary>
        /// إنشاء لوحة بتصميم موحد
        /// </summary>
        public static Panel CreateStyledPanel(Point location, Size size, Color backgroundColor, BorderStyle borderStyle)
        {
            var panel = new Panel();
            panel.Location = location;
            panel.Size = size;
            panel.BackColor = backgroundColor;
            panel.BorderStyle = borderStyle;
            panel.RightToLeft = RightToLeft.Yes;
            return panel;
        }

        /// <summary>
        /// عرض رسالة بتصميم RTL
        /// </summary>
        public static DialogResult ShowRTLMessage(string message, string title, MessageBoxButtons buttons = MessageBoxButtons.OK, MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            return MessageBox.Show(message, title, buttons, icon, MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        /// <summary>
        /// تطبيق تأثيرات بصرية على الأزرار
        /// </summary>
        public static void ApplyButtonEffects(Button button)
        {
            Color originalColor = button.BackColor;

            button.MouseEnter += delegate(object s, EventArgs e) {
                button.BackColor = ControlPaint.Light(originalColor, 0.2f);
            };

            button.MouseLeave += delegate(object s, EventArgs e) {
                button.BackColor = originalColor;
            };

            button.MouseDown += delegate(object s, MouseEventArgs e) {
                button.BackColor = ControlPaint.Dark(originalColor, 0.1f);
            };

            button.MouseUp += delegate(object s, MouseEventArgs e) {
                button.BackColor = ControlPaint.Light(originalColor, 0.2f);
            };
        }

        /// <summary>
        /// إنشاء DateTimePicker بتصميم موحد
        /// </summary>
        public static DateTimePicker CreateStyledDateTimePicker(Point location, Size size, DateTimePickerFormat format = DateTimePickerFormat.Short)
        {
            var dateTimePicker = new DateTimePicker();
            dateTimePicker.Location = location;
            dateTimePicker.Size = size;
            dateTimePicker.Font = ArabicFont;
            dateTimePicker.RightToLeft = RightToLeft.Yes;

            dateTimePicker.Format = format;

            // تنسيق التاريخ العربي
            if (format == DateTimePickerFormat.Custom)
            {
                dateTimePicker.CustomFormat = "dd/MM/yyyy";
            }

            return dateTimePicker;
        }

        /// <summary>
        /// إنشاء CheckBox بتصميم موحد
        /// </summary>
        public static CheckBox CreateStyledCheckBox(string text, Point location, Size size, bool isChecked = false)
        {
            var checkBox = new CheckBox();
            checkBox.Text = text;
            checkBox.Location = location;
            checkBox.Size = size;
            checkBox.Font = ArabicFont;
            checkBox.RightToLeft = RightToLeft.Yes;
            checkBox.CheckAlign = ContentAlignment.MiddleRight;
            checkBox.TextAlign = ContentAlignment.MiddleLeft;
            checkBox.Checked = isChecked;
            return checkBox;
        }

        /// <summary>
        /// إنشاء GroupBox بتصميم موحد
        /// </summary>
        public static GroupBox CreateStyledGroupBox(string text, Point location, Size size)
        {
            var groupBox = new GroupBox();
            groupBox.Text = text;
            groupBox.Location = location;
            groupBox.Size = size;
            groupBox.Font = ArabicFontBold;
            groupBox.RightToLeft = RightToLeft.Yes;

            return groupBox;
        }

        /// <summary>
        /// تطبيق RTL على MessageBox مخصص
        /// </summary>
        public static DialogResult ShowRTLMessageBox(string message, string title,
            MessageBoxButtons buttons = MessageBoxButtons.OK,
            MessageBoxIcon icon = MessageBoxIcon.Information,
            MessageBoxDefaultButton defaultButton = MessageBoxDefaultButton.Button1)
        {
            return MessageBox.Show(message, title, buttons, icon, defaultButton,
                MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        /// <summary>
        /// تطبيق RTL على جميع النوافذ المفتوحة
        /// </summary>
        public static void ApplyRTLToAllForms()
        {
            foreach (Form form in Application.OpenForms)
            {
                ApplyRTLSettings(form);
            }
        }

        /// <summary>
        /// إصلاح مشاكل المحاذاة الشائعة
        /// </summary>
        public static void FixCommonAlignmentIssues(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                // إصلاح محاذاة الأزرار في أسفل النوافذ
                if (control is Button && control.Dock == DockStyle.None)
                {
                    // التأكد من أن الأزرار في الجانب الأيمن
                    if (control.Location.X < parent.Width / 2)
                    {
                        control.Location = new Point(parent.Width - control.Width - control.Location.X, control.Location.Y);
                    }
                }

                // إصلاح محاذاة التسميات
                if (control is Label)
                {
                    var label = (Label)control;
                    if (label.TextAlign == ContentAlignment.TopLeft ||
                        label.TextAlign == ContentAlignment.MiddleLeft ||
                        label.TextAlign == ContentAlignment.BottomLeft)
                    {
                        // تحويل المحاذاة من اليسار إلى اليمين
                        switch (label.TextAlign)
                        {
                            case ContentAlignment.TopLeft:
                                label.TextAlign = ContentAlignment.TopRight;
                                break;
                            case ContentAlignment.MiddleLeft:
                                label.TextAlign = ContentAlignment.MiddleRight;
                                break;
                            case ContentAlignment.BottomLeft:
                                label.TextAlign = ContentAlignment.BottomRight;
                                break;
                        }
                    }
                }

                // تطبيق الإصلاحات على العناصر الفرعية
                if (control.HasChildren)
                {
                    FixCommonAlignmentIssues(control);
                }
            }
        }

        /// <summary>
        /// التحقق من تطبيق RTL بشكل صحيح
        /// </summary>
        public static bool ValidateRTLImplementation(Form form)
        {
            if (form.RightToLeft != RightToLeft.Yes || !form.RightToLeftLayout)
            {
                return false;
            }

            return ValidateControlsRTL(form.Controls);
        }

        /// <summary>
        /// التحقق من تطبيق RTL على العناصر
        /// </summary>
        private static bool ValidateControlsRTL(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                if (control.RightToLeft != RightToLeft.Yes)
                {
                    return false;
                }

                if (control.HasChildren && !ValidateControlsRTL(control.Controls))
                {
                    return false;
                }
            }
            return true;
        }
    }
}
