using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Services;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة عرض مرفقات الوثيقة
    /// </summary>
    public partial class DocumentAttachmentsForm : Form
    {
        private readonly Document _document;
        private readonly AttachmentRepository _attachmentRepository;
        private readonly FileService _fileService;
        private List<Attachment> _attachments;

        private ListBox _attachmentsList;
        private PictureBox _previewPictureBox;
        private Panel _previewPanel;
        private Label _fileInfoLabel;
        private int _currentIndex;

        public DocumentAttachmentsForm(Document document)
        {
            InitializeComponent();
            _document = document;
            _attachmentRepository = new AttachmentRepository();
            _fileService = new FileService();
            _currentIndex = 0;
            InitializeControls();
            LoadAttachments();
        }

        private void InitializeControls()
        {
            this.Text = "مرفقات الوثيقة - " + _document.DocumentNumber;
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 242, 247);

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "مرفقات الوثيقة: " + _document.DocumentNumber,
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(400, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // الجانب الأيمن - قائمة المرفقات
            CreateAttachmentsPanel();

            // الجانب الأيسر - المعاينة
            CreatePreviewPanel();

            // أزرار الإجراءات
            CreateActionButtons();
        }

        private void CreateAttachmentsPanel()
        {
            var attachmentsPanel = new Panel
            {
                Location = new Point(520, 60),
                Size = new Size(450, 500),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // عنوان القائمة
            var listLabel = new Label
            {
                Text = "قائمة المرفقات:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                Location = new Point(350, 10),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            attachmentsPanel.Controls.Add(listLabel);

            // قائمة المرفقات
            _attachmentsList = new ListBox
            {
                Location = new Point(10, 40),
                Size = new Size(430, 350),
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes
            };
            _attachmentsList.SelectedIndexChanged += AttachmentsList_SelectedIndexChanged;
            attachmentsPanel.Controls.Add(_attachmentsList);

            // معلومات الملف
            _fileInfoLabel = new Label
            {
                Location = new Point(10, 400),
                Size = new Size(430, 80),
                Font = new Font("Tahoma", 9F),
                BorderStyle = BorderStyle.Fixed3D,
                BackColor = Color.FromArgb(248, 249, 250),
                TextAlign = ContentAlignment.TopRight,
                Padding = new Padding(5)
            };
            attachmentsPanel.Controls.Add(_fileInfoLabel);

            this.Controls.Add(attachmentsPanel);
        }

        private void CreatePreviewPanel()
        {
            _previewPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(480, 500),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // عنوان المعاينة
            var previewLabel = new Label
            {
                Text = "معاينة الملف:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                Location = new Point(380, 10),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            _previewPanel.Controls.Add(previewLabel);

            // منطقة المعاينة
            _previewPictureBox = new PictureBox
            {
                Location = new Point(10, 40),
                Size = new Size(460, 400),
                BorderStyle = BorderStyle.Fixed3D,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.LightGray
            };
            _previewPanel.Controls.Add(_previewPictureBox);

            // أزرار التنقل
            var prevButton = new Button
            {
                Text = "السابق",
                Location = new Point(10, 450),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            prevButton.Click += PrevButton_Click;
            _previewPanel.Controls.Add(prevButton);

            var nextButton = new Button
            {
                Text = "التالي",
                Location = new Point(100, 450),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            nextButton.Click += NextButton_Click;
            _previewPanel.Controls.Add(nextButton);

            var openButton = new Button
            {
                Text = "فتح الملف",
                Location = new Point(300, 450),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            openButton.Click += OpenButton_Click;
            _previewPanel.Controls.Add(openButton);

            var enlargeButton = new Button
            {
                Text = "تكبير",
                Location = new Point(390, 450),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            enlargeButton.Click += EnlargeButton_Click;
            _previewPanel.Controls.Add(enlargeButton);

            this.Controls.Add(_previewPanel);
        }

        private void CreateActionButtons()
        {
            // زر إضافة مرفق
            var addButton = new Button
            {
                Text = "إضافة مرفق",
                Location = new Point(20, 580),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += AddButton_Click;
            this.Controls.Add(addButton);

            // زر حذف مرفق
            var deleteButton = new Button
            {
                Text = "حذف المرفق",
                Location = new Point(150, 580),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // زر حفظ نسخة
            var saveButton = new Button
            {
                Text = "حفظ نسخة",
                Location = new Point(280, 580),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // زر طباعة
            var printButton = new Button
            {
                Text = "طباعة",
                Location = new Point(410, 580),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += PrintButton_Click;
            this.Controls.Add(printButton);

            // زر إغلاق
            var closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(850, 580),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);
        }

        private void LoadAttachments()
        {
            try
            {
                _attachments = _attachmentRepository.GetByDocumentId(_document.DocumentId);
                
                _attachmentsList.Items.Clear();
                foreach (var attachment in _attachments)
                {
                    _attachmentsList.Items.Add(attachment.FileName + " (" + attachment.FileSizeFormatted + ")");
                }

                if (_attachments.Count > 0)
                {
                    _attachmentsList.SelectedIndex = 0;
                    ShowPreview();
                }
                else
                {
                    _fileInfoLabel.Text = "لا توجد مرفقات لهذه الوثيقة";
                    _previewPictureBox.Image = null;
                }

                // تحديث عنوان النافذة
                this.Text = "مرفقات الوثيقة - " + _document.DocumentNumber + " (" + _attachments.Count + " مرفق)";
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل المرفقات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AttachmentsList_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_attachmentsList.SelectedIndex >= 0)
            {
                _currentIndex = _attachmentsList.SelectedIndex;
                ShowPreview();
            }
        }

        private void ShowPreview()
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count)
            {
                var attachment = _attachments[_currentIndex];

                // عرض معلومات الملف
                _fileInfoLabel.Text = "اسم الملف: " + attachment.FileName + "\n" +
                                     "نوع الملف: " + attachment.FileType + "\n" +
                                     "حجم الملف: " + attachment.FileSizeFormatted + "\n" +
                                     "تاريخ الرفع: " + attachment.UploadDate.ToString("yyyy/MM/dd HH:mm");

                // معاينة الملف
                if (_fileService.FileExists(attachment.FilePath))
                {
                    string extension = attachment.FileExtension.ToLower();

                    if (extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                        extension == ".bmp" || extension == ".gif" || extension == ".tiff")
                    {
                        try
                        {
                            _previewPictureBox.Image = Image.FromFile(attachment.FilePath);
                        }
                        catch
                        {
                            _previewPictureBox.Image = null;
                        }
                    }
                    else
                    {
                        _previewPictureBox.Image = null;
                    }
                }
                else
                {
                    _previewPictureBox.Image = null;
                    _fileInfoLabel.Text += "\n\nتحذير: الملف غير موجود في المسار المحدد";
                }
            }
        }

        private void PrevButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex > 0)
            {
                _currentIndex--;
                _attachmentsList.SelectedIndex = _currentIndex;
                ShowPreview();
            }
        }

        private void NextButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count - 1)
            {
                _currentIndex++;
                _attachmentsList.SelectedIndex = _currentIndex;
                ShowPreview();
            }
        }

        private void OpenButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count)
            {
                var attachment = _attachments[_currentIndex];
                if (_fileService.FileExists(attachment.FilePath))
                {
                    _fileService.OpenFile(attachment.FilePath);
                }
                else
                {
                    MessageBox.Show("الملف غير موجود في المسار المحدد", "ملف غير موجود",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        private void EnlargeButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count)
            {
                var attachment = _attachments[_currentIndex];
                if (_fileService.FileExists(attachment.FilePath))
                {
                    var enlargeForm = new ImageViewerForm(attachment.FilePath);
                    enlargeForm.ShowDialog();
                }
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = _fileService.GetSupportedImageFormats();
                openFileDialog.Multiselect = true;
                openFileDialog.Title = "اختيار الملفات";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        foreach (string fileName in openFileDialog.FileNames)
                        {
                            // نسخ الملف إلى مجلد المرفقات
                            string savedPath = _fileService.SaveAttachment(fileName, _document.DocumentId, Path.GetFileName(fileName));

                            // إنشاء سجل المرفق
                            var attachment = new Attachment
                            {
                                DocumentId = _document.DocumentId,
                                FileName = Path.GetFileName(fileName),
                                FilePath = savedPath,
                                FileExtension = Path.GetExtension(fileName),
                                FileSize = new FileInfo(fileName).Length,
                                FileType = _fileService.GetFileType(Path.GetExtension(fileName))
                            };

                            // حفظ المرفق في قاعدة البيانات
                            _attachmentRepository.Insert(attachment);
                        }

                        MessageBox.Show("تم إضافة المرفقات بنجاح", "نجح الحفظ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // إعادة تحميل المرفقات
                        LoadAttachments();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في إضافة المرفقات: " + ex.Message, "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count)
            {
                var attachment = _attachments[_currentIndex];

                var result = MessageBox.Show("هل أنت متأكد من حذف المرفق '" + attachment.FileName + "'؟",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        // حذف الملف من النظام
                        _fileService.DeleteAttachment(attachment.FilePath);

                        // حذف السجل من قاعدة البيانات
                        _attachmentRepository.Delete(attachment.AttachmentId);

                        MessageBox.Show("تم حذف المرفق بنجاح", "تم الحذف",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // إعادة تحميل المرفقات
                        LoadAttachments();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في حذف المرفق: " + ex.Message, "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count)
            {
                var attachment = _attachments[_currentIndex];

                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.FileName = attachment.FileName;
                    saveFileDialog.Filter = "جميع الملفات|*.*";
                    saveFileDialog.Title = "حفظ نسخة من الملف";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            File.Copy(attachment.FilePath, saveFileDialog.FileName, true);
                            MessageBox.Show("تم حفظ نسخة من الملف بنجاح", "تم الحفظ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("خطأ في حفظ الملف: " + ex.Message, "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            if (_attachments.Count > 0 && _currentIndex < _attachments.Count)
            {
                var attachment = _attachments[_currentIndex];

                if (attachment.FileExtension.ToLower() == ".pdf" ||
                    attachment.FileType.ToLower() == "image")
                {
                    try
                    {
                        _fileService.OpenFile(attachment.FilePath);
                        MessageBox.Show("تم فتح الملف. يمكنك الطباعة من التطبيق المفتوح.", "الطباعة",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في فتح الملف للطباعة: " + ex.Message, "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show("نوع الملف غير مدعوم للطباعة المباشرة", "نوع ملف غير مدعوم",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }
    }
}
