using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    public partial class SimpleDocumentsForm : Form
    {
        private DataGridView _documentsDataGridView;
        private ComboBox _typeComboBox;
        private ComboBox _yearComboBox;
        private TextBox _searchTextBox;
        private Button _searchButton;
        private Button _addButton;
        private Button _editButton;
        private Button _deleteButton;
        private Button _viewButton;
        private Button _viewAttachmentsButton;
        private Button _previewFileButton;
        private Button _closeButton;
        private Panel _bottomPanel;
        private List<DocumentSimple> _documents;
        private List<DocumentSimple> _filteredDocuments;
        private SimpleDataManager _dataManager;

        public SimpleDocumentsForm()
        {
            InitializeComponent();
            _dataManager = SimpleDataManager.Instance;
            SetupForm();
            CreateControls();
            LoadDocuments();

            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Documents.ico");

            // تطبيق الإصلاح الشامل لـ RTL
            RTLHelper.ComprehensiveRTLFix(this);
        }

        private void SetupForm()
        {
            this.Text = "📄 إدارة الوثائق";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(1100, 750);
        }

        private void CreateControls()
        {
            // عنوان النافذة مع توسيط
            var titleLabel = RTLHelper.CreateStyledLabel(
                "إدارة الوثائق",
                new Point(0, 20),
                new Size(this.Width, 40),
                RTLHelper.ArabicFontHeader,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleCenter
            );
            this.Controls.Add(titleLabel);

            CreateSearchControls();
            CreateDocumentsList();
            CreateBottomPanel();
        }

        private void CreateSearchControls()
        {
            // لوحة البحث
            var searchPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(950, 60),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // نوع الوثيقة
            var typeLabel = new Label
            {
                Text = "النوع:",
                Location = new Point(850, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(typeLabel);

            _typeComboBox = new ComboBox
            {
                Location = new Point(720, 15),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            _typeComboBox.Items.AddRange(new object[] { "الكل", "صادر", "وارد" });
            _typeComboBox.SelectedIndex = 0;
            searchPanel.Controls.Add(_typeComboBox);

            // السنة
            var yearLabel = new Label
            {
                Text = "السنة:",
                Location = new Point(650, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(yearLabel);

            _yearComboBox = new ComboBox
            {
                Location = new Point(520, 15),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            _yearComboBox.Items.Add("الكل");
            for (int year = DateTime.Now.Year; year >= 2020; year--)
            {
                _yearComboBox.Items.Add(year.ToString());
            }
            _yearComboBox.SelectedIndex = 0;
            searchPanel.Controls.Add(_yearComboBox);

            // البحث النصي
            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(450, 15),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(searchLabel);

            _searchTextBox = new TextBox
            {
                Location = new Point(200, 15),
                Size = new Size(240, 25),
                RightToLeft = RightToLeft.Yes
            };
            searchPanel.Controls.Add(_searchTextBox);

            _searchButton = new Button
            {
                Text = "بحث",
                Location = new Point(120, 12),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _searchButton.FlatAppearance.BorderSize = 0;
            _searchButton.Click += SearchButton_Click;
            searchPanel.Controls.Add(_searchButton);

            this.Controls.Add(searchPanel);
        }

        private void CreateDocumentsList()
        {
            _documentsDataGridView = new DataGridView
            {
                Location = new Point(20, 140),
                Size = new Size(this.Width - 60, 500),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                Font = RTLHelper.ArabicFont,
                RightToLeft = RightToLeft.Yes,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // إعداد الأعمدة بالترتيب المطلوب
            _documentsDataGridView.Columns.Add("DocumentNumber", "رقم الكتاب");
            _documentsDataGridView.Columns.Add("FolderNumber", "رقم الأضبارة");
            _documentsDataGridView.Columns.Add("Subject", "الموضوع");
            _documentsDataGridView.Columns.Add("Type", "النوع");
            _documentsDataGridView.Columns.Add("Department", "القسم");
            _documentsDataGridView.Columns.Add("SenderReceiver", "المرسل/المستقبل");
            _documentsDataGridView.Columns.Add("DocumentDate", "التاريخ");
            _documentsDataGridView.Columns.Add("AttachmentsCount", "عدد المرفقات");

            // تخصيص عرض الأعمدة
            _documentsDataGridView.Columns["DocumentNumber"].FillWeight = 15;
            _documentsDataGridView.Columns["FolderNumber"].FillWeight = 15;
            _documentsDataGridView.Columns["Subject"].FillWeight = 25;
            _documentsDataGridView.Columns["Type"].FillWeight = 10;
            _documentsDataGridView.Columns["Department"].FillWeight = 15;
            _documentsDataGridView.Columns["SenderReceiver"].FillWeight = 20;
            _documentsDataGridView.Columns["DocumentDate"].FillWeight = 12;
            _documentsDataGridView.Columns["AttachmentsCount"].FillWeight = 8;

            // تطبيق الألوان المتناوبة
            _documentsDataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            _documentsDataGridView.DefaultCellStyle.BackColor = Color.White;
            _documentsDataGridView.DefaultCellStyle.SelectionBackColor = RTLHelper.AccentColor;
            _documentsDataGridView.DefaultCellStyle.SelectionForeColor = Color.White;

            // تفعيل الترتيب
            foreach (DataGridViewColumn column in _documentsDataGridView.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.Automatic;
            }

            _documentsDataGridView.SelectionChanged += DocumentsDataGridView_SelectionChanged;

            this.Controls.Add(_documentsDataGridView);
        }

        private void CreateBottomPanel()
        {
            _bottomPanel = new Panel
            {
                Location = new Point(0, this.Height - 100),
                Size = new Size(this.Width, 80),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = RTLHelper.LightColor
            };

            int buttonWidth = 130;
            int buttonHeight = 40;
            int spacing = 15;
            int totalButtonsWidth = (buttonWidth * 7) + (spacing * 6);
            int startX = (this.Width - totalButtonsWidth) / 2;
            int buttonY = 20;

            _addButton = RTLHelper.CreateStyledButton(
                "📄 إضافة وثيقة",
                new Point(startX, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                AddButton_Click
            );

            _editButton = RTLHelper.CreateStyledButton(
                "✏️ تعديل وثيقة",
                new Point(startX + (buttonWidth + spacing), buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                EditButton_Click
            );
            _editButton.Enabled = false;

            _deleteButton = RTLHelper.CreateStyledButton(
                "🗑️ حذف وثيقة",
                new Point(startX + (buttonWidth + spacing) * 2, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DangerColor,
                DeleteButton_Click
            );
            _deleteButton.Enabled = false;

            _viewButton = RTLHelper.CreateStyledButton(
                "👁️ عرض التفاصيل",
                new Point(startX + (buttonWidth + spacing) * 3, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                ViewButton_Click
            );
            _viewButton.Enabled = false;

            _viewAttachmentsButton = RTLHelper.CreateStyledButton(
                "📎 عرض المرفقات",
                new Point(startX + (buttonWidth + spacing) * 4, buttonY),
                new Size(buttonWidth, buttonHeight),
                Color.FromArgb(155, 89, 182),
                ViewAttachmentsButton_Click
            );
            _viewAttachmentsButton.Enabled = false;

            _previewFileButton = RTLHelper.CreateStyledButton(
                "👁️ معاينة الملف",
                new Point(startX + (buttonWidth + spacing) * 5, buttonY),
                new Size(buttonWidth, buttonHeight),
                Color.FromArgb(230, 126, 34),
                PreviewFileButton_Click
            );
            _previewFileButton.Enabled = false;

            _closeButton = RTLHelper.CreateStyledButton(
                "❌ إغلاق",
                new Point(startX + (buttonWidth + spacing) * 6, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SecondaryColor,
                delegate { this.Close(); }
            );

            RTLHelper.ApplyButtonEffects(_addButton);
            RTLHelper.ApplyButtonEffects(_editButton);
            RTLHelper.ApplyButtonEffects(_deleteButton);
            RTLHelper.ApplyButtonEffects(_viewButton);
            RTLHelper.ApplyButtonEffects(_viewAttachmentsButton);
            RTLHelper.ApplyButtonEffects(_previewFileButton);
            RTLHelper.ApplyButtonEffects(_closeButton);

            _bottomPanel.Controls.Add(_addButton);
            _bottomPanel.Controls.Add(_editButton);
            _bottomPanel.Controls.Add(_deleteButton);
            _bottomPanel.Controls.Add(_viewButton);
            _bottomPanel.Controls.Add(_viewAttachmentsButton);
            _bottomPanel.Controls.Add(_previewFileButton);
            _bottomPanel.Controls.Add(_closeButton);

            this.Controls.Add(_bottomPanel);
        }

        private void LoadDocuments()
        {
            try
            {
                _documents = _dataManager.GetDocuments();
                _filteredDocuments = new List<DocumentSimple>(_documents);
                RefreshDocumentsList();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                _documents = new List<DocumentSimple>();
                _filteredDocuments = new List<DocumentSimple>();
            }
        }

        private void RefreshDocumentsList()
        {
            _documentsDataGridView.Rows.Clear();

            foreach (var doc in _filteredDocuments)
            {
                string senderReceiverLabel = doc.DocumentType == "صادر" ? "الجهة المستقبلة" : "الجهة المرسلة";
                string senderReceiverValue = string.IsNullOrEmpty(doc.SenderReceiver) ? "غير محدد" : doc.SenderReceiver;

                var rowIndex = _documentsDataGridView.Rows.Add(
                    doc.DocumentNumber,
                    doc.FolderNumber ?? "غير محدد",
                    doc.Subject,
                    doc.TypeIcon + " " + doc.DocumentType,
                    doc.DepartmentName ?? "غير محدد",
                    senderReceiverValue,
                    doc.DocumentDateFormatted,
                    doc.AttachmentsCount.ToString()
                );

                _documentsDataGridView.Rows[rowIndex].Tag = doc;
            }
        }

        private void DocumentsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = _documentsDataGridView.SelectedRows.Count > 0;
            _editButton.Enabled = hasSelection;
            _deleteButton.Enabled = hasSelection;
            _viewButton.Enabled = hasSelection;
            _viewAttachmentsButton.Enabled = hasSelection;
            _previewFileButton.Enabled = hasSelection;
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                string typeFilter = _typeComboBox.SelectedItem.ToString() == "الكل" ? null : _typeComboBox.SelectedItem.ToString();
                string searchText = string.IsNullOrEmpty(_searchTextBox.Text) ? null : _searchTextBox.Text.Trim();

                _filteredDocuments = _dataManager.GetDocuments(typeFilter, searchText);

                // تصفية حسب السنة
                if (_yearComboBox.SelectedItem.ToString() != "الكل")
                {
                    int selectedYear = int.Parse(_yearComboBox.SelectedItem.ToString());
                    var yearFiltered = new List<DocumentSimple>();
                    foreach (var doc in _filteredDocuments)
                    {
                        if (doc.DocumentDate.Year == selectedYear)
                            yearFiltered.Add(doc);
                    }
                    _filteredDocuments = yearFiltered;
                }

                RefreshDocumentsList();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في البحث: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                var addDocumentForm = new SimpleAddDocumentForm();
                if (addDocumentForm.ShowDialog() == DialogResult.OK)
                {
                    LoadDocuments(); // إعادة تحميل القائمة
                    RTLHelper.ShowRTLMessageBox("تم إضافة الوثيقة بنجاح!", "نجح الحفظ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة إضافة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_documentsDataGridView.SelectedRows.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsDataGridView.SelectedRows[0].Tag;

                try
                {
                    var editDocumentForm = new SimpleEditDocumentForm(selectedDoc);
                    if (editDocumentForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadDocuments(); // إعادة تحميل القائمة
                        RTLHelper.ShowRTLMessageBox("تم تحديث الوثيقة بنجاح!", "نجح التحديث",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة تعديل الوثائق: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_documentsDataGridView.SelectedRows.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsDataGridView.SelectedRows[0].Tag;

                var result = RTLHelper.ShowRTLMessageBox("هل أنت متأكد من حذف الوثيقة '" + selectedDoc.Subject + "'؟",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        if (_dataManager.DeleteDocument(selectedDoc.DocumentId))
                        {
                            LoadDocuments(); // إعادة تحميل القائمة
                            RTLHelper.ShowRTLMessageBox("تم حذف الوثيقة بنجاح!", "نجح الحذف",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            RTLHelper.ShowRTLMessageBox("فشل في حذف الوثيقة", "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        RTLHelper.ShowRTLMessageBox("خطأ في حذف الوثيقة: " + ex.Message, "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ViewButton_Click(object sender, EventArgs e)
        {
            if (_documentsDataGridView.SelectedRows.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsDataGridView.SelectedRows[0].Tag;
                ShowDocumentDetails(selectedDoc);
            }
        }

        private void ViewAttachmentsButton_Click(object sender, EventArgs e)
        {
            if (_documentsDataGridView.SelectedRows.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsDataGridView.SelectedRows[0].Tag;
                ShowDocumentAttachments(selectedDoc);
            }
        }

        private void ShowDocumentDetails(DocumentSimple document)
        {
            string details = "تفاصيل الوثيقة:\n\n";
            details += "رقم الوثيقة: " + document.DocumentNumber + "\n";
            details += "رقم الأضبارة: " + (document.FolderNumber ?? "غير محدد") + "\n";
            details += "الموضوع: " + document.Subject + "\n";
            details += "النوع: " + document.DocumentType + "\n";
            details += "القسم: " + document.DepartmentName + "\n";
            details += "التاريخ: " + document.DocumentDateFormatted + "\n";
            details += document.DynamicSenderReceiver + ": " + (document.SenderReceiver ?? "غير محدد") + "\n";
            details += "عدد المرفقات: " + document.AttachmentsCount + "\n";
            if (!string.IsNullOrEmpty(document.Notes))
                details += "الملاحظات: " + document.Notes + "\n";
            details += "تاريخ الإنشاء: " + document.CreatedDate.ToString("yyyy/MM/dd HH:mm") + "\n";
            details += "أنشأ بواسطة: " + document.CreatedBy + "\n";

            RTLHelper.ShowRTLMessageBox(details, "تفاصيل الوثيقة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowDocumentAttachments(DocumentSimple document)
        {
            try
            {
                var attachments = _dataManager.GetDocumentAttachments(document.DocumentId);

                if (attachments.Count == 0)
                {
                    RTLHelper.ShowRTLMessageBox("لا توجد مرفقات لهذه الوثيقة", "المرفقات",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                string attachmentsList = "مرفقات الوثيقة: " + document.DocumentNumber + "\n\n";
                foreach (var attachment in attachments)
                {
                    attachmentsList += attachment.FileTypeIcon + " " + attachment.FileName + "\n";
                    attachmentsList += "   الحجم: " + attachment.FileSizeFormatted + "\n";
                    attachmentsList += "   تاريخ الرفع: " + attachment.UploadDateFormatted + "\n\n";
                }

                RTLHelper.ShowRTLMessageBox(attachmentsList, "مرفقات الوثيقة",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في عرض المرفقات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PreviewFileButton_Click(object sender, EventArgs e)
        {
            if (_documentsDataGridView.SelectedRows.Count > 0)
            {
                var selectedDoc = (DocumentSimple)_documentsDataGridView.SelectedRows[0].Tag;

                try
                {
                    var attachments = _dataManager.GetDocumentAttachments(selectedDoc.DocumentId);

                    if (attachments.Count == 0)
                    {
                        RTLHelper.ShowRTLMessageBox("لا توجد مرفقات لهذه الوثيقة للمعاينة", "معاينة الملف",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    if (attachments.Count == 1)
                    {
                        // فتح الملف الوحيد مباشرة
                        OpenFileViewer(attachments[0].FilePath);
                    }
                    else
                    {
                        // عرض قائمة بالملفات للاختيار
                        ShowFileSelectionDialog(attachments);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في معاينة الملف: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void OpenFileViewer(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    RTLHelper.ShowRTLMessageBox("الملف غير موجود أو تم حذفه", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var viewerForm = new DocumentViewerForm(filePath);
                viewerForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح عارض الملفات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowFileSelectionDialog(List<DocumentAttachment> attachments)
        {
            // إنشاء نافذة بسيطة لاختيار الملف
            var selectionForm = new Form();
            selectionForm.Text = "اختيار ملف للمعاينة";
            selectionForm.Size = new Size(500, 400);
            selectionForm.StartPosition = FormStartPosition.CenterParent;
            selectionForm.RightToLeft = RightToLeft.Yes;
            selectionForm.Font = RTLHelper.ArabicFont;

            var listBox = new ListBox();
            listBox.Location = new Point(20, 20);
            listBox.Size = new Size(440, 280);
            listBox.RightToLeft = RightToLeft.Yes;
            listBox.Font = RTLHelper.ArabicFont;

            foreach (var attachment in attachments)
            {
                listBox.Items.Add(attachment.FileName);
                listBox.Tag = attachments; // حفظ قائمة المرفقات
            }

            var openButton = RTLHelper.CreateStyledButton(
                "فتح الملف المحدد",
                new Point(300, 320),
                new Size(120, 35),
                RTLHelper.SuccessColor,
                delegate
                {
                    if (listBox.SelectedIndex >= 0)
                    {
                        var selectedAttachment = attachments[listBox.SelectedIndex];
                        selectionForm.Close();
                        OpenFileViewer(selectedAttachment.FilePath);
                    }
                }
            );

            var cancelButton = RTLHelper.CreateStyledButton(
                "إلغاء",
                new Point(170, 320),
                new Size(120, 35),
                RTLHelper.SecondaryColor,
                delegate { selectionForm.Close(); }
            );

            selectionForm.Controls.Add(listBox);
            selectionForm.Controls.Add(openButton);
            selectionForm.Controls.Add(cancelButton);

            selectionForm.ShowDialog();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Name = "SimpleDocumentsForm";
            this.ResumeLayout(false);
        }
    }
}
