using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة عارض الصور
    /// </summary>
    public partial class ImageViewerForm : Form
    {
        private readonly string _imagePath;
        private PictureBox _pictureBox;
        private Panel _imagePanel;
        private float _zoomFactor = 1.0f;
        private Point _lastPanPoint;
        private bool _isPanning = false;

        public ImageViewerForm(string imagePath)
        {
            InitializeComponent();
            _imagePath = imagePath;
            InitializeControls();
            LoadImage();
        }

        private void InitializeControls()
        {
            this.Text = "عارض الصور - " + Path.GetFileName(_imagePath);
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.KeyPreview = true;

            CreateControls();
            SetupEventHandlers();
        }

        private void CreateControls()
        {
            // شريط الأدوات
            CreateToolbar();

            // منطقة عرض الصورة
            _imagePanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(37, 37, 38),
                AutoScroll = true
            };

            _pictureBox = new PictureBox
            {
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand
            };

            _imagePanel.Controls.Add(_pictureBox);
            this.Controls.Add(_imagePanel);
        }

        private void CreateToolbar()
        {
            var toolbar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(62, 62, 66)
            };

            // زر تكبير
            var zoomInButton = CreateToolbarButton("🔍+", "تكبير", new Point(10, 10));
            zoomInButton.Click += (s, e) => ZoomIn();
            toolbar.Controls.Add(zoomInButton);

            // زر تصغير
            var zoomOutButton = CreateToolbarButton("🔍-", "تصغير", new Point(80, 10));
            zoomOutButton.Click += (s, e) => ZoomOut();
            toolbar.Controls.Add(zoomOutButton);

            // زر الحجم الأصلي
            var actualSizeButton = CreateToolbarButton("1:1", "الحجم الأصلي", new Point(150, 10));
            actualSizeButton.Click += (s, e) => ActualSize();
            toolbar.Controls.Add(actualSizeButton);

            // زر ملء الشاشة
            var fitToWindowButton = CreateToolbarButton("⛶", "ملء النافذة", new Point(220, 10));
            fitToWindowButton.Click += (s, e) => FitToWindow();
            toolbar.Controls.Add(fitToWindowButton);

            // زر الدوران
            var rotateButton = CreateToolbarButton("↻", "دوران", new Point(290, 10));
            rotateButton.Click += (s, e) => RotateImage();
            toolbar.Controls.Add(rotateButton);

            // زر الحفظ
            var saveButton = CreateToolbarButton("💾", "حفظ", new Point(360, 10));
            saveButton.Click += (s, e) => SaveImage();
            toolbar.Controls.Add(saveButton);

            // زر الطباعة
            var printButton = CreateToolbarButton("🖨️", "طباعة", new Point(430, 10));
            printButton.Click += (s, e) => PrintImage();
            toolbar.Controls.Add(printButton);

            // معلومات الصورة
            var infoLabel = new Label
            {
                Location = new Point(600, 15),
                Size = new Size(300, 20),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 9F),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            if (File.Exists(_imagePath))
            {
                var fileInfo = new FileInfo(_imagePath);
                infoLabel.Text = "الحجم: " + FormatFileSize(fileInfo.Length) + " | " + Path.GetFileName(_imagePath);
            }
            
            toolbar.Controls.Add(infoLabel);

            this.Controls.Add(toolbar);
        }

        private Button CreateToolbarButton(string text, string tooltip, Point location)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(60, 30),
                BackColor = Color.FromArgb(104, 104, 104),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(62, 62, 66);

            // إضافة tooltip
            var toolTip = new ToolTip();
            toolTip.SetToolTip(button, tooltip);

            return button;
        }

        private void SetupEventHandlers()
        {
            _pictureBox.MouseDown += PictureBox_MouseDown;
            _pictureBox.MouseMove += PictureBox_MouseMove;
            _pictureBox.MouseUp += PictureBox_MouseUp;
            _pictureBox.MouseWheel += PictureBox_MouseWheel;
            this.KeyDown += ImageViewerForm_KeyDown;
        }

        private void LoadImage()
        {
            try
            {
                if (File.Exists(_imagePath))
                {
                    _pictureBox.Image = Image.FromFile(_imagePath);
                    FitToWindow();
                }
                else
                {
                    MessageBox.Show("الملف غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الصورة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void ZoomIn()
        {
            _zoomFactor *= 1.25f;
            ApplyZoom();
        }

        private void ZoomOut()
        {
            _zoomFactor /= 1.25f;
            ApplyZoom();
        }

        private void ActualSize()
        {
            _zoomFactor = 1.0f;
            ApplyZoom();
        }

        private void FitToWindow()
        {
            if (_pictureBox.Image != null)
            {
                var imageSize = _pictureBox.Image.Size;
                var panelSize = _imagePanel.ClientSize;

                float scaleX = (float)panelSize.Width / imageSize.Width;
                float scaleY = (float)panelSize.Height / imageSize.Height;
                _zoomFactor = Math.Min(scaleX, scaleY);

                ApplyZoom();
            }
        }

        private void ApplyZoom()
        {
            if (_pictureBox.Image != null)
            {
                var imageSize = _pictureBox.Image.Size;
                var newSize = new Size(
                    (int)(imageSize.Width * _zoomFactor),
                    (int)(imageSize.Height * _zoomFactor)
                );

                _pictureBox.Size = newSize;
                _pictureBox.SizeMode = PictureBoxSizeMode.Zoom;

                // توسيط الصورة
                _pictureBox.Location = new Point(
                    Math.Max(0, (_imagePanel.ClientSize.Width - newSize.Width) / 2),
                    Math.Max(0, (_imagePanel.ClientSize.Height - newSize.Height) / 2)
                );
            }
        }

        private void RotateImage()
        {
            if (_pictureBox.Image != null)
            {
                _pictureBox.Image.RotateFlip(RotateFlipType.Rotate90FlipNone);
                _pictureBox.Invalidate();
            }
        }

        private void SaveImage()
        {
            if (_pictureBox.Image != null)
            {
                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "PNG Image|*.png|JPEG Image|*.jpg|Bitmap Image|*.bmp";
                    saveFileDialog.FileName = Path.GetFileNameWithoutExtension(_imagePath);

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            _pictureBox.Image.Save(saveFileDialog.FileName);
                            MessageBox.Show("تم حفظ الصورة بنجاح", "تم الحفظ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("خطأ في حفظ الصورة: " + ex.Message, "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }

        private void PrintImage()
        {
            MessageBox.Show("وظيفة الطباعة قيد التطوير", "الطباعة", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void PictureBox_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _isPanning = true;
                _lastPanPoint = e.Location;
                _pictureBox.Cursor = Cursors.SizeAll;
            }
        }

        private void PictureBox_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isPanning)
            {
                var deltaX = e.X - _lastPanPoint.X;
                var deltaY = e.Y - _lastPanPoint.Y;

                _pictureBox.Location = new Point(
                    _pictureBox.Location.X + deltaX,
                    _pictureBox.Location.Y + deltaY
                );
            }
        }

        private void PictureBox_MouseUp(object sender, MouseEventArgs e)
        {
            _isPanning = false;
            _pictureBox.Cursor = Cursors.Hand;
        }

        private void PictureBox_MouseWheel(object sender, MouseEventArgs e)
        {
            if (e.Delta > 0)
                ZoomIn();
            else
                ZoomOut();
        }

        private void ImageViewerForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    this.Close();
                    break;
                case Keys.Add:
                case Keys.Oemplus:
                    ZoomIn();
                    break;
                case Keys.Subtract:
                case Keys.OemMinus:
                    ZoomOut();
                    break;
                case Keys.D1:
                    ActualSize();
                    break;
                case Keys.F:
                    FitToWindow();
                    break;
                case Keys.R:
                    RotateImage();
                    break;
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024)
                return bytes + " بايت";
            else if (bytes < 1024 * 1024)
                return (bytes / 1024.0).ToString("F1") + " كيلوبايت";
            else
                return (bytes / (1024.0 * 1024.0)).ToString("F1") + " ميجابايت";
        }
    }
}
