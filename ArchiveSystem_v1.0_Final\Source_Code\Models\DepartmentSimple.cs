using System;
using System.Collections.Generic;

namespace ArchiveSystem.Models
{
    public class DepartmentSimple
    {
        private int _departmentId;
        private string _departmentName;
        private string _description;
        private DateTime _createdDate;
        private bool _isActive;

        public int DepartmentId
        {
            get { return _departmentId; }
            set { _departmentId = value; }
        }

        public string DepartmentName
        {
            get { return _departmentName; }
            set { _departmentName = value; }
        }

        public string Description
        {
            get { return _description; }
            set { _description = value; }
        }

        public DateTime CreatedDate
        {
            get { return _createdDate; }
            set { _createdDate = value; }
        }

        public bool IsActive
        {
            get { return _isActive; }
            set { _isActive = value; }
        }

        public string StatusText 
        { 
            get { return IsActive ? "نشط" : "غير نشط"; }
        }
        
        public string CreatedDateFormatted 
        { 
            get { return CreatedDate.ToString("yyyy/MM/dd"); }
        }

        public DepartmentSimple()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
        }

        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrEmpty(DepartmentName) || DepartmentName.Trim().Length == 0)
                errors.Add("اسم القسم مطلوب");
            else if (DepartmentName.Length < 3)
                errors.Add("اسم القسم يجب أن يكون 3 أحرف على الأقل");
            else if (DepartmentName.Length > 100)
                errors.Add("اسم القسم يجب أن يكون أقل من 100 حرف");

            if (!string.IsNullOrEmpty(Description) && Description.Length > 500)
                errors.Add("الوصف يجب أن يكون أقل من 500 حرف");

            return errors.Count == 0;
        }

        public override string ToString()
        {
            return DepartmentName ?? "قسم جديد";
        }
    }
}
