# متطلبات النظام - نظام الأرشفة الإلكترونية v1.0

## 🖥️ متطلبات النظام الأساسية

### نظام التشغيل المطلوب:
- **Windows 7** (Service Pack 1 أو أحدث) ✅
- **Windows 8/8.1** ✅
- **Windows 10** (جميع الإصدارات) ✅
- **Windows 11** ✅

### متطلبات .NET Framework:
- **.NET Framework 4.5** أو أحدث
- يمكن تحميله مجاناً من موقع Microsoft الرسمي
- حجم التحميل: حوالي 50 MB

### متطلبات الأجهزة:

#### الحد الأدنى:
- **المعالج**: Intel Pentium 4 أو AMD Athlon 64
- **الذاكرة**: 1 GB RAM
- **مساحة القرص الصلب**: 100 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 بكسل
- **لوحة المفاتيح**: دعم اللغة العربية

#### المُوصى به:
- **المعالج**: Intel Core i3 أو AMD Ryzen 3 أو أحدث
- **الذاكرة**: 4 GB RAM أو أكثر
- **مساحة القرص الصلب**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1366x768 بكسل أو أعلى
- **لوحة المفاتيح**: لوحة مفاتيح عربية/إنجليزية

## 🔧 متطلبات إضافية

### الخطوط المطلوبة:
- **Segoe UI** (متوفر افتراضياً في Windows 7 وأحدث)
- **Tahoma** (متوفر افتراضياً في جميع إصدارات Windows)

### الصلاحيات المطلوبة:
- **صلاحيات القراءة والكتابة** في مجلد التطبيق
- **صلاحيات إنشاء المجلدات** لحفظ البيانات والأيقونات
- لا يتطلب صلاحيات المدير (Administrator) للتشغيل العادي

### اللغات المدعومة:
- **العربية** (الواجهة الرئيسية)
- **الإنجليزية** (رسائل النظام)
- دعم كامل لاتجاه النص من اليمين إلى اليسار (RTL)

## 📁 هيكل الملفات المطلوبة

```
ArchiveSystem/
├── ArchiveSystem.exe          # الملف التنفيذي الرئيسي
├── AppIcon.ico               # أيقونة التطبيق
├── Data/                     # مجلد قاعدة البيانات
│   ├── departments.xml       # بيانات الأقسام
│   └── documents.xml         # بيانات الوثائق
└── Icons/                    # مجلد الأيقونات
    ├── MainApp.ico
    ├── Departments.ico
    ├── Documents.ico
    ├── Add.ico
    ├── Edit.ico
    ├── Delete.ico
    ├── View.ico
    ├── Search.ico
    └── Settings.ico
```

## 🚀 تعليمات التثبيت

### التثبيت اليدوي:
1. **تحميل .NET Framework 4.5** إذا لم يكن مثبتاً
2. **استخراج** جميع ملفات التطبيق إلى مجلد منفصل
3. **تشغيل** ArchiveSystem.exe
4. **السماح** للتطبيق بإنشاء المجلدات المطلوبة

### التثبيت التلقائي:
1. **تشغيل** ملف setup.bat كمدير
2. **اتباع** التعليمات على الشاشة
3. **إعادة تشغيل** الجهاز إذا لزم الأمر

## ⚠️ ملاحظات مهمة

### للمستخدمين الجدد:
- التطبيق سينشئ قاعدة بيانات تجريبية عند التشغيل الأول
- جميع البيانات محفوظة محلياً على الجهاز
- لا يتطلب اتصال بالإنترنت للتشغيل

### للمطورين:
- الكود المصدري متوفر في مجلد Source_Code
- يمكن تجميع التطبيق باستخدام Visual Studio 2012 أو أحدث
- يمكن تجميعه باستخدام csc.exe من سطر الأوامر

### الأمان:
- التطبيق لا يجمع أي بيانات شخصية
- جميع البيانات محفوظة محلياً
- لا يتصل بأي خوادم خارجية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "التطبيق لا يبدأ":
- تأكد من تثبيت .NET Framework 4.5
- تأكد من وجود جميع الملفات المطلوبة
- تشغيل التطبيق كمدير

#### "النصوص العربية لا تظهر بشكل صحيح":
- تأكد من تثبيت خط Segoe UI
- تأكد من إعدادات اللغة في Windows
- إعادة تشغيل التطبيق

#### "لا يمكن حفظ البيانات":
- تأكد من صلاحيات الكتابة في مجلد التطبيق
- تأكد من وجود مساحة كافية على القرص الصلب
- تشغيل التطبيق كمدير

#### "الأيقونات لا تظهر":
- تأكد من وجود مجلد Icons
- تأكد من وجود جميع ملفات .ico
- إعادة تشغيل التطبيق

## 📞 الدعم التقني

### للحصول على المساعدة:
- راجع دليل المستخدم في مجلد Documentation
- تحقق من ملف README.md للمعلومات التفصيلية
- راجع ملف CHANGELOG.md لآخر التحديثات

### الإبلاغ عن المشاكل:
- وصف مفصل للمشكلة
- إصدار نظام التشغيل
- خطوات إعادة إنتاج المشكلة
- لقطات شاشة إن أمكن

---

**نظام الأرشفة الإلكترونية v1.0**  
**متوافق مع Windows 7, 8, 10, 11**  
**تم اختباره على جميع الأنظمة المدعومة** ✅
