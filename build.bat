@echo off
echo ========================================
echo    نظام الأرشفة الإلكترونية المحسن
echo    PIKA Enhanced Archive System
echo ========================================
echo    الإصدار المحسن مع:
echo    Enhanced Version with:
echo    - Collapsible Right Sidebar
echo    - Windows 7 Optimizations
echo    - Modern UI Design
echo    - Smooth Animations
echo ========================================
echo.

echo [1/4] التحقق من متطلبات البناء...
echo Checking build requirements...

REM التحقق من وجود MSBuild
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: MSBuild غير موجود. يرجى تثبيت Visual Studio أو Build Tools
    echo Error: MSBuild not found. Please install Visual Studio or Build Tools
    pause
    exit /b 1
)

echo [2/4] تنظيف المشروع...
echo Cleaning project...
msbuild WindowsFormsApp1.sln /t:Clean /p:Configuration=Release /p:Platform="Any CPU" /v:minimal

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في تنظيف المشروع
    echo Error cleaning project
    pause
    exit /b 1
)

echo [3/4] بناء المشروع...
echo Building project...
msbuild WindowsFormsApp1.sln /t:Build /p:Configuration=Release /p:Platform="Any CPU" /v:minimal

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    pause
    exit /b 1
)

echo [4/4] التحقق من الملفات المبنية...
echo Checking built files...

if not exist "bin\Release\نظام الأرشفة الإلكترونية.exe" (
    echo خطأ: ملف التطبيق غير موجود
    echo Error: Application file not found
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء المشروع بنجاح!
echo Project built successfully!
echo ========================================
echo.
echo الملفات المبنية في: bin\Release\
echo Built files in: bin\Release\
echo.
echo لتشغيل التطبيق:
echo To run the application:
echo cd bin\Release
echo "نظام الأرشفة الإلكترونية.exe"
echo.

pause
