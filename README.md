# نظام الأرشفة الإلكترونية

## وصف المشروع

نظام الأرشفة الإلكترونية هو تطبيق سطح مكتب شامل مطور بلغة C# باستخدام .NET Framework 4.8.1، مصمم لإدارة وأرشفة الوثائق الإلكترونية بطريقة منظمة وفعالة.

## المميزات الرئيسية

### 1. إدارة الأقسام والأضابير
- إنشاء وتعديل وحذف الأقسام
- إدارة الأضابير داخل كل قسم
- هيكلة منظمة للوثائق

### 2. إدارة الوثائق
- إضافة وثائق جديدة (صادرة ووارده)
- تعديل بيانات الوثائق
- البحث المتقدم في الوثائق
- تسلسل تلقائي للوثائق

### 3. إدارة المرفقات
- رفع ملفات متعددة لكل وثيقة
- دعم أنواع ملفات متنوعة (PDF, صور, مستندات)
- معاينة الملفات المرفقة
- عارض صور متقدم مع إمكانيات التكبير والتصغير

### 4. البحث والتصفية
- بحث سريع في جميع الوثائق
- تصفية حسب السنة، النوع، القسم، الأضبارة
- عرض نتائج البحث في جداول منظمة

### 5. الإحصائيات
- عرض إحصائيات شاملة على الصفحة الرئيسية
- عدد الأقسام والأضابير والوثائق
- إحصائيات الكتب الصادرة والواردة

## التقنيات المستخدمة

- **اللغة**: C#
- **الإطار**: .NET Framework 4.8.1
- **واجهة المستخدم**: Windows Forms
- **قاعدة البيانات**: SQLite
- **نمط التصميم**: Repository Pattern + MVVM

## هيكلة المشروع

```
ArchiveSystem/
├── Models/                 # نماذج البيانات
│   ├── Department.cs      # نموذج القسم
│   ├── Folder.cs         # نموذج الأضبارة
│   ├── Document.cs       # نموذج الوثيقة
│   └── Attachment.cs     # نموذج المرفق
├── Data/                  # طبقة الوصول للبيانات
│   ├── DatabaseHelper.cs # مساعد قاعدة البيانات
│   ├── DepartmentRepository.cs
│   ├── FolderRepository.cs
│   ├── DocumentRepository.cs
│   └── AttachmentRepository.cs
├── Services/              # الخدمات
│   ├── StatisticsService.cs
│   └── FileService.cs
├── Forms/                 # النوافذ
│   ├── MainForm.cs       # النافذة الرئيسية
│   ├── AddDocumentForm.cs
│   ├── ViewDocumentsForm.cs
│   ├── DocumentDetailsForm.cs
│   ├── DocumentAttachmentsForm.cs
│   ├── ImageViewerForm.cs
│   ├── DepartmentsForm.cs
│   ├── AddDepartmentForm.cs
│   ├── EditDepartmentForm.cs
│   ├── AddFolderForm.cs
│   ├── EditFolderForm.cs
│   ├── DepartmentFoldersForm.cs
│   └── EditDocumentForm.cs
└── Program.cs            # نقطة دخول التطبيق
```

## قاعدة البيانات

### الجداول الرئيسية

1. **Departments** - الأقسام
   - DepartmentId (مفتاح أساسي)
   - DepartmentName (اسم القسم)
   - CreatedDate (تاريخ الإنشاء)
   - Description (الوصف)
   - IsActive (نشط/غير نشط)

2. **Folders** - الأضابير
   - FolderId (مفتاح أساسي)
   - FolderName (اسم الأضبارة)
   - DepartmentId (مفتاح خارجي)
   - CreatedDate (تاريخ الإنشاء)
   - Description (الوصف)
   - IsActive (نشط/غير نشط)

3. **Documents** - الوثائق
   - DocumentId (مفتاح أساسي)
   - DocumentSequence (تسلسل الوثيقة)
   - EntryDate (تاريخ الإدخال)
   - DocumentNumber (رقم الوثيقة)
   - DocumentDate (تاريخ الوثيقة)
   - Subject (الموضوع)
   - DocumentType (نوع الوثيقة: صادر/وارد)
   - IssuedFrom (صادر من)
   - ReceivedTo (وارد إلى)
   - DepartmentId (مفتاح خارجي)
   - FolderId (مفتاح خارجي)
   - SaveSequence (تسلسل الحفظ)
   - IsActive (نشط/غير نشط)

4. **Attachments** - المرفقات
   - AttachmentId (مفتاح أساسي)
   - DocumentId (مفتاح خارجي)
   - FileName (اسم الملف)
   - FilePath (مسار الملف)
   - FileExtension (امتداد الملف)
   - FileSize (حجم الملف)
   - UploadDate (تاريخ الرفع)
   - FileType (نوع الملف)
   - IsActive (نشط/غير نشط)

## متطلبات التشغيل

- Windows 7 أو أحدث
- .NET Framework 4.8.1 أو أحدث
- 100 ميجابايت مساحة فارغة على القرص الصلب
- 2 جيجابايت ذاكرة وصول عشوائي (مُوصى به)

## التثبيت والتشغيل

1. تحميل المشروع من المستودع
2. فتح المشروع في Visual Studio
3. بناء المشروع (Build)
4. تشغيل التطبيق

## الاستخدام

### البدء السريع

1. **تشغيل التطبيق**: عند التشغيل الأول، سيتم إنشاء قاعدة البيانات تلقائياً
2. **إضافة قسم**: من الشريط الجانبي، اختر "الأقسام" ثم "إضافة قسم جديد"
3. **إضافة أضبارة**: داخل القسم، أضف أضابير لتنظيم الوثائق
4. **إضافة وثيقة**: من الصفحة الرئيسية، اضغط "إضافة وثيقة جديدة"
5. **رفع المرفقات**: أضف الملفات المرتبطة بالوثيقة

### البحث والتصفية

- استخدم حقل البحث السريع في الصفحة الرئيسية للبحث العام
- في صفحة "عرض الوثائق"، استخدم خيارات التصفية المتقدمة
- يمكن البحث في: رقم الوثيقة، الموضوع، الجهة، التاريخ

## الدعم والصيانة

### النسخ الاحتياطية
- يمكن إنشاء نسخ احتياطية من قاعدة البيانات
- النسخ الاحتياطية تُحفظ في مجلد "Backups"

### استكشاف الأخطاء
- تحقق من وجود ملف قاعدة البيانات "ArchiveDatabase.db"
- تأكد من صلاحيات الكتابة في مجلد التطبيق
- راجع ملفات السجل في حالة حدوث أخطاء

## المطور

تم تطوير هذا النظام بواسطة مساعد الذكي الاصطناعي Augment Agent، مع التركيز على:
- سهولة الاستخدام
- الأداء العالي
- التصميم العصري
- دعم اللغة العربية بالكامل

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة**: هذا النظام مصمم ليكون قابلاً للتوسع والتطوير، ويمكن إضافة المزيد من المميزات حسب الحاجة.
