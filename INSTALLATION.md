# دليل التثبيت - نظام الأرشفة الإلكترونية

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 7 SP1 أو أحدث
- **المعالج**: Intel Pentium 4 أو AMD Athlon 64
- **الذاكرة**: 1 جيجابايت RAM
- **المساحة**: 200 ميجابايت مساحة فارغة
- **.NET Framework**: 4.8.1 أو أحدث

### المُوصى به
- **نظام التشغيل**: Windows 10 أو Windows 11
- **المعالج**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة**: 4 جيجابايت RAM
- **المساحة**: 1 جيجابايت مساحة فارغة
- **الدقة**: 1366x768 أو أعلى

## خطوات التثبيت

### 1. تحضير البيئة

#### تثبيت .NET Framework
1. تحميل .NET Framework 4.8.1 من موقع Microsoft الرسمي
2. تشغيل ملف التثبيت كمدير (Run as Administrator)
3. اتباع تعليمات التثبيت
4. إعادة تشغيل الكمبيوتر إذا طُلب ذلك

#### تثبيت Visual Studio (للمطورين)
1. تحميل Visual Studio Community (مجاني)
2. اختيار حزمة تطوير .NET Desktop
3. تثبيت مكونات Windows Forms

### 2. تحميل المشروع

```bash
# استنساخ المستودع
git clone https://github.com/your-repo/archive-system.git

# أو تحميل ملف ZIP وفك الضغط
```

### 3. بناء المشروع

#### باستخدام Visual Studio
1. فتح ملف `WindowsFormsApp1.sln`
2. اختيار Configuration: `Release`
3. اختيار Platform: `Any CPU`
4. من قائمة Build، اختر `Build Solution`
5. التأكد من عدم وجود أخطاء في البناء

#### باستخدام Command Line
```cmd
# الانتقال إلى مجلد المشروع
cd path\to\archive-system

# بناء المشروع
msbuild WindowsFormsApp1.sln /p:Configuration=Release
```

### 4. تثبيت SQLite (إذا لزم الأمر)

المشروع يتضمن مكتبة SQLite، لكن في حالة وجود مشاكل:

1. تحميل System.Data.SQLite من NuGet
2. أو تحميل SQLite DLL من الموقع الرسمي
3. وضع الملفات في مجلد التطبيق

## التشغيل الأول

### 1. تشغيل التطبيق
```cmd
# من مجلد bin\Release
نظام الأرشفة الإلكترونية.exe
```

### 2. التهيئة التلقائية
- سيتم إنشاء قاعدة البيانات تلقائياً
- سيتم إنشاء المجلدات المطلوبة:
  - `Attachments` - للمرفقات
  - `Backups` - للنسخ الاحتياطية
- سيتم إدخال بيانات أولية (قسم وأضبارة افتراضية)

### 3. التحقق من التثبيت
- تأكد من ظهور النافذة الرئيسية
- تحقق من وجود الإحصائيات في الصفحة الرئيسية
- جرب إضافة قسم جديد للتأكد من عمل قاعدة البيانات

## إعداد قاعدة البيانات

### SQLite (افتراضي)
- لا يتطلب إعداد إضافي
- قاعدة البيانات تُحفظ في ملف `ArchiveDatabase.db`
- يمكن نسخ الملف لعمل نسخة احتياطية

### SQL Server Express (اختياري)
لاستخدام SQL Server بدلاً من SQLite:

1. تثبيت SQL Server Express
2. تعديل connection string في `App.config`
3. تشغيل سكريبت إنشاء قاعدة البيانات

```xml
<connectionStrings>
    <add name="ArchiveDatabase" 
         connectionString="Server=.\SQLEXPRESS;Database=ArchiveDB;Integrated Security=true;" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ "Missing .NET Framework"
**الحل**: تثبيت .NET Framework 4.8.1

#### 2. خطأ "SQLite DLL not found"
**الحل**: 
- نسخ ملفات SQLite إلى مجلد التطبيق
- تثبيت Visual C++ Redistributable

#### 3. خطأ "Access Denied" عند إنشاء قاعدة البيانات
**الحل**:
- تشغيل التطبيق كمدير
- تغيير مجلد التطبيق إلى مجلد المستخدم
- التأكد من صلاحيات الكتابة

#### 4. مشاكل في عرض النصوص العربية
**الحل**:
- التأكد من تثبيت خطوط عربية
- تحديث Windows
- تفعيل دعم اللغات في Windows

#### 5. بطء في الأداء
**الحل**:
- زيادة ذاكرة النظام
- تنظيف قاعدة البيانات
- إنشاء نسخة احتياطية وإعادة إنشاء قاعدة البيانات

## التحديث

### تحديث التطبيق
1. إنشاء نسخة احتياطية من قاعدة البيانات
2. نسخ مجلد المرفقات
3. تثبيت الإصدار الجديد
4. استعادة قاعدة البيانات والمرفقات

### ترقية قاعدة البيانات
```sql
-- مثال على سكريبت ترقية
ALTER TABLE Documents ADD COLUMN NewField TEXT;
UPDATE Documents SET NewField = 'DefaultValue';
```

## النشر

### للمستخدمين النهائيين
1. إنشاء installer باستخدام:
   - Visual Studio Installer Projects
   - Inno Setup
   - NSIS

2. تضمين المتطلبات:
   - .NET Framework
   - SQLite DLLs
   - Visual C++ Redistributable

### للشبكات
1. نشر التطبيق على خادم مشترك
2. إعداد قاعدة بيانات مركزية
3. تكوين صلاحيات المستخدمين

## الصيانة

### النسخ الاحتياطية
- إنشاء نسخة احتياطية يومية من قاعدة البيانات
- نسخ مجلد المرفقات دورياً
- اختبار استعادة النسخ الاحتياطية

### مراقبة الأداء
- مراقبة حجم قاعدة البيانات
- تنظيف الملفات المؤقتة
- فحص سلامة قاعدة البيانات

### التحديثات الأمنية
- تحديث .NET Framework
- تحديث نظام التشغيل
- مراجعة صلاحيات الملفات

## الدعم الفني

### معلومات مفيدة للدعم
- إصدار نظام التشغيل
- إصدار .NET Framework
- رسائل الخطأ كاملة
- خطوات إعادة إنتاج المشكلة

### ملفات السجل
- مجلد التطبيق: `Logs\`
- سجل Windows: Event Viewer
- سجل قاعدة البيانات: `Database.log`

---

**ملاحظة**: في حالة واجهت أي مشاكل في التثبيت، يرجى الرجوع إلى قسم استكشاف الأخطاء أو التواصل مع الدعم الفني.
