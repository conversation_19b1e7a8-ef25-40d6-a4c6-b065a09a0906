@echo off
chcp 65001 >nul
title نظام الأرشفة الإلكترونية - التثبيت التلقائي v1.0

echo.
echo ================================================================
echo                نظام الأرشفة الإلكترونية v1.0
echo                      التثبيت التلقائي
echo ================================================================
echo.

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم تشغيل المثبت بصلاحيات المدير
) else (
    echo ⚠️  تحذير: لم يتم تشغيل المثبت بصلاحيات المدير
    echo    قد تحتاج لصلاحيات إضافية لإكمال التثبيت
    echo.
    pause
)

echo.
echo 🔍 فحص متطلبات النظام...
echo.

:: التحقق من إصدار Windows
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo ✓ إصدار Windows: %VERSION%

:: التحقق من .NET Framework
echo 🔍 فحص .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ .NET Framework 4.5+ مثبت
) else (
    echo ❌ .NET Framework 4.5+ غير مثبت
    echo.
    echo يجب تثبيت .NET Framework 4.5 أو أحدث قبل المتابعة
    echo يمكنك تحميله من: https://dotnet.microsoft.com/download/dotnet-framework
    echo.
    pause
    exit /b 1
)

:: التحقق من مساحة القرص
echo 🔍 فحص مساحة القرص المتوفرة...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set FREESPACE=%%a
if %FREESPACE% GTR 104857600 (
    echo ✓ مساحة كافية متوفرة على القرص
) else (
    echo ⚠️  تحذير: مساحة القرص قد تكون غير كافية
)

echo.
echo 📁 إعداد مجلدات التطبيق...

:: إنشاء مجلد التطبيق في Program Files
set INSTALL_DIR=%ProgramFiles%\ArchiveSystem
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if %errorLevel% == 0 (
        echo ✓ تم إنشاء مجلد التطبيق: %INSTALL_DIR%
    ) else (
        echo ❌ فشل في إنشاء مجلد التطبيق
        set INSTALL_DIR=%USERPROFILE%\Desktop\ArchiveSystem
        mkdir "%INSTALL_DIR%" 2>nul
        echo ✓ تم إنشاء مجلد التطبيق على سطح المكتب: %INSTALL_DIR%
    )
) else (
    echo ✓ مجلد التطبيق موجود: %INSTALL_DIR%
)

echo.
echo 📋 نسخ ملفات التطبيق...

:: نسخ الملف التنفيذي
copy "Application\ArchiveSystem.exe" "%INSTALL_DIR%\" >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم نسخ الملف التنفيذي
) else (
    echo ❌ فشل في نسخ الملف التنفيذي
    goto :error
)

:: نسخ الأيقونة الرئيسية
copy "Application\AppIcon.ico" "%INSTALL_DIR%\" >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم نسخ الأيقونة الرئيسية
) else (
    echo ⚠️  تحذير: فشل في نسخ الأيقونة الرئيسية
)

:: نسخ مجلد البيانات
xcopy "Application\Data" "%INSTALL_DIR%\Data\" /E /I /Y >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم نسخ مجلد البيانات
) else (
    echo ❌ فشل في نسخ مجلد البيانات
    goto :error
)

:: نسخ مجلد الأيقونات
xcopy "Application\Icons" "%INSTALL_DIR%\Icons\" /E /I /Y >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم نسخ مجلد الأيقونات
) else (
    echo ❌ فشل في نسخ مجلد الأيقونات
    goto :error
)

echo.
echo 🔗 إنشاء اختصارات...

:: إنشاء اختصار على سطح المكتب
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\نظام الأرشفة الإلكترونية.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%INSTALL_DIR%\ArchiveSystem.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> CreateShortcut.vbs
echo oLink.IconLocation = "%INSTALL_DIR%\AppIcon.ico" >> CreateShortcut.vbs
echo oLink.Description = "نظام الأرشفة الإلكترونية المتقدم" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم إنشاء اختصار على سطح المكتب
) else (
    echo ⚠️  تحذير: فشل في إنشاء اختصار سطح المكتب
)

del CreateShortcut.vbs >nul 2>&1

:: إنشاء اختصار في قائمة ابدأ
set STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%STARTMENU%\نظام الأرشفة الإلكترونية.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%INSTALL_DIR%\ArchiveSystem.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> CreateShortcut.vbs
echo oLink.IconLocation = "%INSTALL_DIR%\AppIcon.ico" >> CreateShortcut.vbs
echo oLink.Description = "نظام الأرشفة الإلكترونية المتقدم" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم إنشاء اختصار في قائمة ابدأ
) else (
    echo ⚠️  تحذير: فشل في إنشاء اختصار قائمة ابدأ
)

del CreateShortcut.vbs >nul 2>&1

echo.
echo 🧪 اختبار التطبيق...

:: اختبار تشغيل التطبيق
"%INSTALL_DIR%\ArchiveSystem.exe" --test >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ التطبيق جاهز للتشغيل
) else (
    echo ⚠️  تحذير: قد تكون هناك مشكلة في التطبيق
)

echo.
echo ================================================================
echo                    ✅ تم التثبيت بنجاح!
echo ================================================================
echo.
echo 📍 مسار التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: نظام الأرشفة الإلكترونية
echo 📋 اختصار قائمة ابدأ: نظام الأرشفة الإلكترونية
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق من:
echo    - النقر المزدوج على اختصار سطح المكتب
echo    - البحث عن "نظام الأرشفة" في قائمة ابدأ
echo    - تشغيل الملف مباشرة من: %INSTALL_DIR%\ArchiveSystem.exe
echo.
echo 📖 للمساعدة والدعم، راجع ملف README.md في مجلد Documentation
echo.

set /p LAUNCH="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%LAUNCH%"=="y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    start "" "%INSTALL_DIR%\ArchiveSystem.exe"
)

echo.
echo شكراً لاستخدام نظام الأرشفة الإلكترونية!
echo.
pause
exit /b 0

:error
echo.
echo ================================================================
echo                    ❌ فشل في التثبيت!
echo ================================================================
echo.
echo حدث خطأ أثناء عملية التثبيت. يرجى:
echo 1. التأكد من تشغيل المثبت بصلاحيات المدير
echo 2. التأكد من وجود مساحة كافية على القرص
echo 3. التأكد من عدم تشغيل التطبيق حالياً
echo 4. إعادة المحاولة
echo.
echo للمساعدة، راجع ملف SYSTEM_REQUIREMENTS.md
echo.
pause
exit /b 1
