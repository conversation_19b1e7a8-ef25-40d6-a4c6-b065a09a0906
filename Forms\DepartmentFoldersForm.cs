using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة أضابير القسم
    /// </summary>
    public partial class DepartmentFoldersForm : Form
    {
        private readonly Department _department;
        private readonly FolderRepository _folderRepository;
        private readonly DocumentRepository _documentRepository;

        private FlowLayoutPanel _foldersPanel;

        public DepartmentFoldersForm(Department department)
        {
            InitializeComponent();
            _department = department;
            _folderRepository = new FolderRepository();
            _documentRepository = new DocumentRepository();
            InitializeControls();
            LoadFolders();
        }

        private void InitializeControls()
        {
            this.Text = "أضابير القسم - " + _department.DepartmentName;
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 242, 247);

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "أضابير القسم: " + _department.DepartmentName,
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(50, 30),
                Size = new Size(400, 40),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // زر إضافة أضبارة جديدة
            var addButton = new Button
            {
                Text = "➕ إضافة أضبارة جديدة",
                Location = new Point(50, 80),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += AddButton_Click;
            this.Controls.Add(addButton);

            // زر العودة
            var backButton = new Button
            {
                Text = "🔙 العودة",
                Location = new Point(270, 80),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            backButton.Click += (s, e) => this.Close();
            this.Controls.Add(backButton);

            // منطقة عرض الأضابير
            _foldersPanel = new FlowLayoutPanel
            {
                Location = new Point(50, 140),
                Size = new Size(900, 500),
                AutoScroll = true,
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = true,
                BackColor = Color.Transparent
            };
            this.Controls.Add(_foldersPanel);
        }

        private void LoadFolders()
        {
            try
            {
                _foldersPanel.Controls.Clear();
                var folders = _folderRepository.GetByDepartmentId(_department.DepartmentId);

                foreach (var folder in folders)
                {
                    var folderCard = CreateFolderCard(folder);
                    _foldersPanel.Controls.Add(folderCard);
                }

                if (folders.Count == 0)
                {
                    var noDataLabel = new Label
                    {
                        Text = "لا توجد أضابير في هذا القسم",
                        Font = new Font("Tahoma", 14F, FontStyle.Bold),
                        ForeColor = Color.FromArgb(127, 140, 141),
                        Size = new Size(300, 50),
                        TextAlign = ContentAlignment.MiddleCenter
                    };
                    _foldersPanel.Controls.Add(noDataLabel);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الأضابير: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Panel CreateFolderCard(Folder folder)
        {
            var card = new Panel
            {
                Size = new Size(280, 180),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Margin = new Padding(10)
            };

            // إضافة ظل للبطاقة
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width, card.Height);
                using (var brush = new SolidBrush(Color.FromArgb(230, 230, 230)))
                {
                    e.Graphics.FillRectangle(brush, rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                }
                using (var brush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(brush, rect);
                }
                using (var pen = new Pen(Color.FromArgb(220, 220, 220)))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            // أيقونة الأضبارة
            var iconLabel = new Label
            {
                Text = "📂",
                Font = new Font("Segoe UI Emoji", 20F),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(15, 15),
                Size = new Size(40, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };
            card.Controls.Add(iconLabel);

            // اسم الأضبارة
            var nameLabel = new Label
            {
                Text = folder.FolderName,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(70, 15),
                Size = new Size(190, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            card.Controls.Add(nameLabel);

            // تاريخ الإنشاء
            var dateLabel = new Label
            {
                Text = "تاريخ الإنشاء: " + folder.CreatedDate.ToString("yyyy/MM/dd"),
                Font = new Font("Tahoma", 8F),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(70, 40),
                Size = new Size(190, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            card.Controls.Add(dateLabel);

            // الوصف
            if (!string.IsNullOrEmpty(folder.Description))
            {
                var descLabel = new Label
                {
                    Text = folder.Description.Length > 40 ? 
                           folder.Description.Substring(0, 40) + "..." : 
                           folder.Description,
                    Font = new Font("Tahoma", 8F),
                    ForeColor = Color.FromArgb(127, 140, 141),
                    Location = new Point(15, 70),
                    Size = new Size(245, 35),
                    TextAlign = ContentAlignment.TopRight
                };
                card.Controls.Add(descLabel);
            }

            // إحصائية الوثائق
            var documentsCount = _documentRepository.Search("", null, folder.FolderId).Count;
            var statsLabel = new Label
            {
                Text = "عدد الوثائق: " + documentsCount,
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(15, 110),
                Size = new Size(245, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            card.Controls.Add(statsLabel);

            // أزرار الإجراءات
            var openButton = new Button
            {
                Text = "فتح",
                Location = new Point(15, 140),
                Size = new Size(50, 25),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 8F, FontStyle.Bold)
            };
            openButton.Click += (s, e) => OpenFolder(folder);
            card.Controls.Add(openButton);

            var editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(75, 140),
                Size = new Size(50, 25),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 8F, FontStyle.Bold)
            };
            editButton.Click += (s, e) => EditFolder(folder);
            card.Controls.Add(editButton);

            var deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(135, 140),
                Size = new Size(50, 25),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 8F, FontStyle.Bold)
            };
            deleteButton.Click += (s, e) => DeleteFolder(folder);
            card.Controls.Add(deleteButton);

            return card;
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            var addForm = new AddFolderForm(_department.DepartmentId);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadFolders();
            }
        }

        private void OpenFolder(Folder folder)
        {
            // فتح نافذة عرض الوثائق مع تصفية حسب الأضبارة
            var documentsForm = new ViewDocumentsForm();
            documentsForm.ShowDialog();
        }

        private void EditFolder(Folder folder)
        {
            var editForm = new EditFolderForm(folder);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadFolders();
            }
        }

        private void DeleteFolder(Folder folder)
        {
            try
            {
                // التحقق من إمكانية الحذف
                if (!_folderRepository.CanDelete(folder.FolderId))
                {
                    MessageBox.Show("لا يمكن حذف هذه الأضبارة لأنها تحتوي على وثائق مرتبطة بها", 
                                  "لا يمكن الحذف", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف الأضبارة '" + folder.FolderName + "'؟",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (_folderRepository.Delete(folder.FolderId))
                    {
                        MessageBox.Show("تم حذف الأضبارة بنجاح", "تم الحذف", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadFolders();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الأضبارة", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حذف الأضبارة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
