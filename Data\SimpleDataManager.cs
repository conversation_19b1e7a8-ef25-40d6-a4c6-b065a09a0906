using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Windows.Forms;
using ArchiveSystem.Models;

namespace ArchiveSystem.Data
{
    public class SimpleDataManager
    {
        private static SimpleDataManager _instance;
        private string _dataPath;
        private string _departmentsFile;
        private string _documentsFile;

        public static SimpleDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SimpleDataManager();
                }
                return _instance;
            }
        }

        private SimpleDataManager()
        {
            InitializeData();
        }

        private void InitializeData()
        {
            _dataPath = Path.Combine(Application.StartupPath, "Data");
            _departmentsFile = Path.Combine(_dataPath, "departments.xml");
            _documentsFile = Path.Combine(_dataPath, "documents.xml");

            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }

            if (!File.Exists(_departmentsFile))
            {
                CreateDepartmentsFile();
            }

            if (!File.Exists(_documentsFile))
            {
                CreateDocumentsFile();
            }
        }

        private void CreateDepartmentsFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Departments");
            doc.AppendChild(root);

            // إضافة أقسام افتراضية
            var departments = new[]
            {
                new { Id = 1, Name = "الإدارة العامة", Description = "القسم الرئيسي للإدارة العامة والتنسيق" },
                new { Id = 2, Name = "الشؤون المالية", Description = "قسم الشؤون المالية والمحاسبة والميزانية" },
                new { Id = 3, Name = "الموارد البشرية", Description = "قسم الموارد البشرية والتوظيف والتدريب" },
                new { Id = 4, Name = "تقنية المعلومات", Description = "قسم تقنية المعلومات والدعم التقني" },
                new { Id = 5, Name = "الشؤون القانونية", Description = "قسم الشؤون القانونية والاستشارات القانونية" }
            };

            foreach (var dept in departments)
            {
                var element = doc.CreateElement("Department");
                element.SetAttribute("Id", dept.Id.ToString());
                element.SetAttribute("Name", dept.Name);
                element.SetAttribute("Description", dept.Description);
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("IsActive", "true");
                root.AppendChild(element);
            }

            doc.Save(_departmentsFile);
        }

        private void CreateDocumentsFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Documents");
            doc.AppendChild(root);

            // إضافة وثائق تجريبية
            var documents = new[]
            {
                new { Id = 1, Number = "001", Subject = "كتاب تعميم جديد", Type = "صادر", DepartmentId = 1, SenderReceiver = "جميع الأقسام" },
                new { Id = 2, Number = "002", Subject = "طلب معلومات مالية", Type = "وارد", DepartmentId = 2, SenderReceiver = "وزارة المالية" },
                new { Id = 3, Number = "003", Subject = "تقرير التوظيف الشهري", Type = "صادر", DepartmentId = 3, SenderReceiver = "الإدارة العليا" },
                new { Id = 4, Number = "004", Subject = "استفسار قانوني", Type = "وارد", DepartmentId = 5, SenderReceiver = "المحكمة الإدارية" },
                new { Id = 5, Number = "005", Subject = "خطة المشاريع التقنية", Type = "صادر", DepartmentId = 4, SenderReceiver = "لجنة التقنية" },
                new { Id = 6, Number = "006", Subject = "طلب ميزانية إضافية", Type = "وارد", DepartmentId = 2, SenderReceiver = "وزارة التخطيط" },
                new { Id = 7, Number = "007", Subject = "تقرير الأداء السنوي", Type = "صادر", DepartmentId = 1, SenderReceiver = "مجلس الإدارة" },
                new { Id = 8, Number = "008", Subject = "دعوة لحضور ورشة عمل", Type = "وارد", DepartmentId = 3, SenderReceiver = "معهد التدريب" },
                new { Id = 9, Number = "009", Subject = "تحديث أنظمة الحاسوب", Type = "صادر", DepartmentId = 4, SenderReceiver = "شركة التقنية" },
                new { Id = 10, Number = "010", Subject = "مراجعة العقود القانونية", Type = "وارد", DepartmentId = 5, SenderReceiver = "مكتب المحاماة" }
            };

            foreach (var document in documents)
            {
                var element = doc.CreateElement("Document");
                element.SetAttribute("Id", document.Id.ToString());
                element.SetAttribute("Number", document.Number);
                element.SetAttribute("Subject", document.Subject);
                element.SetAttribute("Type", document.Type);
                element.SetAttribute("DepartmentId", document.DepartmentId.ToString());
                element.SetAttribute("SenderReceiver", document.SenderReceiver);
                element.SetAttribute("DocumentDate", DateTime.Now.AddDays(-document.Id).ToString("yyyy-MM-dd"));
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("CreatedBy", "النظام");
                element.SetAttribute("IsActive", "true");
                element.SetAttribute("AttachmentsCount", "0");
                root.AppendChild(element);
            }

            doc.Save(_documentsFile);
        }

        public List<DepartmentSimple> GetDepartments()
        {
            var departments = new List<DepartmentSimple>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                foreach (XmlNode node in doc.SelectNodes("//Department[@IsActive='true']"))
                {
                    var department = new DepartmentSimple
                    {
                        DepartmentId = int.Parse(node.Attributes["Id"].Value),
                        DepartmentName = node.Attributes["Name"].Value,
                        Description = node.Attributes["Description"].Value,
                        CreatedDate = DateTime.Parse(node.Attributes["CreatedDate"].Value),
                        IsActive = bool.Parse(node.Attributes["IsActive"].Value)
                    };
                    departments.Add(department);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة الأقسام: " + ex.Message);
            }

            return departments;
        }

        public bool SaveDepartment(DepartmentSimple department)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                if (department.DepartmentId == 0)
                {
                    // إضافة قسم جديد
                    department.DepartmentId = GetNextId(doc, "Department");
                    
                    var element = doc.CreateElement("Department");
                    element.SetAttribute("Id", department.DepartmentId.ToString());
                    element.SetAttribute("Name", department.DepartmentName);
                    element.SetAttribute("Description", department.Description ?? "");
                    element.SetAttribute("CreatedDate", department.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    element.SetAttribute("IsActive", department.IsActive.ToString().ToLower());
                    
                    doc.DocumentElement.AppendChild(element);
                }
                else
                {
                    // تحديث قسم موجود
                    var node = doc.SelectSingleNode("//Department[@Id='" + department.DepartmentId + "']");
                    if (node != null)
                    {
                        node.Attributes["Name"].Value = department.DepartmentName;
                        node.Attributes["Description"].Value = department.Description ?? "";
                        node.Attributes["IsActive"].Value = department.IsActive.ToString().ToLower();
                    }
                }

                doc.Save(_departmentsFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteDepartment(int departmentId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                var node = doc.SelectSingleNode("//Department[@Id='" + departmentId + "']");
                if (node != null)
                {
                    // حذف منطقي
                    node.Attributes["IsActive"].Value = "false";
                    doc.Save(_departmentsFile);
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        public int GetDepartmentsCount()
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);
                return doc.SelectNodes("//Department[@IsActive='true']").Count;
            }
            catch
            {
                return 0;
            }
        }

        public int GetFoldersCount()
        {
            return 8; // قيمة ثابتة للآن
        }

        public int GetDocumentsCount(string type)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@IsActive='true']";
                if (!string.IsNullOrEmpty(type))
                {
                    xpath += "[@Type='" + type + "']";
                }

                return doc.SelectNodes(xpath).Count;
            }
            catch
            {
                return 0;
            }
        }

        public bool DepartmentNameExists(string name, int excludeId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);
                
                string xpath = "//Department[@Name='" + name + "' and @IsActive='true']";
                if (excludeId > 0)
                {
                    xpath += " and @Id!='" + excludeId + "'";
                }
                
                return doc.SelectSingleNode(xpath) != null;
            }
            catch
            {
                return false;
            }
        }

        private int GetNextId(XmlDocument doc, string elementName)
        {
            int maxId = 0;
            foreach (XmlNode node in doc.SelectNodes("//" + elementName))
            {
                int id;
                if (int.TryParse(node.Attributes["Id"].Value, out id))
                {
                    if (id > maxId) maxId = id;
                }
            }
            return maxId + 1;
        }

        public List<DocumentSimple> GetDocuments(string typeFilter = null, string searchText = null)
        {
            var documents = new List<DocumentSimple>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@IsActive='true']";

                foreach (XmlNode node in doc.SelectNodes(xpath))
                {
                    var document = new DocumentSimple
                    {
                        DocumentId = int.Parse(node.Attributes["Id"].Value),
                        DocumentNumber = node.Attributes["Number"].Value,
                        Subject = node.Attributes["Subject"].Value,
                        DocumentType = node.Attributes["Type"].Value,
                        DepartmentId = int.Parse(node.Attributes["DepartmentId"].Value),
                        SenderReceiver = node.Attributes["SenderReceiver"] != null ? node.Attributes["SenderReceiver"].Value : "",
                        DocumentDate = DateTime.Parse(node.Attributes["DocumentDate"].Value),
                        CreatedDate = DateTime.Parse(node.Attributes["CreatedDate"].Value),
                        CreatedBy = node.Attributes["CreatedBy"].Value,
                        IsActive = bool.Parse(node.Attributes["IsActive"].Value),
                        AttachmentsCount = node.Attributes["AttachmentsCount"] != null ? int.Parse(node.Attributes["AttachmentsCount"].Value) : 0
                    };

                    // تطبيق الفلاتر
                    bool includeDocument = true;

                    if (!string.IsNullOrEmpty(typeFilter) && document.DocumentType != typeFilter)
                        includeDocument = false;

                    if (!string.IsNullOrEmpty(searchText))
                    {
                        string search = searchText.ToLower();
                        if (!document.Subject.ToLower().Contains(search) &&
                            !document.DocumentNumber.ToLower().Contains(search) &&
                            !(document.SenderReceiver ?? "").ToLower().Contains(search))
                            includeDocument = false;
                    }

                    if (includeDocument)
                    {
                        // الحصول على اسم القسم
                        document.DepartmentName = GetDepartmentName(document.DepartmentId);
                        documents.Add(document);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة الوثائق: " + ex.Message);
            }

            return documents;
        }

        private string GetDepartmentName(int departmentId)
        {
            try
            {
                var departments = GetDepartments();
                foreach (var dept in departments)
                {
                    if (dept.DepartmentId == departmentId)
                        return dept.DepartmentName;
                }
                return "غير محدد";
            }
            catch
            {
                return "غير محدد";
            }
        }

        public bool SaveDocument(DocumentSimple document)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                if (document.DocumentId == 0)
                {
                    // إضافة وثيقة جديدة
                    document.DocumentId = GetNextId(doc, "Document");

                    var element = doc.CreateElement("Document");
                    element.SetAttribute("Id", document.DocumentId.ToString());
                    element.SetAttribute("Number", document.DocumentNumber);
                    element.SetAttribute("Subject", document.Subject);
                    element.SetAttribute("Type", document.DocumentType);
                    element.SetAttribute("DepartmentId", document.DepartmentId.ToString());
                    element.SetAttribute("SenderReceiver", document.SenderReceiver ?? "");
                    element.SetAttribute("DocumentDate", document.DocumentDate.ToString("yyyy-MM-dd"));
                    element.SetAttribute("CreatedDate", document.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    element.SetAttribute("CreatedBy", document.CreatedBy);
                    element.SetAttribute("IsActive", document.IsActive.ToString().ToLower());
                    element.SetAttribute("AttachmentsCount", document.AttachmentsCount.ToString());

                    doc.DocumentElement.AppendChild(element);
                }
                else
                {
                    // تحديث وثيقة موجودة
                    var node = doc.SelectSingleNode("//Document[@Id='" + document.DocumentId + "']");
                    if (node != null)
                    {
                        node.Attributes["Number"].Value = document.DocumentNumber;
                        node.Attributes["Subject"].Value = document.Subject;
                        node.Attributes["Type"].Value = document.DocumentType;
                        node.Attributes["DepartmentId"].Value = document.DepartmentId.ToString();
                        node.Attributes["SenderReceiver"].Value = document.SenderReceiver ?? "";
                        node.Attributes["DocumentDate"].Value = document.DocumentDate.ToString("yyyy-MM-dd");
                        node.Attributes["IsActive"].Value = document.IsActive.ToString().ToLower();
                    }
                }

                doc.Save(_documentsFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteDocument(int documentId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                var node = doc.SelectSingleNode("//Document[@Id='" + documentId + "']");
                if (node != null)
                {
                    // حذف منطقي
                    node.Attributes["IsActive"].Value = "false";
                    doc.Save(_documentsFile);
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        public bool DocumentNumberExists(string number, string type, int year, int excludeId = 0)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@Number='" + number + "' and @Type='" + type + "' and @IsActive='true']";
                if (excludeId > 0)
                {
                    xpath += " and @Id!='" + excludeId + "'";
                }

                var nodes = doc.SelectNodes(xpath);
                foreach (XmlNode node in nodes)
                {
                    DateTime docDate = DateTime.Parse(node.Attributes["DocumentDate"].Value);
                    if (docDate.Year == year)
                        return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
