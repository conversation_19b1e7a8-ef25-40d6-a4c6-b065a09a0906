using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Windows.Forms;
using ArchiveSystem.Models;

namespace ArchiveSystem.Data
{
    public class SimpleDataManager
    {
        private static SimpleDataManager _instance;
        private string _dataPath;
        private string _departmentsFile;
        private string _documentsFile;

        public static SimpleDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SimpleDataManager();
                }
                return _instance;
            }
        }

        private SimpleDataManager()
        {
            InitializeData();
        }

        private void InitializeData()
        {
            _dataPath = Path.Combine(Application.StartupPath, "Data");
            _departmentsFile = Path.Combine(_dataPath, "departments.xml");
            _documentsFile = Path.Combine(_dataPath, "documents.xml");

            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }

            if (!File.Exists(_departmentsFile))
            {
                CreateDepartmentsFile();
            }

            if (!File.Exists(_documentsFile))
            {
                CreateDocumentsFile();
            }
        }

        private void CreateDepartmentsFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Departments");
            doc.AppendChild(root);

            // إضافة أقسام افتراضية
            var departments = new[]
            {
                new { Id = 1, Name = "الإدارة العامة", Description = "القسم الرئيسي للإدارة العامة والتنسيق" },
                new { Id = 2, Name = "الشؤون المالية", Description = "قسم الشؤون المالية والمحاسبة والميزانية" },
                new { Id = 3, Name = "الموارد البشرية", Description = "قسم الموارد البشرية والتوظيف والتدريب" },
                new { Id = 4, Name = "تقنية المعلومات", Description = "قسم تقنية المعلومات والدعم التقني" },
                new { Id = 5, Name = "الشؤون القانونية", Description = "قسم الشؤون القانونية والاستشارات القانونية" }
            };

            foreach (var dept in departments)
            {
                var element = doc.CreateElement("Department");
                element.SetAttribute("Id", dept.Id.ToString());
                element.SetAttribute("Name", dept.Name);
                element.SetAttribute("Description", dept.Description);
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("IsActive", "true");
                root.AppendChild(element);
            }

            doc.Save(_departmentsFile);
        }

        private void CreateDocumentsFile()
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Documents");
            doc.AppendChild(root);

            // إضافة وثائق تجريبية
            var documents = new[]
            {
                new { Id = 1, Number = "001", Subject = "كتاب تعميم جديد", Type = "صادر", DepartmentId = 1, SenderReceiver = "جميع الأقسام", FolderNumber = "2024-001" },
                new { Id = 2, Number = "002", Subject = "طلب معلومات مالية", Type = "وارد", DepartmentId = 2, SenderReceiver = "وزارة المالية", FolderNumber = "2024-002" },
                new { Id = 3, Number = "003", Subject = "تقرير التوظيف الشهري", Type = "صادر", DepartmentId = 3, SenderReceiver = "الإدارة العليا", FolderNumber = "2024-003" },
                new { Id = 4, Number = "004", Subject = "استفسار قانوني", Type = "وارد", DepartmentId = 5, SenderReceiver = "المحكمة الإدارية", FolderNumber = "2024-004" },
                new { Id = 5, Number = "005", Subject = "خطة المشاريع التقنية", Type = "صادر", DepartmentId = 4, SenderReceiver = "لجنة التقنية", FolderNumber = "2024-005" },
                new { Id = 6, Number = "006", Subject = "طلب ميزانية إضافية", Type = "وارد", DepartmentId = 2, SenderReceiver = "وزارة التخطيط", FolderNumber = "2024-006" },
                new { Id = 7, Number = "007", Subject = "تقرير الأداء السنوي", Type = "صادر", DepartmentId = 1, SenderReceiver = "مجلس الإدارة", FolderNumber = "2024-007" },
                new { Id = 8, Number = "008", Subject = "دعوة لحضور ورشة عمل", Type = "وارد", DepartmentId = 3, SenderReceiver = "معهد التدريب", FolderNumber = "2024-008" },
                new { Id = 9, Number = "009", Subject = "تحديث أنظمة الحاسوب", Type = "صادر", DepartmentId = 4, SenderReceiver = "شركة التقنية", FolderNumber = "2024-009" },
                new { Id = 10, Number = "010", Subject = "مراجعة العقود القانونية", Type = "وارد", DepartmentId = 5, SenderReceiver = "مكتب المحاماة", FolderNumber = "2024-010" }
            };

            foreach (var document in documents)
            {
                var element = doc.CreateElement("Document");
                element.SetAttribute("Id", document.Id.ToString());
                element.SetAttribute("Number", document.Number);
                element.SetAttribute("Subject", document.Subject);
                element.SetAttribute("Type", document.Type);
                element.SetAttribute("DepartmentId", document.DepartmentId.ToString());
                element.SetAttribute("FolderNumber", document.FolderNumber);
                element.SetAttribute("SenderReceiver", document.SenderReceiver);
                element.SetAttribute("DocumentDate", DateTime.Now.AddDays(-document.Id).ToString("yyyy-MM-dd"));
                element.SetAttribute("CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                element.SetAttribute("CreatedBy", "النظام");
                element.SetAttribute("IsActive", "true");
                root.AppendChild(element);
            }

            doc.Save(_documentsFile);
        }

        public List<DepartmentSimple> GetDepartments()
        {
            var departments = new List<DepartmentSimple>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                foreach (XmlNode node in doc.SelectNodes("//Department[@IsActive='true']"))
                {
                    var department = new DepartmentSimple
                    {
                        DepartmentId = int.Parse(node.Attributes["Id"].Value),
                        DepartmentName = node.Attributes["Name"].Value,
                        Description = node.Attributes["Description"].Value,
                        CreatedDate = DateTime.Parse(node.Attributes["CreatedDate"].Value),
                        IsActive = bool.Parse(node.Attributes["IsActive"].Value)
                    };
                    departments.Add(department);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة الأقسام: " + ex.Message);
            }

            return departments;
        }

        public bool SaveDepartment(DepartmentSimple department)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                if (department.DepartmentId == 0)
                {
                    // إضافة قسم جديد
                    department.DepartmentId = GetNextId(doc, "Department");
                    
                    var element = doc.CreateElement("Department");
                    element.SetAttribute("Id", department.DepartmentId.ToString());
                    element.SetAttribute("Name", department.DepartmentName);
                    element.SetAttribute("Description", department.Description ?? "");
                    element.SetAttribute("CreatedDate", department.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    element.SetAttribute("IsActive", department.IsActive.ToString().ToLower());
                    
                    doc.DocumentElement.AppendChild(element);
                }
                else
                {
                    // تحديث قسم موجود
                    var node = doc.SelectSingleNode("//Department[@Id='" + department.DepartmentId + "']");
                    if (node != null)
                    {
                        node.Attributes["Name"].Value = department.DepartmentName;
                        node.Attributes["Description"].Value = department.Description ?? "";
                        node.Attributes["IsActive"].Value = department.IsActive.ToString().ToLower();
                    }
                }

                doc.Save(_departmentsFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteDepartment(int departmentId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);

                var node = doc.SelectSingleNode("//Department[@Id='" + departmentId + "']");
                if (node != null)
                {
                    // حذف منطقي
                    node.Attributes["IsActive"].Value = "false";
                    doc.Save(_departmentsFile);
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        public int GetDepartmentsCount()
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);
                return doc.SelectNodes("//Department[@IsActive='true']").Count;
            }
            catch
            {
                return 0;
            }
        }

        public int GetFoldersCount()
        {
            return 8; // قيمة ثابتة للآن
        }

        public int GetDocumentsCount(string type)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@IsActive='true']";
                if (!string.IsNullOrEmpty(type))
                {
                    xpath += "[@Type='" + type + "']";
                }

                return doc.SelectNodes(xpath).Count;
            }
            catch
            {
                return 0;
            }
        }

        public bool DepartmentNameExists(string name, int excludeId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_departmentsFile);
                
                string xpath = "//Department[@Name='" + name + "' and @IsActive='true']";
                if (excludeId > 0)
                {
                    xpath += " and @Id!='" + excludeId + "'";
                }
                
                return doc.SelectSingleNode(xpath) != null;
            }
            catch
            {
                return false;
            }
        }

        private int GetNextId(XmlDocument doc, string elementName)
        {
            int maxId = 0;
            foreach (XmlNode node in doc.SelectNodes("//" + elementName))
            {
                int id;
                if (int.TryParse(node.Attributes["Id"].Value, out id))
                {
                    if (id > maxId) maxId = id;
                }
            }
            return maxId + 1;
        }

        public List<DocumentSimple> GetDocuments(string typeFilter = null, string searchText = null)
        {
            var documents = new List<DocumentSimple>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@IsActive='true']";

                foreach (XmlNode node in doc.SelectNodes(xpath))
                {
                    var document = new DocumentSimple
                    {
                        DocumentId = int.Parse(node.Attributes["Id"].Value),
                        DocumentNumber = node.Attributes["Number"].Value,
                        Subject = node.Attributes["Subject"].Value,
                        DocumentType = node.Attributes["Type"].Value,
                        DepartmentId = int.Parse(node.Attributes["DepartmentId"].Value),
                        FolderNumber = node.Attributes["FolderNumber"] != null ? node.Attributes["FolderNumber"].Value : "",
                        SenderReceiver = node.Attributes["SenderReceiver"] != null ? node.Attributes["SenderReceiver"].Value : "",
                        DocumentDate = DateTime.Parse(node.Attributes["DocumentDate"].Value),
                        ReceivedDate = node.Attributes["ReceivedDate"] != null ? (DateTime?)DateTime.Parse(node.Attributes["ReceivedDate"].Value) : null,
                        Notes = node.Attributes["Notes"] != null ? node.Attributes["Notes"].Value : "",
                        CreatedDate = DateTime.Parse(node.Attributes["CreatedDate"].Value),
                        CreatedBy = node.Attributes["CreatedBy"].Value,
                        IsActive = bool.Parse(node.Attributes["IsActive"].Value)
                    };

                    // تطبيق الفلاتر
                    bool includeDocument = true;

                    if (!string.IsNullOrEmpty(typeFilter) && document.DocumentType != typeFilter)
                        includeDocument = false;

                    if (!string.IsNullOrEmpty(searchText))
                    {
                        string search = searchText.ToLower();
                        if (!document.Subject.ToLower().Contains(search) &&
                            !document.DocumentNumber.ToLower().Contains(search) &&
                            !(document.SenderReceiver ?? "").ToLower().Contains(search))
                            includeDocument = false;
                    }

                    if (includeDocument)
                    {
                        // الحصول على اسم القسم
                        document.DepartmentName = GetDepartmentName(document.DepartmentId);

                        // تحميل المرفقات
                        document.Attachments = GetDocumentAttachments(document.DocumentId);

                        documents.Add(document);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة الوثائق: " + ex.Message);
            }

            return documents;
        }

        private string GetDepartmentName(int departmentId)
        {
            try
            {
                var departments = GetDepartments();
                foreach (var dept in departments)
                {
                    if (dept.DepartmentId == departmentId)
                        return dept.DepartmentName;
                }
                return "غير محدد";
            }
            catch
            {
                return "غير محدد";
            }
        }

        public bool SaveDocument(DocumentSimple document)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                if (document.DocumentId == 0)
                {
                    // إضافة وثيقة جديدة
                    document.DocumentId = GetNextId(doc, "Document");

                    var element = doc.CreateElement("Document");
                    element.SetAttribute("Id", document.DocumentId.ToString());
                    element.SetAttribute("Number", document.DocumentNumber);
                    element.SetAttribute("Subject", document.Subject);
                    element.SetAttribute("Type", document.DocumentType);
                    element.SetAttribute("DepartmentId", document.DepartmentId.ToString());
                    element.SetAttribute("FolderNumber", document.FolderNumber ?? "");
                    element.SetAttribute("SenderReceiver", document.SenderReceiver ?? "");
                    element.SetAttribute("DocumentDate", document.DocumentDate.ToString("yyyy-MM-dd"));
                    if (document.ReceivedDate.HasValue)
                        element.SetAttribute("ReceivedDate", document.ReceivedDate.Value.ToString("yyyy-MM-dd"));
                    element.SetAttribute("Notes", document.Notes ?? "");
                    element.SetAttribute("CreatedDate", document.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    element.SetAttribute("CreatedBy", document.CreatedBy);
                    element.SetAttribute("IsActive", document.IsActive.ToString().ToLower());

                    doc.DocumentElement.AppendChild(element);
                }
                else
                {
                    // تحديث وثيقة موجودة
                    var node = doc.SelectSingleNode("//Document[@Id='" + document.DocumentId + "']");
                    if (node != null)
                    {
                        node.Attributes["Number"].Value = document.DocumentNumber;
                        node.Attributes["Subject"].Value = document.Subject;
                        node.Attributes["Type"].Value = document.DocumentType;
                        node.Attributes["DepartmentId"].Value = document.DepartmentId.ToString();
                        node.Attributes["FolderNumber"].Value = document.FolderNumber ?? "";
                        node.Attributes["SenderReceiver"].Value = document.SenderReceiver ?? "";
                        node.Attributes["DocumentDate"].Value = document.DocumentDate.ToString("yyyy-MM-dd");
                        node.Attributes["IsActive"].Value = document.IsActive.ToString().ToLower();
                    }
                }

                doc.Save(_documentsFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteDocument(int documentId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                var node = doc.SelectSingleNode("//Document[@Id='" + documentId + "']");
                if (node != null)
                {
                    // حذف منطقي
                    node.Attributes["IsActive"].Value = "false";
                    doc.Save(_documentsFile);

                    // تنظيف المرفقات
                    CleanupDocumentAttachments(documentId);

                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        public bool DocumentNumberExists(string number, string type, int year, int excludeId = 0)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@Number='" + number + "' and @Type='" + type + "' and @IsActive='true']";
                if (excludeId > 0)
                {
                    xpath += " and @Id!='" + excludeId + "'";
                }

                var nodes = doc.SelectNodes(xpath);
                foreach (XmlNode node in nodes)
                {
                    DateTime docDate = DateTime.Parse(node.Attributes["DocumentDate"].Value);
                    if (docDate.Year == year)
                        return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        // وظائف إضافية مطلوبة لنافذة إضافة الوثائق
        public bool AddDocument(DocumentSimple document)
        {
            return SaveDocument(document);
        }

        public bool IsDocumentNumberExists(string documentNumber)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@Number='" + documentNumber + "' and @IsActive='true']";
                return doc.SelectSingleNode(xpath) != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود رقم أضبارة في قسم معين
        /// </summary>
        public bool IsFolderNumberExists(string folderNumber, int departmentId, int excludeDocumentId = 0)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_documentsFile);

                string xpath = "//Document[@FolderNumber='" + folderNumber + "' and @DepartmentId='" + departmentId + "' and @IsActive='true']";
                if (excludeDocumentId > 0)
                {
                    xpath += " and @Id!='" + excludeDocumentId + "'";
                }

                return doc.SelectSingleNode(xpath) != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حفظ مرفق وثيقة
        /// </summary>
        public bool SaveAttachment(DocumentAttachment attachment)
        {
            try
            {
                string attachmentsFile = Path.Combine(_dataPath, "attachments.xml");

                var doc = new XmlDocument();
                if (!File.Exists(attachmentsFile))
                {
                    CreateAttachmentsFile();
                }
                doc.Load(attachmentsFile);

                if (attachment.AttachmentId == 0)
                {
                    // إضافة مرفق جديد
                    attachment.AttachmentId = GetNextId(doc, "Attachment");

                    var element = doc.CreateElement("Attachment");
                    element.SetAttribute("Id", attachment.AttachmentId.ToString());
                    element.SetAttribute("DocumentId", attachment.DocumentId.ToString());
                    element.SetAttribute("FileName", attachment.FileName);
                    element.SetAttribute("FilePath", attachment.FilePath);
                    element.SetAttribute("FileSize", attachment.FileSize.ToString());
                    element.SetAttribute("FileType", attachment.FileType);
                    element.SetAttribute("UploadDate", attachment.UploadDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    element.SetAttribute("IsActive", attachment.IsActive.ToString().ToLower());

                    doc.DocumentElement.AppendChild(element);
                }
                else
                {
                    // تحديث مرفق موجود
                    var node = doc.SelectSingleNode("//Attachment[@Id='" + attachment.AttachmentId + "']");
                    if (node != null)
                    {
                        node.Attributes["FileName"].Value = attachment.FileName;
                        node.Attributes["FilePath"].Value = attachment.FilePath;
                        node.Attributes["FileSize"].Value = attachment.FileSize.ToString();
                        node.Attributes["FileType"].Value = attachment.FileType;
                        node.Attributes["IsActive"].Value = attachment.IsActive.ToString().ToLower();
                    }
                }

                doc.Save(attachmentsFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على مرفقات وثيقة
        /// </summary>
        public List<DocumentAttachment> GetDocumentAttachments(int documentId)
        {
            var attachments = new List<DocumentAttachment>();

            try
            {
                string attachmentsFile = Path.Combine(_dataPath, "attachments.xml");
                if (!File.Exists(attachmentsFile))
                {
                    return attachments;
                }

                var doc = new XmlDocument();
                doc.Load(attachmentsFile);

                foreach (XmlNode node in doc.SelectNodes("//Attachment[@DocumentId='" + documentId + "' and @IsActive='true']"))
                {
                    var attachment = new DocumentAttachment
                    {
                        AttachmentId = int.Parse(node.Attributes["Id"].Value),
                        DocumentId = int.Parse(node.Attributes["DocumentId"].Value),
                        FileName = node.Attributes["FileName"].Value,
                        FilePath = node.Attributes["FilePath"].Value,
                        FileSize = long.Parse(node.Attributes["FileSize"].Value),
                        FileType = node.Attributes["FileType"].Value,
                        UploadDate = DateTime.Parse(node.Attributes["UploadDate"].Value),
                        IsActive = bool.Parse(node.Attributes["IsActive"].Value)
                    };
                    attachments.Add(attachment);
                }
            }
            catch
            {
                // في حالة الخطأ، إرجاع قائمة فارغة
            }

            return attachments;
        }

        /// <summary>
        /// حذف مرفق
        /// </summary>
        public bool DeleteAttachment(int attachmentId)
        {
            try
            {
                string attachmentsFile = Path.Combine(_dataPath, "attachments.xml");
                if (!File.Exists(attachmentsFile))
                {
                    return false;
                }

                var doc = new XmlDocument();
                doc.Load(attachmentsFile);

                var node = doc.SelectSingleNode("//Attachment[@Id='" + attachmentId + "']");
                if (node != null)
                {
                    // حذف منطقي
                    node.Attributes["IsActive"].Value = "false";
                    doc.Save(attachmentsFile);
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        /// <summary>
        /// إنشاء مجلد المرفقات لوثيقة
        /// </summary>
        public string CreateAttachmentFolder(int documentId)
        {
            try
            {
                string attachmentsPath = Path.Combine(_dataPath, "Attachments");
                if (!Directory.Exists(attachmentsPath))
                {
                    Directory.CreateDirectory(attachmentsPath);
                }

                string documentFolder = Path.Combine(attachmentsPath, documentId.ToString());
                if (!Directory.Exists(documentFolder))
                {
                    Directory.CreateDirectory(documentFolder);
                }

                return documentFolder;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// نسخ ملف إلى مجلد المرفقات
        /// </summary>
        public bool CopyFileToAttachments(string sourceFile, int documentId, out string destinationPath)
        {
            destinationPath = string.Empty;

            try
            {
                if (!File.Exists(sourceFile))
                {
                    return false;
                }

                // التحقق من مساحة القرص المتاحة
                var driveInfo = new DriveInfo(Path.GetPathRoot(_dataPath));
                var fileInfo = new FileInfo(sourceFile);
                if (driveInfo.AvailableFreeSpace < fileInfo.Length + (100 * 1024 * 1024)) // 100MB احتياطي
                {
                    return false;
                }

                string attachmentFolder = CreateAttachmentFolder(documentId);
                if (string.IsNullOrEmpty(attachmentFolder))
                {
                    return false;
                }

                string fileName = Path.GetFileNameWithoutExtension(sourceFile);
                string extension = Path.GetExtension(sourceFile);
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string newFileName = fileName + "_" + timestamp + extension;

                destinationPath = Path.Combine(attachmentFolder, newFileName);
                File.Copy(sourceFile, destinationPath, true);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حذف ملف مرفق من النظام
        /// </summary>
        public bool DeleteAttachmentFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود ملف مرفق
        /// </summary>
        public bool AttachmentFileExists(string filePath)
        {
            try
            {
                return File.Exists(filePath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات ملف مرفق
        /// </summary>
        public FileInfo GetAttachmentFileInfo(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    return new FileInfo(filePath);
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تنظيف مرفقات وثيقة محذوفة
        /// </summary>
        public bool CleanupDocumentAttachments(int documentId)
        {
            try
            {
                // حذف المرفقات من قاعدة البيانات
                var attachments = GetDocumentAttachments(documentId);
                foreach (var attachment in attachments)
                {
                    DeleteAttachment(attachment.AttachmentId);
                    DeleteAttachmentFile(attachment.FilePath);
                }

                // حذف مجلد الوثيقة إذا كان فارغاً
                string documentFolder = Path.Combine(_dataPath, "Attachments", documentId.ToString());
                if (Directory.Exists(documentFolder))
                {
                    try
                    {
                        Directory.Delete(documentFolder, true);
                    }
                    catch
                    {
                        // تجاهل أخطاء حذف المجلد
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء ملف المرفقات
        /// </summary>
        private void CreateAttachmentsFile()
        {
            try
            {
                string attachmentsFile = Path.Combine(_dataPath, "attachments.xml");
                var doc = new XmlDocument();
                var root = doc.CreateElement("Attachments");
                doc.AppendChild(root);
                doc.Save(attachmentsFile);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
    }
}
