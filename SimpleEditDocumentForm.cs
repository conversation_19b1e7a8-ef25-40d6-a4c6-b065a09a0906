using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// نافذة تعديل الوثائق - ترث من نافذة إضافة الوثائق
    /// </summary>
    public partial class SimpleEditDocumentForm : SimpleAddDocumentForm
    {
        #region Fields
        private DocumentSimple _documentToEdit;
        private int _originalDocumentId;
        #endregion

        #region Constructor
        public SimpleEditDocumentForm(DocumentSimple document) : base()
        {
            _documentToEdit = document;
            _originalDocumentId = document.DocumentId;
            
            // تغيير عنوان النافذة
            this.Text = "✏️ تعديل الوثيقة";
            
            // تحميل البيانات الموجودة
            LoadExistingData();
        }
        #endregion

        #region Data Loading
        private void LoadExistingData()
        {
            try
            {
                // تحميل البيانات الأساسية
                _documentNumberTextBox.Text = _documentToEdit.DocumentNumber;
                _subjectTextBox.Text = _documentToEdit.Subject;
                _folderNumberTextBox.Text = _documentToEdit.FolderNumber ?? "";
                _documentDatePicker.Value = _documentToEdit.DocumentDate;
                _senderReceiverTextBox.Text = _documentToEdit.SenderReceiver ?? "";
                _notesTextBox.Text = _documentToEdit.Notes ?? "";

                // تحديد نوع الوثيقة
                for (int i = 0; i < _typeComboBox.Items.Count; i++)
                {
                    if (_typeComboBox.Items[i].ToString() == _documentToEdit.DocumentType)
                    {
                        _typeComboBox.SelectedIndex = i;
                        break;
                    }
                }

                // تحديد القسم
                for (int i = 0; i < _departmentComboBox.Items.Count; i++)
                {
                    var item = (ComboBoxItem)_departmentComboBox.Items[i];
                    if (item.Value == _documentToEdit.DepartmentId)
                    {
                        _departmentComboBox.SelectedIndex = i;
                        break;
                    }
                }

                // تحميل المرفقات الموجودة
                LoadExistingAttachments();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل بيانات الوثيقة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadExistingAttachments()
        {
            try
            {
                var attachments = _dataManager.GetDocumentAttachments(_originalDocumentId);
                
                foreach (var attachment in attachments)
                {
                    if (attachment.IsActive)
                    {
                        int rowIndex = _attachmentsDataGridView.Rows.Add(
                            attachment.FileName,
                            attachment.FileSizeFormatted,
                            attachment.FileType
                        );
                        
                        // حفظ معرف المرفق في Tag للمرفقات الموجودة
                        _attachmentsDataGridView.Rows[rowIndex].Tag = attachment.AttachmentId;
                    }
                }

                UpdateAttachmentsSummary();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل المرفقات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Validation Override
        protected override bool ValidateInput()
        {
            // التحقق الأساسي
            if (!base.ValidateInput())
                return false;

            // التحقق من تكرار رقم الوثيقة (استثناء الوثيقة الحالية)
            if (_dataManager.DocumentNumberExists(_documentNumberTextBox.Text.Trim(), 
                DateTime.Now.Year, _originalDocumentId))
            {
                RTLHelper.ShowRTLMessageBox("رقم الوثيقة موجود مسبقاً. يرجى إدخال رقم مختلف", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberTextBox.Focus();
                return false;
            }

            // التحقق من تكرار رقم الأضبارة (استثناء الوثيقة الحالية)
            int departmentId = ((ComboBoxItem)_departmentComboBox.SelectedItem).Value;
            if (_dataManager.IsFolderNumberExists(_folderNumberTextBox.Text.Trim(), departmentId, _originalDocumentId))
            {
                RTLHelper.ShowRTLMessageBox("رقم الأضبارة موجود مسبقاً في هذا القسم. يرجى إدخال رقم مختلف", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderNumberTextBox.Focus();
                return false;
            }

            return true;
        }
        #endregion

        #region Save Override
        protected override void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // إنشاء كائن الوثيقة المحدثة
                var updatedDocument = new DocumentSimple
                {
                    DocumentId = _originalDocumentId, // الحفاظ على المعرف الأصلي
                    DocumentNumber = _documentNumberTextBox.Text.Trim(),
                    Subject = _subjectTextBox.Text.Trim(),
                    DocumentType = _typeComboBox.SelectedItem.ToString(),
                    DepartmentId = ((ComboBoxItem)_departmentComboBox.SelectedItem).Value,
                    DepartmentName = ((ComboBoxItem)_departmentComboBox.SelectedItem).Text,
                    FolderNumber = _folderNumberTextBox.Text.Trim(),
                    DocumentDate = _documentDatePicker.Value,
                    SenderReceiver = _senderReceiverTextBox.Text.Trim(),
                    Notes = _notesTextBox.Text.Trim(),
                    CreatedDate = _documentToEdit.CreatedDate, // الحفاظ على تاريخ الإنشاء الأصلي
                    CreatedBy = _documentToEdit.CreatedBy, // الحفاظ على منشئ الوثيقة الأصلي
                    IsActive = true
                };

                if (_dataManager.SaveDocument(updatedDocument))
                {
                    // حفظ المرفقات الجديدة
                    bool attachmentsSaved = SaveNewAttachments(_originalDocumentId);
                    
                    if (attachmentsSaved)
                    {
                        this.DialogResult = DialogResult.OK;
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("تم حفظ الوثيقة ولكن فشل في حفظ بعض المرفقات الجديدة", "تحذير",
                                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.DialogResult = DialogResult.OK;
                    }
                }
                else
                {
                    RTLHelper.ShowRTLMessageBox("فشل في حفظ تعديلات الوثيقة", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في حفظ التعديلات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool SaveNewAttachments(int documentId)
        {
            bool allSaved = true;
            
            try
            {
                _fileProgressBar.Visible = true;
                _fileProgressBar.Maximum = _attachmentsDataGridView.Rows.Count;
                _fileProgressBar.Value = 0;

                foreach (DataGridViewRow row in _attachmentsDataGridView.Rows)
                {
                    try
                    {
                        // التحقق من أن هذا مرفق جديد (Tag يحتوي على مسار الملف وليس معرف المرفق)
                        if (row.Tag is string)
                        {
                            string originalPath = row.Tag as string;
                            string fileName = row.Cells["FileName"].Value.ToString();
                            
                            if (!string.IsNullOrEmpty(originalPath) && File.Exists(originalPath))
                            {
                                string destinationPath;
                                if (_dataManager.CopyFileToAttachments(originalPath, documentId, out destinationPath))
                                {
                                    // إنشاء كائن المرفق
                                    var fileInfo = new FileInfo(originalPath);
                                    var attachment = new DocumentAttachment
                                    {
                                        DocumentId = documentId,
                                        FileName = fileName,
                                        FilePath = destinationPath,
                                        FileSize = fileInfo.Length,
                                        FileType = fileInfo.Extension,
                                        UploadDate = DateTime.Now,
                                        IsActive = true
                                    };

                                    // حفظ في قاعدة البيانات
                                    if (!_dataManager.SaveAttachment(attachment))
                                    {
                                        allSaved = false;
                                    }
                                }
                                else
                                {
                                    allSaved = false;
                                }
                            }
                        }
                        // إذا كان Tag يحتوي على معرف المرفق، فهذا مرفق موجود مسبقاً - لا نحتاج لحفظه

                        _fileProgressBar.Value++;
                        Application.DoEvents(); // تحديث واجهة المستخدم
                    }
                    catch
                    {
                        allSaved = false;
                    }
                }

                _fileProgressBar.Visible = false;
            }
            catch
            {
                allSaved = false;
                _fileProgressBar.Visible = false;
            }

            return allSaved;
        }
        #endregion

        #region Delete Attachment Override
        protected override void DeleteFileButton_Click(object sender, EventArgs e)
        {
            if (_attachmentsDataGridView.SelectedRows.Count > 0)
            {
                var result = RTLHelper.ShowRTLMessageBox("هل تريد حذف الملف المحدد؟", "تأكيد الحذف",
                              MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    var selectedRow = _attachmentsDataGridView.SelectedRows[0];
                    
                    // التحقق من نوع المرفق
                    if (selectedRow.Tag is int) // مرفق موجود مسبقاً
                    {
                        int attachmentId = (int)selectedRow.Tag;
                        if (_dataManager.DeleteAttachment(attachmentId))
                        {
                            _attachmentsDataGridView.Rows.RemoveAt(selectedRow.Index);
                            UpdateAttachmentsSummary();
                        }
                        else
                        {
                            RTLHelper.ShowRTLMessageBox("فشل في حذف المرفق", "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else // مرفق جديد لم يحفظ بعد
                    {
                        _attachmentsDataGridView.Rows.RemoveAt(selectedRow.Index);
                        UpdateAttachmentsSummary();
                    }
                }
            }
            else
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار ملف للحذف", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        #endregion
    }
}
