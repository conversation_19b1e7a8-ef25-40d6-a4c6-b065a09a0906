using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// عنصر ComboBox بسيط
    /// </summary>
    public class ComboBoxItem
    {
        public string Text { get; set; }
        public int Value { get; set; }

        public override string ToString()
        {
            return Text;
        }
    }

    /// <summary>
    /// نافذة إضافة/تعديل الأضبارة
    /// </summary>
    public partial class AddFolderForm : Form
    {
        #region Fields
        private List<DepartmentSimple> _departments;
        private Folder _folderToEdit;
        private bool _isEditMode;

        // Controls
        private TextBox _folderNameTextBox;
        private ComboBox _departmentComboBox;
        private DateTimePicker _folderDatePicker;
        private Button _saveButton;
        private Button _cancelButton;
        #endregion

        #region Properties
        public string FolderName { get; private set; }
        public int SelectedDepartmentId { get; private set; }
        public DateTime FolderDate { get; private set; }
        #endregion

        #region Constructors
        /// <summary>
        /// إنشاء نافذة إضافة أضبارة جديدة
        /// </summary>
        public AddFolderForm(List<DepartmentSimple> departments)
        {
            _departments = departments;
            _isEditMode = false;
            InitializeComponent();
            SetupForm();
            CreateControls();
            LoadDepartments();
            
            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Folder.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }

        /// <summary>
        /// إنشاء نافذة تعديل أضبارة موجودة
        /// </summary>
        public AddFolderForm(List<DepartmentSimple> departments, Folder folderToEdit)
        {
            _departments = departments;
            _folderToEdit = folderToEdit;
            _isEditMode = true;
            InitializeComponent();
            SetupForm();
            CreateControls();
            LoadDepartments();
            LoadFolderData();
            
            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Folder.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Form Setup
        private void SetupForm()
        {
            this.Text = _isEditMode ? "✏️ تعديل الأضبارة" : "➕ إضافة أضبارة جديدة";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }

        private void CreateControls()
        {
            int startX = 30;
            int startY = 30;
            int labelWidth = 120;
            int fieldWidth = 300;
            int fieldHeight = 30;
            int spacing = 50;
            int currentY = startY;

            // عنوان النافذة
            var titleLabel = RTLHelper.CreateStyledLabel(
                _isEditMode ? "تعديل بيانات الأضبارة" : "إضافة أضبارة جديدة",
                new Point(0, currentY),
                new Size(this.Width, 35),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleCenter
            );
            this.Controls.Add(titleLabel);
            currentY += 50;

            // اسم الأضبارة
            var nameLabel = RTLHelper.CreateStyledLabel(
                "اسم الأضبارة:",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _folderNameTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            this.Controls.Add(nameLabel);
            this.Controls.Add(_folderNameTextBox);
            currentY += spacing;

            // القسم
            var departmentLabel = RTLHelper.CreateStyledLabel(
                "القسم:",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _departmentComboBox = new ComboBox
            {
                Location = new Point(startX, currentY),
                Size = new Size(fieldWidth, fieldHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont
            };

            this.Controls.Add(departmentLabel);
            this.Controls.Add(_departmentComboBox);
            currentY += spacing;

            // تاريخ الأضبارة
            var dateLabel = RTLHelper.CreateStyledLabel(
                "تاريخ الأضبارة:",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _folderDatePicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight)
            );

            this.Controls.Add(dateLabel);
            this.Controls.Add(_folderDatePicker);
            currentY += spacing + 20;

            // أزرار الحفظ والإلغاء
            CreateButtons(currentY);
        }

        private void CreateButtons(int yPosition)
        {
            int buttonWidth = 120;
            int buttonHeight = 40;
            int spacing = 20;
            int totalButtonsWidth = (buttonWidth * 2) + spacing;
            int startX = (this.Width - totalButtonsWidth) / 2;

            _saveButton = RTLHelper.CreateStyledButton(
                _isEditMode ? "💾 حفظ التعديلات" : "💾 حفظ الأضبارة",
                new Point(startX, yPosition),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                SaveButton_Click
            );

            _cancelButton = RTLHelper.CreateStyledButton(
                "❌ إلغاء",
                new Point(startX + buttonWidth + spacing, yPosition),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SecondaryColor,
                delegate { this.DialogResult = DialogResult.Cancel; }
            );

            RTLHelper.ApplyButtonEffects(_saveButton);
            RTLHelper.ApplyButtonEffects(_cancelButton);

            this.Controls.Add(_saveButton);
            this.Controls.Add(_cancelButton);
        }
        #endregion

        #region Data Loading
        private void LoadDepartments()
        {
            _departmentComboBox.Items.Clear();
            
            foreach (var dept in _departments)
            {
                _departmentComboBox.Items.Add(new ComboBoxItem 
                { 
                    Text = dept.DepartmentName, 
                    Value = dept.DepartmentId 
                });
            }

            if (_departmentComboBox.Items.Count > 0)
                _departmentComboBox.SelectedIndex = 0;
        }

        private void LoadFolderData()
        {
            if (_folderToEdit != null)
            {
                _folderNameTextBox.Text = _folderToEdit.FolderName;
                _folderDatePicker.Value = _folderToEdit.FolderDate;

                // تحديد القسم
                for (int i = 0; i < _departmentComboBox.Items.Count; i++)
                {
                    var item = (ComboBoxItem)_departmentComboBox.Items[i];
                    if (item.Value == _folderToEdit.DepartmentId)
                    {
                        _departmentComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }
        }
        #endregion

        #region Event Handlers
        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                FolderName = _folderNameTextBox.Text.Trim();
                SelectedDepartmentId = ((ComboBoxItem)_departmentComboBox.SelectedItem).Value;
                FolderDate = _folderDatePicker.Value;

                this.DialogResult = DialogResult.OK;
            }
        }
        #endregion

        #region Validation
        private bool ValidateInput()
        {
            // التحقق من اسم الأضبارة
            if (string.IsNullOrEmpty(_folderNameTextBox.Text.Trim()))
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال اسم الأضبارة", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderNameTextBox.Focus();
                return false;
            }

            if (_folderNameTextBox.Text.Trim().Length > 100)
            {
                RTLHelper.ShowRTLMessageBox("اسم الأضبارة يجب أن يكون أقل من 100 حرف", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderNameTextBox.Focus();
                return false;
            }

            // التحقق من اختيار القسم
            if (_departmentComboBox.SelectedItem == null)
            {
                RTLHelper.ShowRTLMessageBox("يرجى اختيار القسم", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _departmentComboBox.Focus();
                return false;
            }

            // التحقق من التاريخ
            if (_folderDatePicker.Value > DateTime.Now.Date)
            {
                RTLHelper.ShowRTLMessageBox("تاريخ الأضبارة لا يمكن أن يكون في المستقبل", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderDatePicker.Focus();
                return false;
            }

            return true;
        }
        #endregion

        #region Designer Code
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 350);
            this.Name = "AddFolderForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
