using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// عنصر ComboBox بسيط
    /// </summary>
    public class ComboBoxItem
    {
        public string Text { get; set; }
        public int Value { get; set; }
        
        public override string ToString()
        {
            return Text;
        }
    }

    /// <summary>
    /// نافذة البحث المتقدم
    /// </summary>
    public partial class AdvancedSearchForm : Form
    {
        #region Fields
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;
        private List<DocumentSimple> _searchResults;
        private List<DepartmentSimple> _departments;

        // Search Controls
        private TextBox _documentNumberTextBox;
        private TextBox _subjectTextBox;
        private ComboBox _typeComboBox;
        private ComboBox _departmentComboBox;
        private DateTimePicker _dateFromPicker;
        private DateTimePicker _dateToPicker;
        private CheckBox _dateRangeCheckBox;
        private TextBox _senderReceiverTextBox;
        
        // Results Controls
        private DataGridView _resultsDataGridView;
        private Label _resultsCountLabel;
        
        // Action Controls
        private Button _searchButton;
        private Button _clearButton;
        private Button _exportButton;
        private Button _closeButton;
        
        // Panels
        private Panel _searchPanel;
        private Panel _resultsPanel;
        private Panel _bottomPanel;
        #endregion

        #region Constructor
        public AdvancedSearchForm()
        {
            InitializeComponent();
            _dataManager = new SimpleDataManager();
            _departmentRepo = new SimpleDepartmentRepository();
            
            SetupForm();
            CreateControls();
            LoadDepartments();
            
            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Search.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Form Setup
        private void SetupForm()
        {
            this.Text = "🔍 البحث المتقدم في الوثائق";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 500);
        }

        private void CreateControls()
        {
            CreateSearchPanel();
            CreateResultsPanel();
            CreateBottomPanel();
        }

        private void CreateSearchPanel()
        {
            _searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 200,
                BackColor = Color.White,
                Padding = new Padding(20),
                BorderStyle = BorderStyle.FixedSingle
            };

            // عنوان لوحة البحث
            var searchTitle = RTLHelper.CreateStyledLabel(
                "🔍 معايير البحث",
                new Point(20, 10),
                new Size(200, 30),
                RTLHelper.ArabicFontBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            int startX = 20;
            int startY = 50;
            int labelWidth = 120;
            int fieldWidth = 200;
            int fieldHeight = 25;
            int spacing = 35;
            int columnSpacing = 350;

            // الصف الأول - رقم الوثيقة والموضوع
            var docNumberLabel = RTLHelper.CreateStyledLabel(
                "رقم الوثيقة:",
                new Point(startX + fieldWidth + 10, startY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _documentNumberTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, startY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            var subjectLabel = RTLHelper.CreateStyledLabel(
                "الموضوع:",
                new Point(startX + columnSpacing + fieldWidth + 10, startY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _subjectTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX + columnSpacing, startY),
                new Size(fieldWidth, fieldHeight),
                false
            );

            // الصف الثاني - النوع والقسم
            var typeLabel = RTLHelper.CreateStyledLabel(
                "نوع الوثيقة:",
                new Point(startX + fieldWidth + 10, startY + spacing),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _typeComboBox = new ComboBox
            {
                Location = new Point(startX, startY + spacing),
                Size = new Size(fieldWidth, fieldHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont
            };
            _typeComboBox.Items.AddRange(new object[] { "الكل", "صادر", "وارد" });
            _typeComboBox.SelectedIndex = 0;

            var departmentLabel = RTLHelper.CreateStyledLabel(
                "القسم:",
                new Point(startX + columnSpacing + fieldWidth + 10, startY + spacing),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _departmentComboBox = new ComboBox
            {
                Location = new Point(startX + columnSpacing, startY + spacing),
                Size = new Size(fieldWidth, fieldHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont
            };

            // الصف الثالث - التاريخ والمرسل/المستقبل
            _dateRangeCheckBox = new CheckBox
            {
                Text = "البحث في فترة زمنية:",
                Location = new Point(startX + fieldWidth + 10, startY + spacing * 2),
                Size = new Size(labelWidth, fieldHeight),
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont,
                CheckAlign = ContentAlignment.MiddleRight
            };
            _dateRangeCheckBox.CheckedChanged += DateRangeCheckBox_CheckedChanged;

            _dateFromPicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX, startY + spacing * 2),
                new Size(90, fieldHeight)
            );
            _dateFromPicker.Enabled = false;

            var toLabel = RTLHelper.CreateStyledLabel(
                "إلى",
                new Point(startX + 100, startY + spacing * 2),
                new Size(30, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleCenter
            );

            _dateToPicker = RTLHelper.CreateStyledDateTimePicker(
                new Point(startX + 140, startY + spacing * 2),
                new Size(90, fieldHeight)
            );
            _dateToPicker.Enabled = false;

            var senderLabel = RTLHelper.CreateStyledLabel(
                "المرسل/المستقبل:",
                new Point(startX + columnSpacing + fieldWidth + 10, startY + spacing * 2),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _senderReceiverTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX + columnSpacing, startY + spacing * 2),
                new Size(fieldWidth, fieldHeight),
                false
            );

            // إضافة العناصر للوحة
            _searchPanel.Controls.Add(searchTitle);
            _searchPanel.Controls.Add(docNumberLabel);
            _searchPanel.Controls.Add(_documentNumberTextBox);
            _searchPanel.Controls.Add(subjectLabel);
            _searchPanel.Controls.Add(_subjectTextBox);
            _searchPanel.Controls.Add(typeLabel);
            _searchPanel.Controls.Add(_typeComboBox);
            _searchPanel.Controls.Add(departmentLabel);
            _searchPanel.Controls.Add(_departmentComboBox);
            _searchPanel.Controls.Add(_dateRangeCheckBox);
            _searchPanel.Controls.Add(_dateFromPicker);
            _searchPanel.Controls.Add(toLabel);
            _searchPanel.Controls.Add(_dateToPicker);
            _searchPanel.Controls.Add(senderLabel);
            _searchPanel.Controls.Add(_senderReceiverTextBox);

            this.Controls.Add(_searchPanel);
        }

        private void CreateResultsPanel()
        {
            _resultsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(20)
            };

            // عنوان النتائج
            var resultsTitle = RTLHelper.CreateStyledLabel(
                "📋 نتائج البحث",
                new Point(20, 10),
                new Size(200, 30),
                RTLHelper.ArabicFontBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            _resultsCountLabel = RTLHelper.CreateStyledLabel(
                "لم يتم البحث بعد",
                new Point(250, 10),
                new Size(300, 30),
                RTLHelper.ArabicFont,
                RTLHelper.InfoColor,
                ContentAlignment.MiddleRight
            );

            // جدول النتائج
            _resultsDataGridView = new DataGridView
            {
                Location = new Point(20, 50),
                Size = new Size(this.Width - 80, this.Height - 350),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing,
                RowHeadersVisible = false
            };

            // إعداد الأعمدة
            _resultsDataGridView.Columns.Add("DocumentNumber", "رقم الوثيقة");
            _resultsDataGridView.Columns.Add("Subject", "الموضوع");
            _resultsDataGridView.Columns.Add("Type", "النوع");
            _resultsDataGridView.Columns.Add("DepartmentName", "القسم");
            _resultsDataGridView.Columns.Add("DocumentDate", "التاريخ");
            _resultsDataGridView.Columns.Add("SenderReceiver", "المرسل/المستقبل");

            // تحديد عرض الأعمدة
            _resultsDataGridView.Columns["DocumentNumber"].Width = 100;
            _resultsDataGridView.Columns["Subject"].Width = 250;
            _resultsDataGridView.Columns["Type"].Width = 80;
            _resultsDataGridView.Columns["DepartmentName"].Width = 150;
            _resultsDataGridView.Columns["DocumentDate"].Width = 100;
            _resultsDataGridView.Columns["SenderReceiver"].Width = 150;

            _resultsPanel.Controls.Add(resultsTitle);
            _resultsPanel.Controls.Add(_resultsCountLabel);
            _resultsPanel.Controls.Add(_resultsDataGridView);

            this.Controls.Add(_resultsPanel);
        }

        private void CreateBottomPanel()
        {
            _bottomPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(20)
            };

            int buttonWidth = 120;
            int buttonHeight = 40;
            int spacing = 20;
            int totalButtonsWidth = (buttonWidth * 4) + (spacing * 3);
            int startX = (this.Width - totalButtonsWidth) / 2;
            int buttonY = 20;

            _searchButton = RTLHelper.CreateStyledButton(
                "🔍 بحث",
                new Point(startX, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                SearchButton_Click
            );

            _clearButton = RTLHelper.CreateStyledButton(
                "🗑️ مسح",
                new Point(startX + buttonWidth + spacing, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.WarningColor,
                ClearButton_Click
            );

            _exportButton = RTLHelper.CreateStyledButton(
                "📤 تصدير",
                new Point(startX + (buttonWidth + spacing) * 2, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                ExportButton_Click
            );
            _exportButton.Enabled = false;

            _closeButton = RTLHelper.CreateStyledButton(
                "❌ إغلاق",
                new Point(startX + (buttonWidth + spacing) * 3, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                delegate { this.Close(); }
            );

            RTLHelper.ApplyButtonEffects(_searchButton);
            RTLHelper.ApplyButtonEffects(_clearButton);
            RTLHelper.ApplyButtonEffects(_exportButton);
            RTLHelper.ApplyButtonEffects(_closeButton);

            _bottomPanel.Controls.Add(_searchButton);
            _bottomPanel.Controls.Add(_clearButton);
            _bottomPanel.Controls.Add(_exportButton);
            _bottomPanel.Controls.Add(_closeButton);

            this.Controls.Add(_bottomPanel);
        }
        #endregion

        #region Data Loading
        private void LoadDepartments()
        {
            try
            {
                var allDepartments = _departmentRepo.GetAll();
                _departments = new List<DepartmentSimple>();
                foreach (var dept in allDepartments)
                {
                    if (dept.IsActive)
                        _departments.Add(dept);
                }

                _departmentComboBox.Items.Clear();
                _departmentComboBox.Items.Add(new ComboBoxItem { Text = "جميع الأقسام", Value = 0 });

                foreach (var dept in _departments)
                {
                    _departmentComboBox.Items.Add(new ComboBoxItem
                    {
                        Text = dept.DepartmentName,
                        Value = dept.DepartmentId
                    });
                }

                _departmentComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Event Handlers
        private void DateRangeCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            _dateFromPicker.Enabled = _dateRangeCheckBox.Checked;
            _dateToPicker.Enabled = _dateRangeCheckBox.Checked;
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            try
            {
                PerformSearch();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في البحث: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearSearchFields();
            ClearResults();
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            RTLHelper.ShowRTLMessageBox("ميزة التصدير قيد التطوير", "تصدير النتائج",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        #endregion

        #region Search Methods
        private void PerformSearch()
        {
            // الحصول على جميع الوثائق
            var allDocuments = _dataManager.GetAllDocuments();
            _searchResults = new List<DocumentSimple>();

            foreach (var doc in allDocuments)
            {
                if (doc.IsActive && MatchesSearchCriteria(doc))
                {
                    _searchResults.Add(doc);
                }
            }

            DisplayResults();
        }

        private bool MatchesSearchCriteria(DocumentSimple document)
        {
            // فلترة رقم الوثيقة
            if (!string.IsNullOrEmpty(_documentNumberTextBox.Text.Trim()))
            {
                if (!document.DocumentNumber.Contains(_documentNumberTextBox.Text.Trim()))
                    return false;
            }

            // فلترة الموضوع
            if (!string.IsNullOrEmpty(_subjectTextBox.Text.Trim()))
            {
                if (!document.Subject.Contains(_subjectTextBox.Text.Trim()))
                    return false;
            }

            // فلترة النوع
            if (_typeComboBox.SelectedIndex > 0) // ليس "الكل"
            {
                if (document.DocumentType != _typeComboBox.SelectedItem.ToString())
                    return false;
            }

            // فلترة القسم
            if (_departmentComboBox.SelectedItem != null)
            {
                var selectedDept = (ComboBoxItem)_departmentComboBox.SelectedItem;
                if (selectedDept.Value > 0 && document.DepartmentId != selectedDept.Value)
                    return false;
            }

            // فلترة التاريخ
            if (_dateRangeCheckBox.Checked)
            {
                if (document.DocumentDate < _dateFromPicker.Value.Date ||
                    document.DocumentDate > _dateToPicker.Value.Date)
                    return false;
            }

            // فلترة المرسل/المستقبل
            if (!string.IsNullOrEmpty(_senderReceiverTextBox.Text.Trim()))
            {
                if (string.IsNullOrEmpty(document.SenderReceiver) ||
                    !document.SenderReceiver.Contains(_senderReceiverTextBox.Text.Trim()))
                    return false;
            }

            return true;
        }

        private void DisplayResults()
        {
            _resultsDataGridView.Rows.Clear();

            foreach (var doc in _searchResults)
            {
                int rowIndex = _resultsDataGridView.Rows.Add(
                    doc.DocumentNumber,
                    doc.Subject,
                    doc.DocumentType,
                    doc.DepartmentName,
                    doc.DocumentDateFormatted,
                    doc.SenderReceiver ?? ""
                );

                _resultsDataGridView.Rows[rowIndex].Tag = doc;
            }

            _resultsCountLabel.Text = "تم العثور على " + _searchResults.Count + " وثيقة";
            _exportButton.Enabled = _searchResults.Count > 0;
        }

        private void ClearSearchFields()
        {
            _documentNumberTextBox.Text = "";
            _subjectTextBox.Text = "";
            _typeComboBox.SelectedIndex = 0;
            _departmentComboBox.SelectedIndex = 0;
            _dateRangeCheckBox.Checked = false;
            _senderReceiverTextBox.Text = "";
        }

        private void ClearResults()
        {
            _resultsDataGridView.Rows.Clear();
            _resultsCountLabel.Text = "لم يتم البحث بعد";
            _exportButton.Enabled = false;
            if (_searchResults != null)
                _searchResults.Clear();
        }
        #endregion

        #region Designer Code
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(900, 600);
            this.Name = "AdvancedSearchForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
