using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    public partial class SimpleDepartmentsForm : Form
    {
        private ListView _departmentsList;
        private Button _addButton;
        private Button _editButton;
        private Button _deleteButton;
        private Button _closeButton;
        private List<DepartmentSimple> _departments;
        private SimpleDepartmentRepository _departmentRepo;

        public SimpleDepartmentsForm()
        {
            InitializeComponent();
            _departmentRepo = new SimpleDepartmentRepository();
            SetupForm();
            CreateControls();
            LoadDepartments();

            // تطبيق إعدادات RTL والأيقونة
            RTLHelper.ApplyRTLSettings(this);
            IconManager.SetFormIcon(this, "Departments.ico");

            // إصلاح مشاكل المحاذاة
            RTLHelper.FixCommonAlignmentIssues(this);
        }

        private void SetupForm()
        {
            this.Text = "إدارة الأقسام";
            this.Size = new Size(900, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = RTLHelper.CreateStyledLabel(
                "📁 إدارة الأقسام",
                new Point(20, 20),
                new Size(300, 35),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(titleLabel);

            // وصف النافذة
            var descLabel = RTLHelper.CreateStyledLabel(
                "إضافة وتعديل وحذف أقسام المؤسسة",
                new Point(20, 55),
                new Size(400, 25),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(descLabel);

            // قائمة الأقسام
            _departmentsList = RTLHelper.CreateStyledListView(
                new Point(20, 90),
                new Size(600, 450),
                new string[] { "المعرف", "اسم القسم", "الوصف", "تاريخ الإنشاء", "الحالة" },
                new int[] { 80, 200, 250, 120, 80 }
            );

            this.Controls.Add(_departmentsList);

            // أزرار التحكم
            CreateControlButtons();
        }

        private void CreateControlButtons()
        {
            int buttonWidth = 140;
            int buttonHeight = 40;
            int startX = 650;
            int startY = 90;
            int spacing = 50;

            _addButton = RTLHelper.CreateStyledButton(
                "➕ إضافة قسم",
                new Point(startX, startY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                AddButton_Click
            );

            _editButton = RTLHelper.CreateStyledButton(
                "✏️ تعديل قسم",
                new Point(startX, startY + spacing),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                EditButton_Click
            );
            _editButton.Enabled = false;

            _deleteButton = RTLHelper.CreateStyledButton(
                "🗑️ حذف قسم",
                new Point(startX, startY + spacing * 2),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DangerColor,
                DeleteButton_Click
            );
            _deleteButton.Enabled = false;

            _closeButton = RTLHelper.CreateStyledButton(
                "❌ إغلاق",
                new Point(startX, startY + spacing * 4),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                delegate { this.Close(); }
            );

            // تطبيق تأثيرات بصرية
            RTLHelper.ApplyButtonEffects(_addButton);
            RTLHelper.ApplyButtonEffects(_editButton);
            RTLHelper.ApplyButtonEffects(_deleteButton);
            RTLHelper.ApplyButtonEffects(_closeButton);

            this.Controls.Add(_addButton);
            this.Controls.Add(_editButton);
            this.Controls.Add(_deleteButton);
            this.Controls.Add(_closeButton);

            // ربط حدث تحديد العنصر
            _departmentsList.SelectedIndexChanged += DepartmentsList_SelectedIndexChanged;
        }

        private void LoadDepartments()
        {
            try
            {
                _departments = _departmentRepo.GetAll();
                RefreshDepartmentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الأقسام: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                _departments = new List<DepartmentSimple>();
            }
        }

        private void RefreshDepartmentsList()
        {
            _departmentsList.Items.Clear();

            foreach (var dept in _departments)
            {
                var item = new ListViewItem(dept.DepartmentId.ToString());
                item.SubItems.Add(dept.DepartmentName);
                item.SubItems.Add(dept.Description ?? "");
                item.SubItems.Add(dept.CreatedDateFormatted);
                item.SubItems.Add(dept.StatusText);
                item.Tag = dept;

                // تلوين الصف حسب الحالة
                if (!dept.IsActive)
                {
                    item.ForeColor = RTLHelper.DarkColor;
                    item.Font = new Font(RTLHelper.ArabicFont, FontStyle.Strikeout);
                }

                _departmentsList.Items.Add(item);
            }
        }

        private void DepartmentsList_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool hasSelection = _departmentsList.SelectedItems.Count > 0;
            _editButton.Enabled = hasSelection;
            _deleteButton.Enabled = hasSelection;
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            var addForm = new SimpleAddDepartmentForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var newDept = new DepartmentSimple
                    {
                        DepartmentName = addForm.DepartmentName,
                        Description = addForm.DepartmentDescription
                    };

                    if (_departmentRepo.Insert(newDept))
                    {
                        LoadDepartments(); // إعادة تحميل القائمة
                        RTLHelper.ShowRTLMessageBox("تم إضافة القسم بنجاح!", "نجح الحفظ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في إضافة القسم", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("خطأ في إضافة القسم: " + ex.Message, "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_departmentsList.SelectedItems.Count > 0)
            {
                var selectedDept = (DepartmentSimple)_departmentsList.SelectedItems[0].Tag;
                var editForm = new SimpleAddDepartmentForm(selectedDept);
                
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        selectedDept.DepartmentName = editForm.DepartmentName;
                        selectedDept.Description = editForm.DepartmentDescription;
                        
                        if (_departmentRepo.Update(selectedDept))
                        {
                            LoadDepartments(); // إعادة تحميل القائمة
                            MessageBox.Show("تم تحديث القسم بنجاح!", "نجح التحديث", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في تحديث القسم", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في تحديث القسم: " + ex.Message, "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_departmentsList.SelectedItems.Count > 0)
            {
                var selectedDept = (DepartmentSimple)_departmentsList.SelectedItems[0].Tag;
                
                var result = MessageBox.Show("هل أنت متأكد من حذف القسم '" + selectedDept.DepartmentName + "'؟\n\nملاحظة: سيتم الحذف المنطقي وليس الحذف النهائي.", 
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        if (_departmentRepo.Delete(selectedDept.DepartmentId))
                        {
                            LoadDepartments(); // إعادة تحميل القائمة
                            MessageBox.Show("تم حذف القسم بنجاح!", "نجح الحذف", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف القسم", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في حذف القسم: " + ex.Message, "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Name = "SimpleDepartmentsForm";
            this.ResumeLayout(false);
        }
    }
}
