using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    public partial class SimpleMainForm : Form
    {
        private Button _departmentsButton;
        private Button _documentsButton;
        private Button _searchButton;
        private Button _settingsButton;
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;

        public SimpleMainForm()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                SetupForm();
                CreateControls();

                // تطبيق إعدادات RTL والأيقونة (مع معالجة الأخطاء)
                SetApplicationIcon();

                // تطبيق الإصلاح الشامل لـ RTL (مع معالجة الأخطاء)
                try
                {
                    RTLHelper.ComprehensiveRTLFix(this);
                }
                catch (Exception ex)
                {
                    // إذا فشل RTL Helper، نطبق RTL بسيط
                    this.RightToLeft = RightToLeft.Yes;
                    this.RightToLeftLayout = true;
                    // لا نوقف التطبيق
                }

                // التحقق من تطبيق RTL (مع معالجة الأخطاء)
                try
                {
                    if (!RTLHelper.ValidateRTLImplementation(this))
                    {
                        string report = RTLHelper.GenerateRTLReport(this);
                        RTLHelper.ShowRTLMessageBox(report, "تقرير RTL",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch
                {
                    // تجاهل أخطاء التقرير
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة النافذة الرئيسية: " + ex.Message +
                              "\n\nسيتم تشغيل النافذة بالإعدادات الافتراضية.",
                              "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                // إعداد أساسي للنافذة في حالة الفشل
                this.Text = "نظام الأرشفة الإلكترونية";
                this.Size = new Size(1000, 700);
                this.StartPosition = FormStartPosition.CenterScreen;
                this.RightToLeft = RightToLeft.Yes;
                this.RightToLeftLayout = true;
            }
        }

        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void SetupForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية المتقدم";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.WindowState = FormWindowState.Maximized;
            this.MinimumSize = new Size(1000, 600);
        }

        private void CreateControls()
        {
            CreateHeader();
            CreateSidebar();
            CreateMainContent();
        }

        private void CreateHeader()
        {
            var headerPanel = RTLHelper.CreateStyledPanel(Point.Empty, Size.Empty, RTLHelper.PrimaryColor, BorderStyle.None);
            headerPanel.Dock = DockStyle.Top;
            headerPanel.Height = 80;

            var titleLabel = RTLHelper.CreateStyledLabel(
                "نظام الأرشفة الإلكترونية المتقدم",
                new Point(50, 15),
                new Size(500, 35),
                RTLHelper.ArabicFontHeader,
                Color.White,
                ContentAlignment.MiddleRight
            );

            var subtitleLabel = RTLHelper.CreateStyledLabel(
                "إدارة شاملة للوثائق والمراسلات الرسمية مع قاعدة بيانات متقدمة",
                new Point(50, 45),
                new Size(600, 25),
                RTLHelper.ArabicFontLarge,
                Color.FromArgb(189, 195, 199),
                ContentAlignment.MiddleRight
            );

            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(subtitleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreateSidebar()
        {
            var sidebarPanel = RTLHelper.CreateStyledPanel(Point.Empty, Size.Empty, RTLHelper.SecondaryColor, BorderStyle.None);
            sidebarPanel.Dock = DockStyle.Right;
            sidebarPanel.Width = 280;

            var sidebarTitle = RTLHelper.CreateStyledLabel(
                "القائمة الرئيسية",
                new Point(20, 20),
                new Size(240, 30),
                RTLHelper.ArabicFontLargeBold,
                Color.White,
                ContentAlignment.MiddleRight
            );
            sidebarPanel.Controls.Add(sidebarTitle);

            CreateMenuButtons(sidebarPanel);
            this.Controls.Add(sidebarPanel);
        }

        private void CreateMenuButtons(Panel parent)
        {
            int buttonWidth = 230;
            int buttonHeight = 55;
            int startY = 80;
            int spacing = 70;

            _departmentsButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأقسام",
                new Point(25, startY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                MenuButton_Click
            );

            _documentsButton = RTLHelper.CreateStyledButton(
                "📄 إدارة الوثائق",
                new Point(25, startY + spacing),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                MenuButton_Click
            );

            _searchButton = RTLHelper.CreateStyledButton(
                "🔍 البحث المتقدم",
                new Point(25, startY + spacing * 2),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.WarningColor,
                MenuButton_Click
            );

            _settingsButton = RTLHelper.CreateStyledButton(
                "⚙️ الإعدادات",
                new Point(25, startY + spacing * 3),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                MenuButton_Click
            );

            // تطبيق تأثيرات بصرية
            RTLHelper.ApplyButtonEffects(_departmentsButton);
            RTLHelper.ApplyButtonEffects(_documentsButton);
            RTLHelper.ApplyButtonEffects(_searchButton);
            RTLHelper.ApplyButtonEffects(_settingsButton);

            parent.Controls.Add(_departmentsButton);
            parent.Controls.Add(_documentsButton);
            parent.Controls.Add(_searchButton);
            parent.Controls.Add(_settingsButton);
        }



        private void CreateMainContent()
        {
            var mainPanel = RTLHelper.CreateStyledPanel(Point.Empty, Size.Empty, RTLHelper.LightColor, BorderStyle.None);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Padding = new Padding(30);

            CreateWelcomeSection(mainPanel);
            CreateStatisticsCards(mainPanel);
            CreateQuickActions(mainPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreateWelcomeSection(Panel parent)
        {
            var welcomeLabel = RTLHelper.CreateStyledLabel(
                "مرحباً بك في نظام الأرشفة الإلكترونية المتقدم",
                new Point(30, 30),
                new Size(600, 40),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            var descLabel = RTLHelper.CreateStyledLabel(
                "نظام متكامل لإدارة الوثائق والمراسلات الرسمية مع قاعدة بيانات XML متقدمة وواجهة عربية احترافية",
                new Point(30, 75),
                new Size(700, 50),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            parent.Controls.Add(welcomeLabel);
            parent.Controls.Add(descLabel);
        }

        private void CreateStatisticsCards(Panel parent)
        {
            // الحصول على الإحصائيات الحقيقية من قاعدة البيانات
            var realStats = GetRealStatistics();
            
            var statistics = new[]
            {
                new { Title = "عدد الأقسام", Value = realStats.DepartmentsCount.ToString(), Icon = "📁", Color = Color.FromArgb(52, 152, 219) },
                new { Title = "عدد الأضابير", Value = realStats.FoldersCount.ToString(), Icon = "📂", Color = Color.FromArgb(46, 204, 113) },
                new { Title = "الكتب الصادرة", Value = realStats.OutgoingDocuments.ToString(), Icon = "📤", Color = Color.FromArgb(231, 76, 60) },
                new { Title = "الكتب الواردة", Value = realStats.IncomingDocuments.ToString(), Icon = "📥", Color = Color.FromArgb(155, 89, 182) }
            };

            int cardWidth = 200;
            int cardHeight = 120;
            int startX = 50;
            int startY = 130;
            int spacing = 220;

            for (int i = 0; i < statistics.Length; i++)
            {
                var stat = statistics[i];
                var card = CreateStatCard(stat.Title, stat.Value, stat.Icon, stat.Color);
                card.Location = new Point(startX + (i * spacing), startY);
                card.Size = new Size(cardWidth, cardHeight);
                parent.Controls.Add(card);
            }
        }

        private Panel CreateStatCard(string title, string value, string icon, Color color)
        {
            var card = RTLHelper.CreateStyledPanel(Point.Empty, Size.Empty, Color.White, BorderStyle.FixedSingle);

            var iconLabel = RTLHelper.CreateStyledLabel(
                icon,
                new Point(150, 15),
                new Size(50, 40),
                new Font("Segoe UI Emoji", 24F),
                color,
                ContentAlignment.MiddleCenter
            );

            var valueLabel = RTLHelper.CreateStyledLabel(
                value,
                new Point(70, 15),
                new Size(120, 35),
                RTLHelper.ArabicFontHeader,
                color,
                ContentAlignment.MiddleRight
            );

            var titleLabel = RTLHelper.CreateStyledLabel(
                title,
                new Point(10, 70),
                new Size(180, 25),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            // إضافة تأثير الظل
            card.Paint += (s, e) => {
                var rect = new Rectangle(0, 0, card.Width - 1, card.Height - 1);
                using (var pen = new Pen(Color.FromArgb(200, 200, 200)))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            card.Controls.Add(iconLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(titleLabel);

            return card;
        }

        private void CreateQuickActions(Panel parent)
        {
            var actionsLabel = RTLHelper.CreateStyledLabel(
                "الإجراءات السريعة",
                new Point(30, 280),
                new Size(200, 30),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );
            parent.Controls.Add(actionsLabel);

            var addDocumentButton = RTLHelper.CreateStyledButton(
                "📄 إضافة وثيقة جديدة",
                new Point(50, 320),
                new Size(220, 55),
                RTLHelper.SuccessColor,
                delegate { OpenDocumentsForm(); }
            );

            var viewDocumentsButton = RTLHelper.CreateStyledButton(
                "📋 عرض جميع الوثائق",
                new Point(290, 320),
                new Size(220, 55),
                RTLHelper.AccentColor,
                delegate { OpenDocumentsForm(); }
            );

            var manageDepartmentsButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأقسام",
                new Point(530, 320),
                new Size(220, 55),
                RTLHelper.InfoColor,
                delegate { OpenDepartmentsForm(); }
            );

            // تطبيق تأثيرات بصرية
            RTLHelper.ApplyButtonEffects(addDocumentButton);
            RTLHelper.ApplyButtonEffects(viewDocumentsButton);
            RTLHelper.ApplyButtonEffects(manageDepartmentsButton);

            parent.Controls.Add(addDocumentButton);
            parent.Controls.Add(viewDocumentsButton);
            parent.Controls.Add(manageDepartmentsButton);
        }

        private void MenuButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button != null)
            {
                if (button == _departmentsButton)
                {
                    OpenDepartmentsForm();
                }
                else if (button == _documentsButton)
                {
                    OpenDocumentsForm();
                }
                else if (button == _searchButton)
                {
                    OpenSearchForm();
                }
                else if (button == _settingsButton)
                {
                    OpenSettingsForm();
                }
            }
        }

        private void OpenDepartmentsForm()
        {
            try
            {
                var departmentsForm = new SimpleDepartmentsForm();
                departmentsForm.ShowDialog();
                
                // تحديث الإحصائيات بعد إغلاق النافذة
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نافذة الأقسام: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenDocumentsForm()
        {
            try
            {
                var documentsForm = new SimpleDocumentsForm();
                documentsForm.ShowDialog();

                // تحديث الإحصائيات بعد إغلاق النافذة
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نافذة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSearchForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة البحث المتقدم قريباً\nهذه الميزة قيد التطوير", "البحث المتقدم",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OpenSettingsForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة الإعدادات قريباً\nهذه الميزة قيد التطوير", "إعدادات النظام",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void RefreshStatistics()
        {
            // إعادة إنشاء البطاقات الإحصائية
            this.Controls.Clear();
            CreateControls();
        }

        private void SetApplicationIcon()
        {
            try
            {
                // محاولة تحميل الأيقونة من الملف
                if (File.Exists("AppIcon.ico"))
                {
                    this.Icon = new Icon("AppIcon.ico");
                }
                else
                {
                    // إنشاء أيقونة افتراضية إذا لم يوجد الملف
                    IconManager.SetFormIcon(this, "MainApp.ico");
                }
            }
            catch
            {
                // في حالة فشل تحميل الأيقونة، نتجاهل الخطأ
            }
        }

        private StatisticsData GetRealStatistics()
        {
            try
            {
                int departmentsCount = _departmentRepo.GetTotalCount();
                int foldersCount = _dataManager.GetFoldersCount();
                int outgoingDocs = _dataManager.GetDocumentsCount("صادر");
                int incomingDocs = _dataManager.GetDocumentsCount("وارد");

                return new StatisticsData
                {
                    DepartmentsCount = departmentsCount,
                    FoldersCount = foldersCount,
                    OutgoingDocuments = outgoingDocs,
                    IncomingDocuments = incomingDocs
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في جلب الإحصائيات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return new StatisticsData { DepartmentsCount = 0, FoldersCount = 0, OutgoingDocuments = 0, IncomingDocuments = 0 };
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Name = "SimpleMainForm";
            this.ResumeLayout(false);
        }
    }

    public class StatisticsData
    {
        public int DepartmentsCount { get; set; }
        public int FoldersCount { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
    }
}
