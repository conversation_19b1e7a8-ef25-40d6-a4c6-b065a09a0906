using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;

namespace ArchiveSystem
{
    public partial class SimpleMainForm : Form
    {
        private Button _departmentsButton;
        private Button _documentsButton;
        private Button _searchButton;
        private Button _settingsButton;
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;

        public SimpleMainForm()
        {
            InitializeComponent();
            InitializeData();
            SetupForm();
            CreateControls();
            SetApplicationIcon();
        }

        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void SetupForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية المتقدم";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(236, 240, 241);
            this.WindowState = FormWindowState.Maximized;
            this.MinimumSize = new Size(1000, 600);
        }

        private void CreateControls()
        {
            CreateHeader();
            CreateSidebar();
            CreateMainContent();
        }

        private void CreateHeader()
        {
            var headerPanel = new Panel();
            headerPanel.Dock = DockStyle.Top;
            headerPanel.Height = 80;
            headerPanel.BackColor = Color.FromArgb(52, 73, 94);
            headerPanel.RightToLeft = RightToLeft.Yes;

            var titleLabel = new Label();
            titleLabel.Text = "نظام الأرشفة الإلكترونية المتقدم";
            titleLabel.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(50, 15);
            titleLabel.Size = new Size(500, 35);
            titleLabel.TextAlign = ContentAlignment.MiddleRight;
            titleLabel.RightToLeft = RightToLeft.Yes;

            var subtitleLabel = new Label();
            subtitleLabel.Text = "إدارة شاملة للوثائق والمراسلات الرسمية";
            subtitleLabel.Font = new Font("Tahoma", 12F);
            subtitleLabel.ForeColor = Color.FromArgb(189, 195, 199);
            subtitleLabel.Location = new Point(50, 45);
            subtitleLabel.Size = new Size(600, 25);
            subtitleLabel.TextAlign = ContentAlignment.MiddleRight;
            subtitleLabel.RightToLeft = RightToLeft.Yes;

            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(subtitleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreateSidebar()
        {
            var sidebarPanel = new Panel();
            sidebarPanel.Dock = DockStyle.Right;
            sidebarPanel.Width = 280;
            sidebarPanel.BackColor = Color.FromArgb(44, 62, 80);
            sidebarPanel.RightToLeft = RightToLeft.Yes;

            var sidebarTitle = new Label();
            sidebarTitle.Text = "القائمة الرئيسية";
            sidebarTitle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            sidebarTitle.ForeColor = Color.White;
            sidebarTitle.Location = new Point(20, 20);
            sidebarTitle.Size = new Size(240, 30);
            sidebarTitle.TextAlign = ContentAlignment.MiddleRight;
            sidebarTitle.RightToLeft = RightToLeft.Yes;
            sidebarPanel.Controls.Add(sidebarTitle);

            CreateMenuButtons(sidebarPanel);
            this.Controls.Add(sidebarPanel);
        }

        private void CreateMenuButtons(Panel parent)
        {
            int buttonWidth = 230;
            int buttonHeight = 55;
            int startY = 80;
            int spacing = 70;

            _departmentsButton = CreateStyledButton("📁 إدارة الأقسام", 
                new Point(25, startY), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(52, 152, 219));

            _documentsButton = CreateStyledButton("📄 إدارة الوثائق", 
                new Point(25, startY + spacing), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(155, 89, 182));

            _searchButton = CreateStyledButton("🔍 البحث المتقدم", 
                new Point(25, startY + spacing * 2), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(243, 156, 18));

            _settingsButton = CreateStyledButton("⚙️ الإعدادات", 
                new Point(25, startY + spacing * 3), new Size(buttonWidth, buttonHeight), 
                Color.FromArgb(127, 140, 141));

            _departmentsButton.Click += MenuButton_Click;
            _documentsButton.Click += MenuButton_Click;
            _searchButton.Click += MenuButton_Click;
            _settingsButton.Click += MenuButton_Click;

            parent.Controls.Add(_departmentsButton);
            parent.Controls.Add(_documentsButton);
            parent.Controls.Add(_searchButton);
            parent.Controls.Add(_settingsButton);
        }

        private Button CreateStyledButton(string text, Point location, Size size, Color backgroundColor)
        {
            var button = new Button();
            button.Text = text;
            button.Location = location;
            button.Size = size;
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleCenter;
            button.Cursor = Cursors.Hand;
            button.RightToLeft = RightToLeft.Yes;
            button.FlatAppearance.BorderSize = 0;
            return button;
        }

        private void CreateMainContent()
        {
            var mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.BackColor = Color.FromArgb(236, 240, 241);
            mainPanel.Padding = new Padding(30);
            mainPanel.RightToLeft = RightToLeft.Yes;

            CreateWelcomeSection(mainPanel);
            CreateStatisticsCards(mainPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreateWelcomeSection(Panel parent)
        {
            var welcomeLabel = new Label();
            welcomeLabel.Text = "مرحباً بك في نظام الأرشفة الإلكترونية المتقدم";
            welcomeLabel.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            welcomeLabel.ForeColor = Color.FromArgb(52, 73, 94);
            welcomeLabel.Location = new Point(30, 30);
            welcomeLabel.Size = new Size(600, 40);
            welcomeLabel.TextAlign = ContentAlignment.MiddleRight;
            welcomeLabel.RightToLeft = RightToLeft.Yes;

            var descLabel = new Label();
            descLabel.Text = "نظام متكامل لإدارة الوثائق والمراسلات الرسمية";
            descLabel.Font = new Font("Tahoma", 12F);
            descLabel.ForeColor = Color.FromArgb(127, 140, 141);
            descLabel.Location = new Point(30, 75);
            descLabel.Size = new Size(700, 50);
            descLabel.TextAlign = ContentAlignment.MiddleRight;
            descLabel.RightToLeft = RightToLeft.Yes;

            parent.Controls.Add(welcomeLabel);
            parent.Controls.Add(descLabel);
        }

        private void CreateStatisticsCards(Panel parent)
        {
            var realStats = GetRealStatistics();
            
            var statistics = new[]
            {
                new { Title = "عدد الأقسام", Value = realStats.DepartmentsCount.ToString(), Icon = "📁", Color = Color.FromArgb(52, 152, 219) },
                new { Title = "عدد الوثائق", Value = realStats.FoldersCount.ToString(), Icon = "📄", Color = Color.FromArgb(46, 204, 113) },
                new { Title = "الكتب الصادرة", Value = realStats.OutgoingDocuments.ToString(), Icon = "📤", Color = Color.FromArgb(231, 76, 60) },
                new { Title = "الكتب الواردة", Value = realStats.IncomingDocuments.ToString(), Icon = "📥", Color = Color.FromArgb(155, 89, 182) }
            };

            int cardWidth = 200;
            int cardHeight = 120;
            int startX = 50;
            int startY = 130;
            int spacing = 220;

            for (int i = 0; i < statistics.Length; i++)
            {
                var stat = statistics[i];
                var card = CreateStatCard(stat.Title, stat.Value, stat.Icon, stat.Color);
                card.Location = new Point(startX + (i * spacing), startY);
                card.Size = new Size(cardWidth, cardHeight);
                parent.Controls.Add(card);
            }
        }

        private Panel CreateStatCard(string title, string value, string icon, Color color)
        {
            var card = new Panel();
            card.BackColor = Color.White;
            card.BorderStyle = BorderStyle.FixedSingle;
            card.RightToLeft = RightToLeft.Yes;

            var iconLabel = new Label();
            iconLabel.Text = icon;
            iconLabel.Font = new Font("Segoe UI Emoji", 24F);
            iconLabel.Location = new Point(150, 15);
            iconLabel.Size = new Size(50, 40);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;

            var valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Tahoma", 20F, FontStyle.Bold);
            valueLabel.ForeColor = color;
            valueLabel.Location = new Point(70, 15);
            valueLabel.Size = new Size(120, 35);
            valueLabel.TextAlign = ContentAlignment.MiddleRight;
            valueLabel.RightToLeft = RightToLeft.Yes;

            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Tahoma", 11F);
            titleLabel.ForeColor = Color.FromArgb(127, 140, 141);
            titleLabel.Location = new Point(10, 70);
            titleLabel.Size = new Size(180, 25);
            titleLabel.TextAlign = ContentAlignment.MiddleRight;
            titleLabel.RightToLeft = RightToLeft.Yes;

            card.Controls.Add(iconLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(titleLabel);

            return card;
        }

        private void MenuButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button != null)
            {
                if (button == _departmentsButton)
                {
                    OpenDepartmentsForm();
                }
                else if (button == _documentsButton)
                {
                    OpenDocumentsForm();
                }
                else if (button == _searchButton)
                {
                    OpenSearchForm();
                }
                else if (button == _settingsButton)
                {
                    OpenSettingsForm();
                }
            }
        }

        private void OpenDepartmentsForm()
        {
            try
            {
                var departmentsForm = new SimpleDepartmentsForm();
                departmentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نافذة الأقسام: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenDocumentsForm()
        {
            try
            {
                var documentsForm = new SimpleDocumentsForm();
                documentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نافذة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSearchForm()
        {
            MessageBox.Show("سيتم فتح نافذة البحث المتقدم قريباً\nهذه الميزة قيد التطوير", "البحث المتقدم",
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        private void OpenSettingsForm()
        {
            MessageBox.Show("سيتم فتح نافذة الإعدادات قريباً\nهذه الميزة قيد التطوير", "إعدادات النظام",
                          MessageBoxButtons.OK, MessageBoxIcon.Information,
                          MessageBoxDefaultButton.Button1,
                          MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        private void SetApplicationIcon()
        {
            try
            {
                if (File.Exists("AppIcon.ico"))
                {
                    this.Icon = new Icon("AppIcon.ico");
                }
            }
            catch
            {
                // تجاهل أخطاء الأيقونة
            }
        }

        private StatisticsData GetRealStatistics()
        {
            try
            {
                int departmentsCount = _departmentRepo.GetTotalCount();
                int foldersCount = _dataManager.GetFoldersCount();
                int outgoingDocs = _dataManager.GetDocumentsCount("صادر");
                int incomingDocs = _dataManager.GetDocumentsCount("وارد");

                return new StatisticsData
                {
                    DepartmentsCount = departmentsCount,
                    FoldersCount = foldersCount,
                    OutgoingDocuments = outgoingDocs,
                    IncomingDocuments = incomingDocs
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في جلب الإحصائيات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return new StatisticsData { DepartmentsCount = 0, FoldersCount = 0, OutgoingDocuments = 0, IncomingDocuments = 0 };
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Name = "SimpleMainForm";
            this.ResumeLayout(false);
        }
    }

    public class StatisticsData
    {
        public int DepartmentsCount { get; set; }
        public int FoldersCount { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
    }
}
