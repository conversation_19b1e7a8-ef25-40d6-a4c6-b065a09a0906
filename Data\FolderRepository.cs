using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using ArchiveSystem.Models;

namespace ArchiveSystem.Data
{
    /// <summary>
    /// مستودع بيانات الأضبارات
    /// </summary>
    public class FolderRepository
    {
        #region Fields
        private readonly string _foldersFile;
        private readonly SimpleDepartmentRepository _departmentRepo;
        #endregion

        #region Constructor
        public FolderRepository()
        {
            _foldersFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Folders.xml");
            _departmentRepo = new SimpleDepartmentRepository();
            EnsureDataFileExists();
        }
        #endregion

        #region File Management
        private void EnsureDataFileExists()
        {
            try
            {
                string dataDir = Path.GetDirectoryName(_foldersFile);
                if (!Directory.Exists(dataDir))
                {
                    Directory.CreateDirectory(dataDir);
                }

                if (!File.Exists(_foldersFile))
                {
                    CreateEmptyDataFile();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في إنشاء ملف بيانات الأضبارات: " + ex.Message);
            }
        }

        private void CreateEmptyDataFile()
        {
            var doc = new XmlDocument();
            var declaration = doc.CreateXmlDeclaration("1.0", "UTF-8", null);
            doc.AppendChild(declaration);

            var root = doc.CreateElement("Folders");
            doc.AppendChild(root);

            doc.Save(_foldersFile);
        }
        #endregion

        #region CRUD Operations
        /// <summary>
        /// الحصول على جميع الأضبارات
        /// </summary>
        public List<Folder> GetAll()
        {
            var folders = new List<Folder>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                var folderNodes = doc.SelectNodes("//Folder");
                foreach (XmlNode node in folderNodes)
                {
                    var folder = ParseFolderFromXml(node);
                    if (folder != null)
                    {
                        folders.Add(folder);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة الأضبارات: " + ex.Message);
            }

            return folders;
        }

        /// <summary>
        /// الحصول على أضبارة بالمعرف
        /// </summary>
        public Folder GetById(int folderId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                var node = doc.SelectSingleNode("//Folder[@FolderId='" + folderId + "']");
                return node != null ? ParseFolderFromXml(node) : null;
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في البحث عن الأضبارة: " + ex.Message);
            }
        }

        /// <summary>
        /// الحصول على أضبارات قسم معين
        /// </summary>
        public List<Folder> GetByDepartment(int departmentId)
        {
            var folders = new List<Folder>();

            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                var folderNodes = doc.SelectNodes("//Folder[@DepartmentId='" + departmentId + "' and @IsActive='true']");
                foreach (XmlNode node in folderNodes)
                {
                    var folder = ParseFolderFromXml(node);
                    if (folder != null)
                    {
                        folders.Add(folder);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في قراءة أضبارات القسم: " + ex.Message);
            }

            return folders;
        }

        /// <summary>
        /// إضافة أضبارة جديدة
        /// </summary>
        public bool Insert(Folder folder)
        {
            try
            {
                List<string> errors;
                if (!folder.IsValid(out errors))
                {
                    throw new Exception("بيانات الأضبارة غير صحيحة: " + string.Join(", ", errors.ToArray()));
                }

                // التحقق من عدم تكرار اسم الأضبارة في نفس القسم
                if (IsFolderNameExists(folder.FolderName, folder.DepartmentId))
                {
                    throw new Exception("اسم الأضبارة موجود مسبقاً في هذا القسم");
                }

                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                // إنشاء معرف جديد
                folder.FolderId = GetNextFolderId();
                folder.CreatedDate = DateTime.Now;

                // إنشاء عقدة XML
                var folderElement = CreateFolderXmlElement(doc, folder);
                doc.DocumentElement.AppendChild(folderElement);

                doc.Save(_foldersFile);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في إضافة الأضبارة: " + ex.Message);
            }
        }

        /// <summary>
        /// تحديث أضبارة
        /// </summary>
        public bool Update(Folder folder)
        {
            try
            {
                List<string> errors;
                if (!folder.IsValid(out errors))
                {
                    throw new Exception("بيانات الأضبارة غير صحيحة: " + string.Join(", ", errors.ToArray()));
                }

                // التحقق من عدم تكرار اسم الأضبارة (استثناء الأضبارة الحالية)
                if (IsFolderNameExists(folder.FolderName, folder.DepartmentId, folder.FolderId))
                {
                    throw new Exception("اسم الأضبارة موجود مسبقاً في هذا القسم");
                }

                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                var existingNode = doc.SelectSingleNode("//Folder[@FolderId='" + folder.FolderId + "']");
                if (existingNode != null)
                {
                    // تحديث البيانات
                    existingNode.Attributes["FolderName"].Value = folder.FolderName;
                    existingNode.Attributes["DepartmentId"].Value = folder.DepartmentId.ToString();
                    existingNode.Attributes["FolderDate"].Value = folder.FolderDate.ToString("yyyy-MM-dd");

                    doc.Save(_foldersFile);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في تحديث الأضبارة: " + ex.Message);
            }
        }

        /// <summary>
        /// حذف أضبارة (حذف منطقي)
        /// </summary>
        public bool Delete(int folderId)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                var folderNode = doc.SelectSingleNode("//Folder[@FolderId='" + folderId + "']");
                if (folderNode != null)
                {
                    folderNode.Attributes["IsActive"].Value = "false";
                    doc.Save(_foldersFile);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في حذف الأضبارة: " + ex.Message);
            }
        }
        #endregion

        #region Helper Methods
        private Folder ParseFolderFromXml(XmlNode node)
        {
            try
            {
                var folder = new Folder
                {
                    FolderId = int.Parse(node.Attributes["FolderId"].Value),
                    FolderName = node.Attributes["FolderName"].Value,
                    DepartmentId = int.Parse(node.Attributes["DepartmentId"].Value),
                    FolderDate = DateTime.Parse(node.Attributes["FolderDate"].Value),
                    CreatedDate = DateTime.Parse(node.Attributes["CreatedDate"].Value),
                    CreatedBy = node.Attributes["CreatedBy"] != null ? node.Attributes["CreatedBy"].Value : "النظام",
                    IsActive = bool.Parse(node.Attributes["IsActive"].Value)
                };

                // تحميل اسم القسم
                var department = _departmentRepo.GetById(folder.DepartmentId);
                folder.DepartmentName = department != null ? department.DepartmentName : "قسم محذوف";

                return folder;
            }
            catch
            {
                return null;
            }
        }

        private XmlElement CreateFolderXmlElement(XmlDocument doc, Folder folder)
        {
            var element = doc.CreateElement("Folder");
            element.SetAttribute("FolderId", folder.FolderId.ToString());
            element.SetAttribute("FolderName", folder.FolderName);
            element.SetAttribute("DepartmentId", folder.DepartmentId.ToString());
            element.SetAttribute("FolderDate", folder.FolderDate.ToString("yyyy-MM-dd"));
            element.SetAttribute("CreatedDate", folder.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
            element.SetAttribute("CreatedBy", folder.CreatedBy ?? "النظام");
            element.SetAttribute("IsActive", folder.IsActive.ToString());
            return element;
        }

        private int GetNextFolderId()
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                int maxId = 0;
                var folderNodes = doc.SelectNodes("//Folder");
                foreach (XmlNode node in folderNodes)
                {
                    int id = int.Parse(node.Attributes["FolderId"].Value);
                    if (id > maxId)
                        maxId = id;
                }

                return maxId + 1;
            }
            catch
            {
                return 1;
            }
        }

        private bool IsFolderNameExists(string folderName, int departmentId, int excludeFolderId = 0)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_foldersFile);

                string xpath = "//Folder[@FolderName='" + folderName + "' and @DepartmentId='" + departmentId + "' and @IsActive='true'";
                if (excludeFolderId > 0)
                {
                    xpath += " and @FolderId!='" + excludeFolderId + "'";
                }
                xpath += "]";

                var existingNode = doc.SelectSingleNode(xpath);
                return existingNode != null;
            }
            catch
            {
                return false;
            }
        }
        #endregion
    }
}
