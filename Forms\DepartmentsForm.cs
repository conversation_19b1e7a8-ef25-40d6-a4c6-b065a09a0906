using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة إدارة الأقسام
    /// </summary>
    public partial class DepartmentsForm : Form
    {
        private readonly DepartmentRepository _departmentRepository;
        private readonly FolderRepository _folderRepository;
        private readonly DocumentRepository _documentRepository;

        private FlowLayoutPanel _departmentsPanel;

        public DepartmentsForm()
        {
            InitializeComponent();
            _departmentRepository = new DepartmentRepository();
            _folderRepository = new FolderRepository();
            _documentRepository = new DocumentRepository();
            InitializeControls();
            LoadDepartments();
        }

        private void InitializeControls()
        {
            this.Text = "إدارة الأقسام";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 242, 247);

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "إدارة الأقسام",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(50, 30),
                Size = new Size(200, 40),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // زر إضافة قسم جديد
            var addButton = new Button
            {
                Text = "➕ إضافة قسم جديد",
                Location = new Point(50, 80),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += AddButton_Click;
            this.Controls.Add(addButton);

            // منطقة عرض الأقسام
            _departmentsPanel = new FlowLayoutPanel
            {
                Location = new Point(50, 140),
                Size = new Size(1100, 600),
                AutoScroll = true,
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = true,
                BackColor = Color.Transparent
            };
            this.Controls.Add(_departmentsPanel);
        }

        private void LoadDepartments()
        {
            try
            {
                _departmentsPanel.Controls.Clear();
                var departments = _departmentRepository.GetAll();

                foreach (var department in departments)
                {
                    var departmentCard = CreateDepartmentCard(department);
                    _departmentsPanel.Controls.Add(departmentCard);
                }

                if (departments.Count == 0)
                {
                    var noDataLabel = new Label
                    {
                        Text = "لا توجد أقسام مضافة",
                        Font = new Font("Tahoma", 14F, FontStyle.Bold),
                        ForeColor = Color.FromArgb(127, 140, 141),
                        Size = new Size(300, 50),
                        TextAlign = ContentAlignment.MiddleCenter
                    };
                    _departmentsPanel.Controls.Add(noDataLabel);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Panel CreateDepartmentCard(Department department)
        {
            var card = new Panel
            {
                Size = new Size(320, 200),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Margin = new Padding(10)
            };

            // إضافة ظل للبطاقة
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width, card.Height);
                using (var brush = new SolidBrush(Color.FromArgb(230, 230, 230)))
                {
                    e.Graphics.FillRectangle(brush, rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                }
                using (var brush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(brush, rect);
                }
                using (var pen = new Pen(Color.FromArgb(220, 220, 220)))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            // أيقونة القسم
            var iconLabel = new Label
            {
                Text = "📁",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(15, 15),
                Size = new Size(50, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            card.Controls.Add(iconLabel);

            // اسم القسم
            var nameLabel = new Label
            {
                Text = department.DepartmentName,
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(80, 15),
                Size = new Size(220, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            card.Controls.Add(nameLabel);

            // تاريخ الإنشاء
            var dateLabel = new Label
            {
                Text = "تاريخ الإنشاء: " + department.CreatedDate.ToString("yyyy/MM/dd"),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(80, 45),
                Size = new Size(220, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            card.Controls.Add(dateLabel);

            // الوصف
            if (!string.IsNullOrEmpty(department.Description))
            {
                var descLabel = new Label
                {
                    Text = department.Description.Length > 50 ? 
                           department.Description.Substring(0, 50) + "..." : 
                           department.Description,
                    Font = new Font("Tahoma", 9F),
                    ForeColor = Color.FromArgb(127, 140, 141),
                    Location = new Point(15, 75),
                    Size = new Size(285, 40),
                    TextAlign = ContentAlignment.TopRight
                };
                card.Controls.Add(descLabel);
            }

            // إحصائيات
            var foldersCount = _folderRepository.GetByDepartmentId(department.DepartmentId).Count;
            var documentsCount = _documentRepository.Search("", department.DepartmentId).Count;

            var statsLabel = new Label
            {
                Text = "الأضابير: " + foldersCount + " | الوثائق: " + documentsCount,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(15, 120),
                Size = new Size(285, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            card.Controls.Add(statsLabel);

            // أزرار الإجراءات
            var openButton = new Button
            {
                Text = "فتح",
                Location = new Point(15, 155),
                Size = new Size(60, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F, FontStyle.Bold)
            };
            openButton.Click += (s, e) => OpenDepartment(department);
            card.Controls.Add(openButton);

            var editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(85, 155),
                Size = new Size(60, 30),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F, FontStyle.Bold)
            };
            editButton.Click += (s, e) => EditDepartment(department);
            card.Controls.Add(editButton);

            var deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(155, 155),
                Size = new Size(60, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F, FontStyle.Bold)
            };
            deleteButton.Click += (s, e) => DeleteDepartment(department);
            card.Controls.Add(deleteButton);

            var foldersButton = new Button
            {
                Text = "الأضابير",
                Location = new Point(225, 155),
                Size = new Size(75, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F, FontStyle.Bold)
            };
            foldersButton.Click += (s, e) => ShowDepartmentFolders(department);
            card.Controls.Add(foldersButton);

            return card;
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            var addForm = new AddDepartmentForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadDepartments();
            }
        }

        private void OpenDepartment(Department department)
        {
            var documentsForm = new ViewDocumentsForm();
            documentsForm.ShowDialog();
        }

        private void EditDepartment(Department department)
        {
            var editForm = new EditDepartmentForm(department);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadDepartments();
            }
        }

        private void DeleteDepartment(Department department)
        {
            try
            {
                // التحقق من إمكانية الحذف
                if (!_departmentRepository.CanDelete(department.DepartmentId))
                {
                    MessageBox.Show("لا يمكن حذف هذا القسم لأنه يحتوي على أضابير أو وثائق مرتبطة به", 
                                  "لا يمكن الحذف", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف القسم '" + department.DepartmentName + "'؟",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (_departmentRepository.Delete(department.DepartmentId))
                    {
                        MessageBox.Show("تم حذف القسم بنجاح", "تم الحذف", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadDepartments();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف القسم", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حذف القسم: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowDepartmentFolders(Department department)
        {
            var foldersForm = new DepartmentFoldersForm(department);
            foldersForm.ShowDialog();
        }
    }
}
