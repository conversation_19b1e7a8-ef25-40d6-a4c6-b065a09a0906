﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
    </startup>

    <connectionStrings>
        <add name="ArchiveDatabase"
             connectionString="Data Source=ArchiveDatabase.db;Version=3;"
             providerName="System.Data.SQLite" />
    </connectionStrings>

    <appSettings>
        <add key="ApplicationName" value="نظام الأرشفة الإلكترونية" />
        <add key="Version" value="1.0.0" />
        <add key="DatabaseType" value="SQLite" />
        <add key="AttachmentsPath" value="Attachments" />
        <add key="BackupsPath" value="Backups" />
        <add key="MaxFileSize" value="50" />
        <add key="SupportedImageFormats" value=".jpg,.jpeg,.png,.bmp,.tiff,.gif" />
        <add key="SupportedDocumentFormats" value=".pdf,.doc,.docx,.txt,.rtf" />
    </appSettings>
</configuration>