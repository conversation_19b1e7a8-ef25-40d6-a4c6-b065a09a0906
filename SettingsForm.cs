using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// نافذة إعدادات النظام
    /// </summary>
    public partial class SettingsForm : Form
    {
        #region Fields
        // General Settings Controls
        private TextBox _attachmentsPathTextBox;
        private Button _browseAttachmentsButton;
        private TextBox _backupPathTextBox;
        private Button _browseBackupButton;
        private CheckBox _autoBackupCheckBox;
        private ComboBox _backupIntervalComboBox;
        
        // Display Settings Controls
        private ComboBox _fontSizeComboBox;
        private ComboBox _themeComboBox;
        private CheckBox _showNotificationsCheckBox;
        private CheckBox _confirmDeleteCheckBox;
        
        // Action Controls
        private Button _saveButton;
        private Button _restoreDefaultsButton;
        private Button _cancelButton;
        private Button _createBackupButton;
        
        // Panels
        private TabControl _settingsTabControl;
        private TabPage _generalTabPage;
        private TabPage _displayTabPage;
        private Panel _bottomPanel;
        #endregion

        #region Constructor
        public SettingsForm()
        {
            InitializeComponent();
            SetupForm();
            CreateControls();
            LoadCurrentSettings();
            
            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Settings.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Form Setup
        private void SetupForm()
        {
            this.Text = "⚙️ إعدادات النظام";
            this.Size = new Size(700, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }

        private void CreateControls()
        {
            CreateTabControl();
            CreateGeneralTab();
            CreateDisplayTab();
            CreateBottomPanel();
        }

        private void CreateTabControl()
        {
            _settingsTabControl = new TabControl
            {
                Location = new Point(20, 20),
                Size = new Size(this.Width - 60, this.Height - 120),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Font = RTLHelper.ArabicFont
            };

            _generalTabPage = new TabPage("الإعدادات العامة");
            _displayTabPage = new TabPage("إعدادات العرض");

            _settingsTabControl.TabPages.Add(_generalTabPage);
            _settingsTabControl.TabPages.Add(_displayTabPage);

            this.Controls.Add(_settingsTabControl);
        }

        private void CreateGeneralTab()
        {
            int startX = 30;
            int startY = 30;
            int labelWidth = 150;
            int fieldWidth = 300;
            int fieldHeight = 25;
            int spacing = 40;
            int currentY = startY;

            // مسار حفظ المرفقات
            var attachmentsLabel = RTLHelper.CreateStyledLabel(
                "مسار حفظ المرفقات:",
                new Point(startX + fieldWidth + 50, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _attachmentsPathTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                true
            );

            _browseAttachmentsButton = RTLHelper.CreateStyledButton(
                "...",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(30, fieldHeight),
                RTLHelper.InfoColor,
                BrowseAttachmentsButton_Click
            );

            _generalTabPage.Controls.Add(attachmentsLabel);
            _generalTabPage.Controls.Add(_attachmentsPathTextBox);
            _generalTabPage.Controls.Add(_browseAttachmentsButton);
            currentY += spacing;

            // مسار النسخ الاحتياطي
            var backupLabel = RTLHelper.CreateStyledLabel(
                "مسار النسخ الاحتياطي:",
                new Point(startX + fieldWidth + 50, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _backupPathTextBox = RTLHelper.CreateStyledTextBox(
                new Point(startX, currentY),
                new Size(fieldWidth, fieldHeight),
                true
            );

            _browseBackupButton = RTLHelper.CreateStyledButton(
                "...",
                new Point(startX + fieldWidth + 10, currentY),
                new Size(30, fieldHeight),
                RTLHelper.InfoColor,
                BrowseBackupButton_Click
            );

            _generalTabPage.Controls.Add(backupLabel);
            _generalTabPage.Controls.Add(_backupPathTextBox);
            _generalTabPage.Controls.Add(_browseBackupButton);
            currentY += spacing;

            // النسخ الاحتياطي التلقائي
            _autoBackupCheckBox = new CheckBox
            {
                Text = "تفعيل النسخ الاحتياطي التلقائي",
                Location = new Point(startX + fieldWidth + 50, currentY),
                Size = new Size(labelWidth, fieldHeight),
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont,
                CheckAlign = ContentAlignment.MiddleRight
            };
            _autoBackupCheckBox.CheckedChanged += AutoBackupCheckBox_CheckedChanged;

            _backupIntervalComboBox = new ComboBox
            {
                Location = new Point(startX, currentY),
                Size = new Size(fieldWidth, fieldHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont,
                Enabled = false
            };
            _backupIntervalComboBox.Items.AddRange(new object[] { "يومياً", "أسبوعياً", "شهرياً" });
            _backupIntervalComboBox.SelectedIndex = 0;

            _generalTabPage.Controls.Add(_autoBackupCheckBox);
            _generalTabPage.Controls.Add(_backupIntervalComboBox);
            currentY += spacing;

            // زر إنشاء نسخة احتياطية يدوية
            _createBackupButton = RTLHelper.CreateStyledButton(
                "📦 إنشاء نسخة احتياطية الآن",
                new Point(startX, currentY),
                new Size(200, 35),
                RTLHelper.AccentColor,
                CreateBackupButton_Click
            );

            _generalTabPage.Controls.Add(_createBackupButton);
        }

        private void CreateDisplayTab()
        {
            int startX = 30;
            int startY = 30;
            int labelWidth = 150;
            int fieldWidth = 200;
            int fieldHeight = 25;
            int spacing = 40;
            int currentY = startY;

            // حجم الخط
            var fontSizeLabel = RTLHelper.CreateStyledLabel(
                "حجم الخط:",
                new Point(startX + fieldWidth + 50, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _fontSizeComboBox = new ComboBox
            {
                Location = new Point(startX, currentY),
                Size = new Size(fieldWidth, fieldHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont
            };
            _fontSizeComboBox.Items.AddRange(new object[] { "صغير", "متوسط", "كبير" });
            _fontSizeComboBox.SelectedIndex = 1;

            _displayTabPage.Controls.Add(fontSizeLabel);
            _displayTabPage.Controls.Add(_fontSizeComboBox);
            currentY += spacing;

            // المظهر
            var themeLabel = RTLHelper.CreateStyledLabel(
                "مظهر النظام:",
                new Point(startX + fieldWidth + 50, currentY),
                new Size(labelWidth, fieldHeight),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _themeComboBox = new ComboBox
            {
                Location = new Point(startX, currentY),
                Size = new Size(fieldWidth, fieldHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont
            };
            _themeComboBox.Items.AddRange(new object[] { "فاتح", "داكن", "تلقائي" });
            _themeComboBox.SelectedIndex = 0;

            _displayTabPage.Controls.Add(themeLabel);
            _displayTabPage.Controls.Add(_themeComboBox);
            currentY += spacing;

            // إظهار الإشعارات
            _showNotificationsCheckBox = new CheckBox
            {
                Text = "إظهار الإشعارات",
                Location = new Point(startX + fieldWidth + 50, currentY),
                Size = new Size(labelWidth, fieldHeight),
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont,
                CheckAlign = ContentAlignment.MiddleRight,
                Checked = true
            };

            _displayTabPage.Controls.Add(_showNotificationsCheckBox);
            currentY += spacing;

            // تأكيد الحذف
            _confirmDeleteCheckBox = new CheckBox
            {
                Text = "تأكيد عمليات الحذف",
                Location = new Point(startX + fieldWidth + 50, currentY),
                Size = new Size(labelWidth, fieldHeight),
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont,
                CheckAlign = ContentAlignment.MiddleRight,
                Checked = true
            };

            _displayTabPage.Controls.Add(_confirmDeleteCheckBox);
        }

        private void CreateBottomPanel()
        {
            _bottomPanel = new Panel
            {
                Location = new Point(0, this.Height - 80),
                Size = new Size(this.Width, 60),
                BackColor = RTLHelper.LightColor,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            int buttonWidth = 120;
            int buttonHeight = 35;
            int spacing = 20;
            int totalButtonsWidth = (buttonWidth * 3) + (spacing * 2);
            int startX = (this.Width - totalButtonsWidth) / 2;
            int buttonY = 15;

            _saveButton = RTLHelper.CreateStyledButton(
                "💾 حفظ الإعدادات",
                new Point(startX, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                SaveButton_Click
            );

            _restoreDefaultsButton = RTLHelper.CreateStyledButton(
                "🔄 استعادة افتراضي",
                new Point(startX + buttonWidth + spacing, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.WarningColor,
                RestoreDefaultsButton_Click
            );

            _cancelButton = RTLHelper.CreateStyledButton(
                "❌ إلغاء",
                new Point(startX + (buttonWidth + spacing) * 2, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SecondaryColor,
                delegate { this.Close(); }
            );

            RTLHelper.ApplyButtonEffects(_saveButton);
            RTLHelper.ApplyButtonEffects(_restoreDefaultsButton);
            RTLHelper.ApplyButtonEffects(_cancelButton);

            _bottomPanel.Controls.Add(_saveButton);
            _bottomPanel.Controls.Add(_restoreDefaultsButton);
            _bottomPanel.Controls.Add(_cancelButton);

            this.Controls.Add(_bottomPanel);
        }
        #endregion

        #region Settings Management
        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل الإعدادات الحالية
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                _attachmentsPathTextBox.Text = Path.Combine(baseDir, "Attachments");
                _backupPathTextBox.Text = Path.Combine(baseDir, "Backups");

                // إنشاء المجلدات إذا لم تكن موجودة
                if (!Directory.Exists(_attachmentsPathTextBox.Text))
                    Directory.CreateDirectory(_attachmentsPathTextBox.Text);

                if (!Directory.Exists(_backupPathTextBox.Text))
                    Directory.CreateDirectory(_backupPathTextBox.Text);
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الإعدادات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void SaveSettings()
        {
            try
            {
                // حفظ الإعدادات (يمكن تطويرها لاحقاً لحفظها في ملف XML أو Registry)
                RTLHelper.ShowRTLMessageBox("تم حفظ الإعدادات بنجاح!", "حفظ الإعدادات",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في حفظ الإعدادات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RestoreDefaults()
        {
            try
            {
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                _attachmentsPathTextBox.Text = Path.Combine(baseDir, "Attachments");
                _backupPathTextBox.Text = Path.Combine(baseDir, "Backups");
                _autoBackupCheckBox.Checked = false;
                _backupIntervalComboBox.SelectedIndex = 0;
                _fontSizeComboBox.SelectedIndex = 1;
                _themeComboBox.SelectedIndex = 0;
                _showNotificationsCheckBox.Checked = true;
                _confirmDeleteCheckBox.Checked = true;

                RTLHelper.ShowRTLMessageBox("تم استعادة الإعدادات الافتراضية", "استعادة الإعدادات",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في استعادة الإعدادات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Event Handlers
        private void AutoBackupCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            _backupIntervalComboBox.Enabled = _autoBackupCheckBox.Checked;
        }

        private void BrowseAttachmentsButton_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "اختر مجلد حفظ المرفقات";
                folderDialog.SelectedPath = _attachmentsPathTextBox.Text;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    _attachmentsPathTextBox.Text = folderDialog.SelectedPath;
                }
            }
        }

        private void BrowseBackupButton_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "اختر مجلد النسخ الاحتياطي";
                folderDialog.SelectedPath = _backupPathTextBox.Text;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    _backupPathTextBox.Text = folderDialog.SelectedPath;
                }
            }
        }

        private void CreateBackupButton_Click(object sender, EventArgs e)
        {
            try
            {
                string backupFileName = "PIKA_Backup_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".zip";
                string backupPath = Path.Combine(_backupPathTextBox.Text, backupFileName);

                // هنا يمكن إضافة كود إنشاء النسخة الاحتياطية الفعلية
                RTLHelper.ShowRTLMessageBox("تم إنشاء النسخة الاحتياطية بنجاح!\n\nالمسار: " + backupPath, "نسخة احتياطية",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في إنشاء النسخة الاحتياطية: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            SaveSettings();
        }

        private void RestoreDefaultsButton_Click(object sender, EventArgs e)
        {
            var result = RTLHelper.ShowRTLMessageBox("هل أنت متأكد من استعادة الإعدادات الافتراضية؟", "تأكيد الاستعادة",
                              MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                RestoreDefaults();
            }
        }
        #endregion

        #region Designer Code
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(700, 500);
            this.Name = "SettingsForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
