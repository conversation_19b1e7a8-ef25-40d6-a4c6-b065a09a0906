using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// النافذة الرئيسية المحسنة مع شريط جانبي قابل للطي
    /// </summary>
    public partial class SimpleEnhancedMainForm : Form
    {
        #region Fields
        private Panel _sidebarPanel;
        private Panel _headerPanel;
        private Panel _mainContentPanel;
        private Panel _statisticsPanel;
        private Button _toggleButton;
        private bool _sidebarCollapsed = false;
        private int _expandedWidth = 280;
        private int _collapsedWidth = 50;
        private Timer _animationTimer;
        private int _targetWidth;
        private int _animationStep = 0;
        private const int ANIMATION_FRAMES = 15;
        private const int ANIMATION_INTERVAL = 20;
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;
        #endregion

        #region Constructor
        public SimpleEnhancedMainForm()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                SetupForm();
                CreateControls();
                SetApplicationIcon();
                RTLHelper.ComprehensiveRTLFix(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة النافذة: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                SetupBasicForm();
            }
        }
        #endregion

        #region Initialization
        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void SetupForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية المحسن - PIKA Enhanced";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.WindowState = FormWindowState.Maximized;
            this.MinimumSize = new Size(1200, 700);
        }

        private void SetupBasicForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }
        #endregion

        #region UI Creation
        private void CreateControls()
        {
            CreateHeader();
            CreateSidebar();
            CreateMainContent();
            SetupAnimation();
        }

        private void CreateHeader()
        {
            _headerPanel = new Panel();
            _headerPanel.Dock = DockStyle.Top;
            _headerPanel.Height = 100;
            _headerPanel.BackColor = RTLHelper.PrimaryColor;
            _headerPanel.RightToLeft = RightToLeft.Yes;

            var titleLabel = RTLHelper.CreateStyledLabel(
                "نظام الأرشفة الإلكترونية المحسن",
                new Point(50, 20),
                new Size(600, 35),
                RTLHelper.ArabicFontHeader,
                Color.White,
                ContentAlignment.MiddleRight
            );

            var subtitleLabel = RTLHelper.CreateStyledLabel(
                "إدارة شاملة للوثائق مع شريط جانبي قابل للطي وتصميم حديث",
                new Point(50, 55),
                new Size(700, 25),
                RTLHelper.ArabicFontLarge,
                Color.FromArgb(200, 255, 255, 255),
                ContentAlignment.MiddleRight
            );

            _headerPanel.Controls.Add(titleLabel);
            _headerPanel.Controls.Add(subtitleLabel);
            this.Controls.Add(_headerPanel);
        }

        private void CreateSidebar()
        {
            _sidebarPanel = new Panel();
            _sidebarPanel.Dock = DockStyle.Right;
            _sidebarPanel.Width = _expandedWidth;
            _sidebarPanel.BackColor = RTLHelper.SecondaryColor;
            _sidebarPanel.RightToLeft = RightToLeft.Yes;

            // زر الهامبرغر
            _toggleButton = new Button();
            _toggleButton.Text = "☰";
            _toggleButton.Size = new Size(24, 24);
            _toggleButton.Location = new Point(_expandedWidth - 34, 10);
            _toggleButton.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            _toggleButton.ForeColor = RTLHelper.DarkColor;
            _toggleButton.BackColor = Color.White;
            _toggleButton.FlatStyle = FlatStyle.Flat;
            _toggleButton.FlatAppearance.BorderSize = 1;
            _toggleButton.FlatAppearance.BorderColor = RTLHelper.DarkColor;
            _toggleButton.Cursor = Cursors.Hand;
            _toggleButton.Click += ToggleButton_Click;

            // عنوان الشريط الجانبي
            var sidebarTitle = RTLHelper.CreateStyledLabel(
                "القائمة الرئيسية",
                new Point(20, 45),
                new Size(240, 30),
                RTLHelper.ArabicFontLargeBold,
                Color.White,
                ContentAlignment.MiddleRight
            );
            sidebarTitle.Name = "SidebarTitle";

            // تطبيق تأثيرات الهوفر على زر الهامبرغر
            RTLHelper.ApplyButtonEffects(_toggleButton);

            _sidebarPanel.Controls.Add(_toggleButton);
            _sidebarPanel.Controls.Add(sidebarTitle);

            CreateMenuButtons();
            this.Controls.Add(_sidebarPanel);
        }

        private void CreateMenuButtons()
        {
            int buttonWidth = 230;
            int buttonHeight = 55;
            int startY = 80;
            int spacing = 70;

            var departmentsButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأقسام",
                new Point(25, startY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                delegate { OpenDepartmentsForm(); }
            );
            departmentsButton.Tag = "📁 إدارة الأقسام"; // حفظ النص الأصلي

            var documentsButton = RTLHelper.CreateStyledButton(
                "📄 إدارة الوثائق",
                new Point(25, startY + spacing),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                delegate { OpenDocumentsForm(); }
            );
            documentsButton.Tag = "📄 إدارة الوثائق"; // حفظ النص الأصلي

            var foldersButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأضبارات",
                new Point(25, startY + spacing * 2),
                new Size(buttonWidth, buttonHeight),
                Color.FromArgb(230, 126, 34),
                delegate { OpenFoldersForm(); }
            );
            foldersButton.Tag = "📁 إدارة الأضبارات"; // حفظ النص الأصلي

            var searchButton = RTLHelper.CreateStyledButton(
                "🔍 البحث المتقدم",
                new Point(25, startY + spacing * 3),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.WarningColor,
                delegate { OpenSearchForm(); }
            );
            searchButton.Tag = "🔍 البحث المتقدم"; // حفظ النص الأصلي

            var settingsButton = RTLHelper.CreateStyledButton(
                "⚙️ الإعدادات",
                new Point(25, startY + spacing * 4),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                delegate { OpenSettingsForm(); }
            );
            settingsButton.Tag = "⚙️ الإعدادات"; // حفظ النص الأصلي

            RTLHelper.ApplyButtonEffects(departmentsButton);
            RTLHelper.ApplyButtonEffects(documentsButton);
            RTLHelper.ApplyButtonEffects(foldersButton);
            RTLHelper.ApplyButtonEffects(searchButton);
            RTLHelper.ApplyButtonEffects(settingsButton);

            _sidebarPanel.Controls.Add(departmentsButton);
            _sidebarPanel.Controls.Add(documentsButton);
            _sidebarPanel.Controls.Add(foldersButton);
            _sidebarPanel.Controls.Add(searchButton);
            _sidebarPanel.Controls.Add(settingsButton);
        }

        private void CreateMainContent()
        {
            _mainContentPanel = new Panel();
            _mainContentPanel.Dock = DockStyle.Fill;
            _mainContentPanel.BackColor = RTLHelper.LightColor;
            _mainContentPanel.Padding = new Padding(30);
            _mainContentPanel.RightToLeft = RightToLeft.Yes;

            CreateWelcomeSection();
            CreateStatisticsSection();
            CreateQuickActionsSection();

            this.Controls.Add(_mainContentPanel);
        }

        private void CreateWelcomeSection()
        {
            // حساب العرض المتاح للمحتوى
            int availableWidth = _mainContentPanel.Width - 60; // 30 padding من كل جانب

            var welcomeLabel = RTLHelper.CreateStyledLabel(
                "مرحباً بك في نظام الأرشفة الإلكترونية المحسن",
                new Point(30, 30),
                new Size(availableWidth, 40),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleCenter
            );

            var descLabel = RTLHelper.CreateStyledLabel(
                "نظام متكامل ومحسن مع شريط جانبي قابل للطي وتصميم حديث متوافق مع Windows 7",
                new Point(30, 80),
                new Size(availableWidth, 35),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleCenter
            );

            _mainContentPanel.Controls.Add(welcomeLabel);
            _mainContentPanel.Controls.Add(descLabel);
        }

        private void CreateStatisticsSection()
        {
            int availableWidth = _mainContentPanel.Width - 60;

            _statisticsPanel = new Panel();
            _statisticsPanel.Location = new Point(30, 140);
            _statisticsPanel.Size = new Size(availableWidth, 180);
            _statisticsPanel.BackColor = Color.Transparent;
            _statisticsPanel.RightToLeft = RightToLeft.Yes;

            var statsTitle = RTLHelper.CreateStyledLabel(
                "إحصائيات النظام",
                new Point(0, 0),
                new Size(availableWidth, 35),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleCenter
            );

            _statisticsPanel.Controls.Add(statsTitle);
            RefreshStatistics();
            _mainContentPanel.Controls.Add(_statisticsPanel);
        }

        private void CreateQuickActionsSection()
        {
            int availableWidth = _mainContentPanel.Width - 60;

            var actionsTitle = RTLHelper.CreateStyledLabel(
                "الإجراءات السريعة",
                new Point(30, 340),
                new Size(availableWidth, 35),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleCenter
            );

            // حساب توسيط الأزرار
            int buttonWidth = 220;
            int buttonHeight = 65;
            int spacing = 30;
            int totalButtonsWidth = (buttonWidth * 3) + (spacing * 2);
            int startX = 30 + (availableWidth - totalButtonsWidth) / 2;
            int buttonY = 390;

            var addDocButton = RTLHelper.CreateStyledButton(
                "📄 إضافة وثيقة جديدة",
                new Point(startX, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                delegate { OpenAddDocumentForm(); }
            );

            var viewDocsButton = RTLHelper.CreateStyledButton(
                "📋 عرض جميع الوثائق",
                new Point(startX + buttonWidth + spacing, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                delegate { OpenDocumentsForm(); }
            );

            var manageDepsButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأقسام",
                new Point(startX + (buttonWidth + spacing) * 2, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                delegate { OpenDepartmentsForm(); }
            );

            RTLHelper.ApplyButtonEffects(addDocButton);
            RTLHelper.ApplyButtonEffects(viewDocsButton);
            RTLHelper.ApplyButtonEffects(manageDepsButton);

            _mainContentPanel.Controls.Add(actionsTitle);
            _mainContentPanel.Controls.Add(addDocButton);
            _mainContentPanel.Controls.Add(viewDocsButton);
            _mainContentPanel.Controls.Add(manageDepsButton);
        }
        #endregion

        #region Animation and Events
        private void SetupAnimation()
        {
            _animationTimer = new Timer();
            _animationTimer.Interval = ANIMATION_INTERVAL;
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        private void ToggleButton_Click(object sender, EventArgs e)
        {
            _sidebarCollapsed = !_sidebarCollapsed;
            _targetWidth = _sidebarCollapsed ? _collapsedWidth : _expandedWidth;
            _animationStep = 0;
            _animationTimer.Start();

            // إخفاء/إظهار النصوص والعناصر
            foreach (Control control in _sidebarPanel.Controls)
            {
                if (control is Button && control != _toggleButton)
                {
                    if (_sidebarCollapsed)
                    {
                        // عرض الأيقونة فقط (أول حرفين)
                        string iconText = GetIconFromText(control.Text);
                        control.Text = iconText;
                        control.Size = new Size(40, 40);
                        control.Location = new Point(5, control.Location.Y);
                    }
                    else
                    {
                        // استعادة النص الكامل والحجم
                        control.Size = new Size(230, 55);
                        control.Location = new Point(25, control.Location.Y);

                        // استعادة النص الأصلي من Tag
                        if (control.Tag != null && control.Tag is string)
                        {
                            control.Text = control.Tag.ToString();
                        }
                        else
                        {
                            // fallback للنصوص القديمة
                            control.Text = GetFullTextFromIcon(control.Text);
                        }
                    }
                }
                else if (control is Label || control.Name == "SidebarTitle")
                {
                    control.Visible = !_sidebarCollapsed;
                }
            }
        }

        private string GetIconFromText(string fullText)
        {
            if (string.IsNullOrEmpty(fullText))
                return "";

            // استخراج الأيقونة (أول حرفين عادة)
            if (fullText.Length >= 2)
                return fullText.Substring(0, 2);
            else
                return fullText;
        }

        private string GetFullTextFromIcon(string iconText)
        {
            // استعادة النص الكامل بناءً على الأيقونة (fallback)
            if (iconText.StartsWith("📁"))
                return "📁 إدارة الأقسام"; // افتراضي
            else if (iconText.StartsWith("📄"))
                return "📄 إدارة الوثائق";
            else if (iconText.StartsWith("🔍"))
                return "🔍 البحث المتقدم";
            else if (iconText.StartsWith("⚙️"))
                return "⚙️ الإعدادات";
            else
                return iconText;
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            _animationStep++;

            if (_animationStep >= ANIMATION_FRAMES)
            {
                _sidebarPanel.Width = _targetWidth;
                _animationTimer.Stop();

                // تحديث موقع زر الهامبرغر بعد انتهاء الانيميشن
                _toggleButton.Location = new Point(_targetWidth - 34, 10);
            }
            else
            {
                // حساب العرض الحالي باستخدام interpolation سلس
                int startWidth = _sidebarCollapsed ? _expandedWidth : _collapsedWidth;
                int widthDifference = _targetWidth - startWidth;
                double progress = (double)_animationStep / ANIMATION_FRAMES;

                // استخدام ease-out للحصول على انيميشن أكثر سلاسة
                progress = 1 - Math.Pow(1 - progress, 3);

                int newWidth = startWidth + (int)(widthDifference * progress);
                _sidebarPanel.Width = newWidth;

                // تحديث موقع زر الهامبرغر أثناء الانيميشن
                _toggleButton.Location = new Point(newWidth - 34, 10);
            }
        }
        #endregion

        #region Statistics
        private void RefreshStatistics()
        {
            try
            {
                // مسح البطاقات الموجودة
                var existingCards = new Control[_statisticsPanel.Controls.Count];
                _statisticsPanel.Controls.CopyTo(existingCards, 0);

                foreach (Control control in existingCards)
                {
                    if (control.Tag != null && control.Tag.ToString() == "StatCard")
                    {
                        _statisticsPanel.Controls.Remove(control);
                        control.Dispose();
                    }
                }

                var stats = GetRealStatistics();

                var statisticsData = new object[]
                {
                    new { Title = "عدد الأقسام", Value = stats.DepartmentsCount.ToString(), Icon = "📁", Color = RTLHelper.AccentColor },
                    new { Title = "عدد الأضابير", Value = stats.FoldersCount.ToString(), Icon = "📂", Color = RTLHelper.SuccessColor },
                    new { Title = "الكتب الصادرة", Value = stats.OutgoingDocuments.ToString(), Icon = "📤", Color = RTLHelper.DangerColor },
                    new { Title = "الكتب الواردة", Value = stats.IncomingDocuments.ToString(), Icon = "📥", Color = RTLHelper.InfoColor }
                };

                int cardWidth = 200;
                int cardHeight = 100;
                int spacing = 25;
                int totalCardsWidth = (cardWidth * statisticsData.Length) + (spacing * (statisticsData.Length - 1));
                int startX = (_statisticsPanel.Width - totalCardsWidth) / 2;
                int startY = 50;

                for (int i = 0; i < statisticsData.Length; i++)
                {
                    var stat = statisticsData[i];
                    var card = CreateStatCard(
                        GetProperty(stat, "Title").ToString(),
                        GetProperty(stat, "Value").ToString(),
                        GetProperty(stat, "Icon").ToString(),
                        (Color)GetProperty(stat, "Color"),
                        new Size(cardWidth, cardHeight)
                    );

                    card.Location = new Point(startX + (i * (cardWidth + spacing)), startY);
                    card.Tag = "StatCard";
                    _statisticsPanel.Controls.Add(card);
                }
            }
            catch
            {
                // في حالة الخطأ، عرض إحصائيات افتراضية
                ShowDefaultStatistics();
            }
        }

        private void ShowDefaultStatistics()
        {
            var defaultStats = new object[]
            {
                new { Title = "عدد الأقسام", Value = "0", Icon = "📁", Color = RTLHelper.AccentColor },
                new { Title = "عدد الأضابير", Value = "0", Icon = "📂", Color = RTLHelper.SuccessColor },
                new { Title = "الكتب الصادرة", Value = "0", Icon = "📤", Color = RTLHelper.DangerColor },
                new { Title = "الكتب الواردة", Value = "0", Icon = "📥", Color = RTLHelper.InfoColor }
            };

            int cardWidth = 200;
            int cardHeight = 100;
            int spacing = 25;
            int totalCardsWidth = (cardWidth * defaultStats.Length) + (spacing * (defaultStats.Length - 1));
            int startX = (_statisticsPanel.Width - totalCardsWidth) / 2;
            int startY = 50;

            for (int i = 0; i < defaultStats.Length; i++)
            {
                var stat = defaultStats[i];
                var card = CreateStatCard(
                    GetProperty(stat, "Title").ToString(),
                    GetProperty(stat, "Value").ToString(),
                    GetProperty(stat, "Icon").ToString(),
                    (Color)GetProperty(stat, "Color"),
                    new Size(cardWidth, cardHeight)
                );

                card.Location = new Point(startX + (i * (cardWidth + spacing)), startY);
                card.Tag = "StatCard";
                _statisticsPanel.Controls.Add(card);
            }
        }

        private object GetProperty(object obj, string propertyName)
        {
            return obj.GetType().GetProperty(propertyName).GetValue(obj, null);
        }

        private Panel CreateStatCard(string title, string value, string icon, Color color, Size size)
        {
            var card = new Panel();
            card.Size = size;
            card.BackColor = Color.White;
            card.BorderStyle = BorderStyle.None;
            card.RightToLeft = RightToLeft.Yes;

            // إضافة تأثير الظل والحدود المدورة
            card.Paint += (s, e) =>
            {
                var rect = card.ClientRectangle;

                // رسم ظل خفيف
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, new Rectangle(rect.X + 3, rect.Y + 3, rect.Width, rect.Height));
                }

                // رسم الخلفية البيضاء
                using (var backgroundBrush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillRectangle(backgroundBrush, rect);
                }

                // رسم خط علوي ملون
                using (var accentBrush = new SolidBrush(color))
                {
                    e.Graphics.FillRectangle(accentBrush, new Rectangle(rect.X, rect.Y, rect.Width, 5));
                }

                // رسم حدود خفيفة
                using (var borderPen = new Pen(Color.FromArgb(230, 230, 230), 1))
                {
                    e.Graphics.DrawRectangle(borderPen, new Rectangle(rect.X, rect.Y, rect.Width - 1, rect.Height - 1));
                }
            };

            var iconLabel = RTLHelper.CreateStyledLabel(
                icon,
                new Point(size.Width - 60, 15),
                new Size(50, 35),
                new Font("Segoe UI Emoji", 20F),
                color,
                ContentAlignment.MiddleCenter
            );

            var valueLabel = RTLHelper.CreateStyledLabel(
                value,
                new Point(size.Width - 140, 15),
                new Size(70, 30),
                RTLHelper.ArabicFontHeader,
                color,
                ContentAlignment.MiddleRight
            );

            var titleLabel = RTLHelper.CreateStyledLabel(
                title,
                new Point(10, size.Height - 30),
                new Size(size.Width - 20, 25),
                RTLHelper.ArabicFontBold,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            card.Controls.Add(iconLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(titleLabel);

            // تأثير hover
            card.MouseEnter += (s, e) =>
            {
                card.BackColor = Color.FromArgb(248, 249, 250);
                card.Invalidate();
            };

            card.MouseLeave += (s, e) =>
            {
                card.BackColor = Color.White;
                card.Invalidate();
            };

            return card;
        }

        private StatisticsData GetRealStatistics()
        {
            try
            {
                int departmentsCount = _departmentRepo.GetTotalCount();
                int foldersCount = _dataManager.GetFoldersCount();
                int outgoingDocs = _dataManager.GetDocumentsCount("صادر");
                int incomingDocs = _dataManager.GetDocumentsCount("وارد");

                return new StatisticsData
                {
                    DepartmentsCount = departmentsCount,
                    FoldersCount = foldersCount,
                    OutgoingDocuments = outgoingDocs,
                    IncomingDocuments = incomingDocs
                };
            }
            catch
            {
                return new StatisticsData { DepartmentsCount = 0, FoldersCount = 0, OutgoingDocuments = 0, IncomingDocuments = 0 };
            }
        }
        #endregion

        #region Navigation
        private void OpenAddDocumentForm()
        {
            try
            {
                // التحقق من وجود أقسام قبل إضافة وثيقة
                if (_departmentRepo.GetTotalCount() == 0)
                {
                    var result = RTLHelper.ShowRTLMessageBox(
                        "لا توجد أقسام في النظام. هل تريد إضافة قسم جديد أولاً؟",
                        "تنبيه",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question
                    );

                    if (result == DialogResult.Yes)
                    {
                        OpenDepartmentsForm();
                        return;
                    }
                    else
                    {
                        return;
                    }
                }

                // فتح نافذة إضافة الوثائق
                var addDocumentForm = new SimpleAddDocumentForm();
                if (addDocumentForm.ShowDialog() == DialogResult.OK)
                {
                    RefreshStatistics();
                    RTLHelper.ShowRTLMessageBox("تم إضافة الوثيقة بنجاح!", "نجح الحفظ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة إضافة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenDepartmentsForm()
        {
            try
            {
                var departmentsForm = new SimpleDepartmentsForm();
                departmentsForm.ShowDialog();
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenDocumentsForm()
        {
            try
            {
                var documentsForm = new SimpleDocumentsForm();
                documentsForm.ShowDialog();
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenFoldersForm()
        {
            try
            {
                var foldersForm = new FolderManagementForm();
                foldersForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة إدارة الأضبارات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSearchForm()
        {
            try
            {
                var searchForm = new AdvancedSearchForm();
                searchForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة البحث المتقدم: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSettingsForm()
        {
            try
            {
                var settingsForm = new SettingsForm();
                settingsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الإعدادات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Utility Methods
        private void SetApplicationIcon()
        {
            try
            {
                if (File.Exists("AppIcon.ico"))
                {
                    this.Icon = new Icon("AppIcon.ico");
                }
                else
                {
                    IconManager.SetFormIcon(this, "MainApp.ico");
                }
            }
            catch
            {
                // تجاهل أخطاء الأيقونة
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);

            // تحديث تخطيط المحتوى عند تغيير حجم النافذة
            if (_mainContentPanel != null && _statisticsPanel != null)
            {
                UpdateLayout();
            }
        }

        private void UpdateLayout()
        {
            try
            {
                // تحديث أحجام اللوحات بناءً على حالة الشريط الجانبي
                int sidebarWidth = _sidebarCollapsed ? _collapsedWidth : _expandedWidth;
                int availableWidth = this.Width - sidebarWidth - 60;

                if (_mainContentPanel != null)
                {
                    _mainContentPanel.Width = this.Width - sidebarWidth;
                }

                if (_statisticsPanel != null)
                {
                    _statisticsPanel.Size = new Size(availableWidth, 180);
                }

                // إعادة توسيط العناصر
                RefreshStatistics();
                UpdateQuickActionsLayout();
                UpdateWelcomeLayout();
            }
            catch
            {
                // تجاهل أخطاء التخطيط
            }
        }

        private void UpdateQuickActionsLayout()
        {
            try
            {
                int availableWidth = _mainContentPanel.Width - 60;
                int buttonWidth = 220;
                int spacing = 30;
                int totalButtonsWidth = (buttonWidth * 3) + (spacing * 2);
                int startX = 30 + (availableWidth - totalButtonsWidth) / 2;

                // البحث عن الأزرار وتحديث مواقعها
                foreach (Control control in _mainContentPanel.Controls)
                {
                    if (control is Button)
                    {
                        Button button = (Button)control;
                        if (button.Text.Contains("إضافة وثيقة"))
                        {
                            button.Location = new Point(startX, button.Location.Y);
                        }
                        else if (button.Text.Contains("عرض جميع"))
                        {
                            button.Location = new Point(startX + buttonWidth + spacing, button.Location.Y);
                        }
                        else if (button.Text.Contains("إدارة الأقسام"))
                        {
                            button.Location = new Point(startX + (buttonWidth + spacing) * 2, button.Location.Y);
                        }
                    }
                }

                // تحديث عنوان الإجراءات السريعة
                foreach (Control control in _mainContentPanel.Controls)
                {
                    if (control is Label)
                    {
                        Label label = (Label)control;
                        if (label.Text == "الإجراءات السريعة")
                        {
                            label.Size = new Size(availableWidth, 35);
                        }
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء التخطيط
            }
        }

        private void UpdateWelcomeLayout()
        {
            try
            {
                int availableWidth = _mainContentPanel.Width - 60;

                foreach (Control control in _mainContentPanel.Controls)
                {
                    if (control is Label)
                    {
                        Label label = (Label)control;
                        if (label.Text.Contains("مرحباً بك"))
                        {
                            label.Size = new Size(availableWidth, 40);
                        }
                        else if (label.Text.Contains("نظام متكامل"))
                        {
                            label.Size = new Size(availableWidth, 35);
                        }
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء التخطيط
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 900);
            this.Name = "SimpleEnhancedMainForm";
            this.ResumeLayout(false);
        }
        #endregion
    }

    public class StatisticsData
    {
        public int DepartmentsCount { get; set; }
        public int FoldersCount { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
    }
}
