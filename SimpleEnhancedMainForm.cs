using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// النافذة الرئيسية المحسنة مع شريط جانبي قابل للطي
    /// </summary>
    public partial class SimpleEnhancedMainForm : Form
    {
        #region Fields
        private Panel _sidebarPanel;
        private Panel _headerPanel;
        private Panel _mainContentPanel;
        private Panel _statisticsPanel;
        private Button _toggleButton;
        private bool _sidebarCollapsed = false;
        private int _expandedWidth = 280;
        private int _collapsedWidth = 60;
        private Timer _animationTimer;
        private int _targetWidth;
        private SimpleDataManager _dataManager;
        private SimpleDepartmentRepository _departmentRepo;
        #endregion

        #region Constructor
        public SimpleEnhancedMainForm()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                SetupForm();
                CreateControls();
                SetApplicationIcon();
                RTLHelper.ComprehensiveRTLFix(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة النافذة: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                SetupBasicForm();
            }
        }
        #endregion

        #region Initialization
        private void InitializeData()
        {
            try
            {
                _dataManager = SimpleDataManager.Instance;
                _departmentRepo = new SimpleDepartmentRepository();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تهيئة البيانات: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void SetupForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية المحسن - PIKA Enhanced";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.WindowState = FormWindowState.Maximized;
            this.MinimumSize = new Size(1200, 700);
        }

        private void SetupBasicForm()
        {
            this.Text = "نظام الأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }
        #endregion

        #region UI Creation
        private void CreateControls()
        {
            CreateHeader();
            CreateSidebar();
            CreateMainContent();
            SetupAnimation();
        }

        private void CreateHeader()
        {
            _headerPanel = new Panel();
            _headerPanel.Dock = DockStyle.Top;
            _headerPanel.Height = 100;
            _headerPanel.BackColor = RTLHelper.PrimaryColor;
            _headerPanel.RightToLeft = RightToLeft.Yes;

            var titleLabel = RTLHelper.CreateStyledLabel(
                "نظام الأرشفة الإلكترونية المحسن",
                new Point(50, 20),
                new Size(600, 35),
                RTLHelper.ArabicFontHeader,
                Color.White,
                ContentAlignment.MiddleRight
            );

            var subtitleLabel = RTLHelper.CreateStyledLabel(
                "إدارة شاملة للوثائق مع شريط جانبي قابل للطي وتصميم حديث",
                new Point(50, 55),
                new Size(700, 25),
                RTLHelper.ArabicFontLarge,
                Color.FromArgb(200, 255, 255, 255),
                ContentAlignment.MiddleRight
            );

            _headerPanel.Controls.Add(titleLabel);
            _headerPanel.Controls.Add(subtitleLabel);
            this.Controls.Add(_headerPanel);
        }

        private void CreateSidebar()
        {
            _sidebarPanel = new Panel();
            _sidebarPanel.Dock = DockStyle.Right;
            _sidebarPanel.Width = _expandedWidth;
            _sidebarPanel.BackColor = RTLHelper.SecondaryColor;
            _sidebarPanel.RightToLeft = RightToLeft.Yes;

            // زر التبديل
            _toggleButton = new Button();
            _toggleButton.Text = "◀";
            _toggleButton.Size = new Size(40, 40);
            _toggleButton.Location = new Point(10, 20);
            _toggleButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            _toggleButton.ForeColor = Color.White;
            _toggleButton.BackColor = Color.Transparent;
            _toggleButton.FlatStyle = FlatStyle.Flat;
            _toggleButton.FlatAppearance.BorderSize = 0;
            _toggleButton.Cursor = Cursors.Hand;
            _toggleButton.Click += ToggleButton_Click;

            // عنوان الشريط الجانبي
            var sidebarTitle = RTLHelper.CreateStyledLabel(
                "القائمة الرئيسية",
                new Point(60, 25),
                new Size(200, 30),
                RTLHelper.ArabicFontLargeBold,
                Color.White,
                ContentAlignment.MiddleRight
            );

            _sidebarPanel.Controls.Add(_toggleButton);
            _sidebarPanel.Controls.Add(sidebarTitle);

            CreateMenuButtons();
            this.Controls.Add(_sidebarPanel);
        }

        private void CreateMenuButtons()
        {
            int buttonWidth = 230;
            int buttonHeight = 55;
            int startY = 80;
            int spacing = 70;

            var departmentsButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأقسام",
                new Point(25, startY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                delegate { OpenDepartmentsForm(); }
            );

            var documentsButton = RTLHelper.CreateStyledButton(
                "📄 إدارة الوثائق",
                new Point(25, startY + spacing),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                delegate { OpenDocumentsForm(); }
            );

            var searchButton = RTLHelper.CreateStyledButton(
                "🔍 البحث المتقدم",
                new Point(25, startY + spacing * 2),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.WarningColor,
                delegate { OpenSearchForm(); }
            );

            var settingsButton = RTLHelper.CreateStyledButton(
                "⚙️ الإعدادات",
                new Point(25, startY + spacing * 3),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                delegate { OpenSettingsForm(); }
            );

            RTLHelper.ApplyButtonEffects(departmentsButton);
            RTLHelper.ApplyButtonEffects(documentsButton);
            RTLHelper.ApplyButtonEffects(searchButton);
            RTLHelper.ApplyButtonEffects(settingsButton);

            _sidebarPanel.Controls.Add(departmentsButton);
            _sidebarPanel.Controls.Add(documentsButton);
            _sidebarPanel.Controls.Add(searchButton);
            _sidebarPanel.Controls.Add(settingsButton);
        }

        private void CreateMainContent()
        {
            _mainContentPanel = new Panel();
            _mainContentPanel.Dock = DockStyle.Fill;
            _mainContentPanel.BackColor = RTLHelper.LightColor;
            _mainContentPanel.Padding = new Padding(30);
            _mainContentPanel.RightToLeft = RightToLeft.Yes;

            CreateWelcomeSection();
            CreateStatisticsSection();
            CreateQuickActionsSection();

            this.Controls.Add(_mainContentPanel);
        }

        private void CreateWelcomeSection()
        {
            var welcomeLabel = RTLHelper.CreateStyledLabel(
                "مرحباً بك في نظام الأرشفة الإلكترونية المحسن",
                new Point(30, 30),
                new Size(600, 40),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            var descLabel = RTLHelper.CreateStyledLabel(
                "نظام متكامل ومحسن مع شريط جانبي قابل للطي وتصميم حديث متوافق مع Windows 7",
                new Point(30, 75),
                new Size(700, 30),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _mainContentPanel.Controls.Add(welcomeLabel);
            _mainContentPanel.Controls.Add(descLabel);
        }

        private void CreateStatisticsSection()
        {
            _statisticsPanel = new Panel();
            _statisticsPanel.Location = new Point(30, 130);
            _statisticsPanel.Size = new Size(800, 150);
            _statisticsPanel.BackColor = Color.Transparent;
            _statisticsPanel.RightToLeft = RightToLeft.Yes;

            var statsTitle = RTLHelper.CreateStyledLabel(
                "إحصائيات النظام",
                new Point(0, 0),
                new Size(200, 30),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            _statisticsPanel.Controls.Add(statsTitle);
            RefreshStatistics();
            _mainContentPanel.Controls.Add(_statisticsPanel);
        }

        private void CreateQuickActionsSection()
        {
            var actionsTitle = RTLHelper.CreateStyledLabel(
                "الإجراءات السريعة",
                new Point(30, 300),
                new Size(200, 30),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            var addDocButton = RTLHelper.CreateStyledButton(
                "📄 إضافة وثيقة جديدة",
                new Point(30, 340),
                new Size(250, 60),
                RTLHelper.SuccessColor,
                delegate { OpenDocumentsForm(); }
            );

            var viewDocsButton = RTLHelper.CreateStyledButton(
                "📋 عرض جميع الوثائق",
                new Point(300, 340),
                new Size(250, 60),
                RTLHelper.AccentColor,
                delegate { OpenDocumentsForm(); }
            );

            var manageDepsButton = RTLHelper.CreateStyledButton(
                "📁 إدارة الأقسام",
                new Point(570, 340),
                new Size(250, 60),
                RTLHelper.InfoColor,
                delegate { OpenDepartmentsForm(); }
            );

            RTLHelper.ApplyButtonEffects(addDocButton);
            RTLHelper.ApplyButtonEffects(viewDocsButton);
            RTLHelper.ApplyButtonEffects(manageDepsButton);

            _mainContentPanel.Controls.Add(actionsTitle);
            _mainContentPanel.Controls.Add(addDocButton);
            _mainContentPanel.Controls.Add(viewDocsButton);
            _mainContentPanel.Controls.Add(manageDepsButton);
        }
        #endregion

        #region Animation and Events
        private void SetupAnimation()
        {
            _animationTimer = new Timer();
            _animationTimer.Interval = 20;
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        private void ToggleButton_Click(object sender, EventArgs e)
        {
            _sidebarCollapsed = !_sidebarCollapsed;
            _targetWidth = _sidebarCollapsed ? _collapsedWidth : _expandedWidth;
            _toggleButton.Text = _sidebarCollapsed ? "▶" : "◀";
            _animationTimer.Start();

            // إخفاء/إظهار النصوص
            foreach (Control control in _sidebarPanel.Controls)
            {
                if (control is Button && control != _toggleButton)
                {
                    if (_sidebarCollapsed)
                    {
                        control.Text = control.Text.Substring(0, 2); // الأيقونة فقط
                    }
                    else
                    {
                        // استعادة النص الكامل (يمكن تحسينه)
                        if (control.Text.StartsWith("📁"))
                            control.Text = "📁 إدارة الأقسام";
                        else if (control.Text.StartsWith("📄"))
                            control.Text = "📄 إدارة الوثائق";
                        else if (control.Text.StartsWith("🔍"))
                            control.Text = "🔍 البحث المتقدم";
                        else if (control.Text.StartsWith("⚙️"))
                            control.Text = "⚙️ الإعدادات";
                    }
                }
            }
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            int currentWidth = _sidebarPanel.Width;
            int difference = _targetWidth - currentWidth;

            if (Math.Abs(difference) <= 10)
            {
                _sidebarPanel.Width = _targetWidth;
                _animationTimer.Stop();
            }
            else
            {
                int step = difference > 0 ? 10 : -10;
                _sidebarPanel.Width = currentWidth + step;
            }
        }
        #endregion

        #region Statistics
        private void RefreshStatistics()
        {
            try
            {
                var stats = GetRealStatistics();

                var statisticsData = new object[]
                {
                    new { Title = "عدد الأقسام", Value = stats.DepartmentsCount.ToString(), Icon = "📁", Color = RTLHelper.AccentColor },
                    new { Title = "عدد الأضابير", Value = stats.FoldersCount.ToString(), Icon = "📂", Color = RTLHelper.SuccessColor },
                    new { Title = "الكتب الصادرة", Value = stats.OutgoingDocuments.ToString(), Icon = "📤", Color = RTLHelper.DangerColor },
                    new { Title = "الكتب الواردة", Value = stats.IncomingDocuments.ToString(), Icon = "📥", Color = RTLHelper.InfoColor }
                };

                int cardWidth = 180;
                int cardHeight = 80;
                int startX = 0;
                int startY = 40;
                int spacing = 200;

                for (int i = 0; i < statisticsData.Length; i++)
                {
                    var stat = statisticsData[i];
                    var card = CreateStatCard(
                        GetProperty(stat, "Title").ToString(),
                        GetProperty(stat, "Value").ToString(),
                        GetProperty(stat, "Icon").ToString(),
                        (Color)GetProperty(stat, "Color"),
                        new Size(cardWidth, cardHeight)
                    );

                    card.Location = new Point(startX + (i * spacing), startY);
                    _statisticsPanel.Controls.Add(card);
                }
            }
            catch
            {
                // في حالة الخطأ، عرض إحصائيات افتراضية
            }
        }

        private object GetProperty(object obj, string propertyName)
        {
            return obj.GetType().GetProperty(propertyName).GetValue(obj, null);
        }

        private Panel CreateStatCard(string title, string value, string icon, Color color, Size size)
        {
            var card = new Panel();
            card.Size = size;
            card.BackColor = Color.White;
            card.BorderStyle = BorderStyle.FixedSingle;

            var iconLabel = RTLHelper.CreateStyledLabel(
                icon,
                new Point(size.Width - 50, 10),
                new Size(40, 30),
                new Font("Segoe UI Emoji", 16F),
                color,
                ContentAlignment.MiddleCenter
            );

            var valueLabel = RTLHelper.CreateStyledLabel(
                value,
                new Point(size.Width - 120, 10),
                new Size(60, 25),
                RTLHelper.ArabicFontLargeBold,
                color,
                ContentAlignment.MiddleRight
            );

            var titleLabel = RTLHelper.CreateStyledLabel(
                title,
                new Point(10, size.Height - 25),
                new Size(size.Width - 20, 20),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            card.Controls.Add(iconLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(titleLabel);

            return card;
        }

        private StatisticsData GetRealStatistics()
        {
            try
            {
                int departmentsCount = _departmentRepo.GetTotalCount();
                int foldersCount = _dataManager.GetFoldersCount();
                int outgoingDocs = _dataManager.GetDocumentsCount("صادر");
                int incomingDocs = _dataManager.GetDocumentsCount("وارد");

                return new StatisticsData
                {
                    DepartmentsCount = departmentsCount,
                    FoldersCount = foldersCount,
                    OutgoingDocuments = outgoingDocs,
                    IncomingDocuments = incomingDocs
                };
            }
            catch
            {
                return new StatisticsData { DepartmentsCount = 0, FoldersCount = 0, OutgoingDocuments = 0, IncomingDocuments = 0 };
            }
        }
        #endregion

        #region Navigation
        private void OpenDepartmentsForm()
        {
            try
            {
                var departmentsForm = new SimpleDepartmentsForm();
                departmentsForm.ShowDialog();
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenDocumentsForm()
        {
            try
            {
                var documentsForm = new SimpleDocumentsForm();
                documentsForm.ShowDialog();
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح نافذة الوثائق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSearchForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة البحث المتقدم قريباً\nهذه الميزة قيد التطوير", "البحث المتقدم",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OpenSettingsForm()
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح نافذة الإعدادات قريباً\nهذه الميزة قيد التطوير", "إعدادات النظام",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        #endregion

        #region Utility Methods
        private void SetApplicationIcon()
        {
            try
            {
                if (File.Exists("AppIcon.ico"))
                {
                    this.Icon = new Icon("AppIcon.ico");
                }
                else
                {
                    IconManager.SetFormIcon(this, "MainApp.ico");
                }
            }
            catch
            {
                // تجاهل أخطاء الأيقونة
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 900);
            this.Name = "SimpleEnhancedMainForm";
            this.ResumeLayout(false);
        }
        #endregion
    }

    public class StatisticsData
    {
        public int DepartmentsCount { get; set; }
        public int FoldersCount { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
    }
}
