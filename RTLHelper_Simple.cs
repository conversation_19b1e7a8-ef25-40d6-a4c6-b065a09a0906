using System;
using System.Drawing;
using System.Windows.Forms;

namespace ArchiveSystem.Helpers
{
    /// <summary>
    /// مساعد RTL مبسط للواجهة العربية
    /// </summary>
    public static class RTLHelper
    {
        // الخطوط العربية
        public static readonly Font ArabicFont = new Font("Tahoma", 10F);
        public static readonly Font ArabicFontBold = new Font("Tahoma", 10F, FontStyle.Bold);
        public static readonly Font ArabicFontLarge = new Font("Tahoma", 12F);
        public static readonly Font ArabicFontLargeBold = new Font("Tahoma", 12F, FontStyle.Bold);
        public static readonly Font ArabicFontHeader = new Font("Tahoma", 16F, FontStyle.Bold);
        public static readonly Font ArabicFontTitle = new Font("Tahoma", 18F, FontStyle.Bold);

        // الألوان الأساسية
        public static readonly Color PrimaryColor = Color.FromArgb(52, 73, 94);
        public static readonly Color SecondaryColor = Color.FromArgb(44, 62, 80);
        public static readonly Color AccentColor = Color.FromArgb(52, 152, 219);
        public static readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        public static readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        public static readonly Color WarningColor = Color.FromArgb(243, 156, 18);
        public static readonly Color InfoColor = Color.FromArgb(155, 89, 182);
        public static readonly Color LightColor = Color.FromArgb(236, 240, 241);
        public static readonly Color DarkColor = Color.FromArgb(127, 140, 141);

        /// <summary>
        /// تطبيق إعدادات RTL على النموذج
        /// </summary>
        public static void ApplyRTL(Form form)
        {
            if (form == null) return;

            form.RightToLeft = RightToLeft.Yes;
            form.RightToLeftLayout = true;
            form.Font = ArabicFont;

            ApplyRTLToControls(form.Controls);
        }

        /// <summary>
        /// تطبيق إعدادات RTL على مجموعة من العناصر
        /// </summary>
        private static void ApplyRTLToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                control.RightToLeft = RightToLeft.Yes;
                
                if (control.HasChildren)
                {
                    ApplyRTLToControls(control.Controls);
                }
            }
        }

        /// <summary>
        /// إنشاء لوحة مصممة
        /// </summary>
        public static Panel CreateStyledPanel(Point location, Size size, Color backgroundColor, BorderStyle borderStyle)
        {
            var panel = new Panel();
            panel.Location = location;
            panel.Size = size;
            panel.BackColor = backgroundColor;
            panel.BorderStyle = borderStyle;
            panel.RightToLeft = RightToLeft.Yes;
            return panel;
        }

        /// <summary>
        /// إنشاء تسمية مصممة
        /// </summary>
        public static Label CreateStyledLabel(string text, Point location, Size size, Font font, Color foreColor, ContentAlignment textAlign)
        {
            var label = new Label();
            label.Text = text;
            label.Location = location;
            label.Size = size;
            label.Font = font;
            label.ForeColor = foreColor;
            label.TextAlign = textAlign;
            label.RightToLeft = RightToLeft.Yes;
            return label;
        }

        /// <summary>
        /// إنشاء زر مصمم
        /// </summary>
        public static Button CreateStyledButton(string text, Point location, Size size, Color backgroundColor, EventHandler clickHandler)
        {
            var button = new Button();
            button.Text = text;
            button.Location = location;
            button.Size = size;
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.Font = ArabicFontBold;
            button.TextAlign = ContentAlignment.MiddleCenter;
            button.Cursor = Cursors.Hand;
            button.RightToLeft = RightToLeft.Yes;
            button.FlatAppearance.BorderSize = 0;
            
            if (clickHandler != null)
                button.Click += clickHandler;
            
            return button;
        }

        /// <summary>
        /// تطبيق تأثيرات بصرية على الزر
        /// </summary>
        public static void ApplyButtonEffects(Button button)
        {
            if (button == null) return;

            var originalColor = button.BackColor;
            
            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(originalColor, 0.2f);
            };
            
            button.MouseLeave += (s, e) => {
                button.BackColor = originalColor;
            };
        }

        /// <summary>
        /// عرض رسالة مع دعم RTL
        /// </summary>
        public static DialogResult ShowRTLMessageBox(string text, string caption, MessageBoxButtons buttons, MessageBoxIcon icon)
        {
            return MessageBox.Show(text, caption, buttons, icon,
                                 MessageBoxDefaultButton.Button1,
                                 MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        /// <summary>
        /// إنشاء ListView مع دعم RTL
        /// </summary>
        public static ListView CreateStyledListView(Point location, Size size, Color backgroundColor, Font font)
        {
            var listView = new ListView();
            listView.Location = location;
            listView.Size = size;
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = true;
            listView.Font = font ?? ArabicFont;
            listView.BackColor = backgroundColor;
            listView.RightToLeft = RightToLeft.Yes;
            listView.RightToLeftLayout = true;
            return listView;
        }

        /// <summary>
        /// إنشاء TextBox مع دعم RTL
        /// </summary>
        public static TextBox CreateStyledTextBox(Point location, Size size, Font font)
        {
            var textBox = new TextBox();
            textBox.Location = location;
            textBox.Size = size;
            textBox.Font = font ?? ArabicFont;
            textBox.RightToLeft = RightToLeft.Yes;
            textBox.TextAlign = HorizontalAlignment.Right;
            return textBox;
        }

        /// <summary>
        /// إنشاء ComboBox مع دعم RTL
        /// </summary>
        public static ComboBox CreateStyledComboBox(Point location, Size size)
        {
            var comboBox = new ComboBox();
            comboBox.Location = location;
            comboBox.Size = size;
            comboBox.Font = ArabicFont;
            comboBox.RightToLeft = RightToLeft.Yes;
            comboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            return comboBox;
        }

        /// <summary>
        /// إصلاح شامل لـ RTL (نسخة مبسطة)
        /// </summary>
        public static void ComprehensiveRTLFix(Form form)
        {
            ApplyRTL(form);
        }

        /// <summary>
        /// التحقق من تطبيق RTL (نسخة مبسطة)
        /// </summary>
        public static bool ValidateRTLImplementation(Form form)
        {
            return form.RightToLeft == RightToLeft.Yes && form.RightToLeftLayout;
        }

        /// <summary>
        /// إنشاء تقرير RTL (نسخة مبسطة)
        /// </summary>
        public static string GenerateRTLReport(Form form)
        {
            return "تم تطبيق إعدادات RTL بنجاح على النموذج";
        }
    }
}
