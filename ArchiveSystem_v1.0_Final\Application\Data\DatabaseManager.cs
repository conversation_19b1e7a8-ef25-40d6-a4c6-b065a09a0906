using System;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Windows.Forms;

namespace ArchiveSystem.Data
{
    /// <summary>
    /// مدير قاعدة البيانات المتقدم - SQLite
    /// </summary>
    public class DatabaseManager
    {
        private static DatabaseManager _instance;
        private static readonly object _lock = new object();
        private string _connectionString;
        private string _databasePath;

        public static DatabaseManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DatabaseManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private DatabaseManager()
        {
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            try
            {
                string appPath = Application.StartupPath;
                string dataFolder = Path.Combine(appPath, "Database");
                
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                _databasePath = Path.Combine(dataFolder, "ArchiveSystem.db");
                _connectionString = $"Data Source={_databasePath};Version=3;";

                if (!File.Exists(_databasePath))
                {
                    CreateDatabase();
                }
                else
                {
                    // التحقق من إصدار قاعدة البيانات وتحديثها إذا لزم الأمر
                    CheckAndUpgradeDatabase();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            }
        }

        private void CreateDatabase()
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                connection.Open();
                
                // إنشاء الجداول
                CreateTables(connection);
                
                // إدراج البيانات الأولية
                InsertInitialData(connection);
                
                // إنشاء الفهارس
                CreateIndexes(connection);
            }
        }

        private void CreateTables(SQLiteConnection connection)
        {
            string[] createTableQueries = {
                // جدول الأقسام
                @"CREATE TABLE IF NOT EXISTS Departments (
                    DepartmentId INTEGER PRIMARY KEY AUTOINCREMENT,
                    DepartmentName NVARCHAR(100) NOT NULL UNIQUE,
                    Description NVARCHAR(500),
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    IsActive BOOLEAN NOT NULL DEFAULT 1
                )",

                // جدول الأضابير
                @"CREATE TABLE IF NOT EXISTS Folders (
                    FolderId INTEGER PRIMARY KEY AUTOINCREMENT,
                    FolderName NVARCHAR(100) NOT NULL,
                    Description NVARCHAR(500),
                    DepartmentId INTEGER NOT NULL,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    FOREIGN KEY (DepartmentId) REFERENCES Departments(DepartmentId),
                    UNIQUE(FolderName, DepartmentId)
                )",

                // جدول الوثائق
                @"CREATE TABLE IF NOT EXISTS Documents (
                    DocumentId INTEGER PRIMARY KEY AUTOINCREMENT,
                    DocumentNumber NVARCHAR(50) NOT NULL,
                    Subject NVARCHAR(200) NOT NULL,
                    DocumentType NVARCHAR(20) NOT NULL CHECK (DocumentType IN ('صادر', 'وارد')),
                    DepartmentId INTEGER NOT NULL,
                    FolderId INTEGER,
                    DocumentDate DATE NOT NULL,
                    ReceivedDate DATE,
                    SenderReceiver NVARCHAR(200),
                    Notes NVARCHAR(1000),
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy NVARCHAR(100),
                    ModifiedDate DATETIME,
                    ModifiedBy NVARCHAR(100),
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    FOREIGN KEY (DepartmentId) REFERENCES Departments(DepartmentId),
                    FOREIGN KEY (FolderId) REFERENCES Folders(FolderId),
                    UNIQUE(DocumentNumber, DocumentType, strftime('%Y', DocumentDate))
                )",

                // جدول المرفقات
                @"CREATE TABLE IF NOT EXISTS Attachments (
                    AttachmentId INTEGER PRIMARY KEY AUTOINCREMENT,
                    DocumentId INTEGER NOT NULL,
                    FileName NVARCHAR(255) NOT NULL,
                    OriginalFileName NVARCHAR(255) NOT NULL,
                    FilePath NVARCHAR(500) NOT NULL,
                    FileSize INTEGER NOT NULL,
                    FileType NVARCHAR(50) NOT NULL,
                    MimeType NVARCHAR(100),
                    UploadDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UploadedBy NVARCHAR(100),
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    FOREIGN KEY (DocumentId) REFERENCES Documents(DocumentId) ON DELETE CASCADE
                )",

                // جدول إعدادات النظام
                @"CREATE TABLE IF NOT EXISTS SystemSettings (
                    SettingId INTEGER PRIMARY KEY AUTOINCREMENT,
                    SettingKey NVARCHAR(100) NOT NULL UNIQUE,
                    SettingValue NVARCHAR(500),
                    Description NVARCHAR(200),
                    ModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                )",

                // جدول سجل العمليات
                @"CREATE TABLE IF NOT EXISTS ActivityLog (
                    LogId INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId NVARCHAR(100),
                    Action NVARCHAR(100) NOT NULL,
                    TableName NVARCHAR(50),
                    RecordId INTEGER,
                    OldValues NVARCHAR(2000),
                    NewValues NVARCHAR(2000),
                    LogDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    IPAddress NVARCHAR(50)
                )"
            };

            foreach (string query in createTableQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void InsertInitialData(SQLiteConnection connection)
        {
            // إدراج الأقسام الأولية
            string[] departments = {
                "('الإدارة العامة', 'القسم الرئيسي للإدارة العامة والتنسيق')",
                "('الشؤون المالية', 'قسم الشؤون المالية والمحاسبة والميزانية')",
                "('الموارد البشرية', 'قسم الموارد البشرية والتوظيف والتدريب')",
                "('تقنية المعلومات', 'قسم تقنية المعلومات والدعم التقني')",
                "('الشؤون القانونية', 'قسم الشؤون القانونية والاستشارات القانونية')"
            };

            foreach (string dept in departments)
            {
                string query = $"INSERT OR IGNORE INTO Departments (DepartmentName, Description) VALUES {dept}";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }

            // إدراج الأضابير الأولية
            string[] folders = {
                "(1, 'أضبارة عامة', 'أضبارة للوثائق العامة والمراسلات')",
                "(1, 'التعاميم', 'أضبارة التعاميم والقرارات الإدارية')",
                "(2, 'الميزانية', 'أضبارة وثائق الميزانية والمالية')",
                "(2, 'المشتريات', 'أضبارة وثائق المشتريات والعقود')",
                "(3, 'التوظيف', 'أضبارة وثائق التوظيف والتعيينات')",
                "(3, 'التدريب', 'أضبارة وثائق التدريب والتطوير')",
                "(4, 'المشاريع التقنية', 'أضبارة مشاريع تقنية المعلومات')",
                "(5, 'العقود القانونية', 'أضبارة العقود والاتفاقيات القانونية')"
            };

            foreach (string folder in folders)
            {
                string query = $"INSERT OR IGNORE INTO Folders (DepartmentId, FolderName, Description) VALUES {folder}";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }

            // إدراج إعدادات النظام الأولية
            string[] settings = {
                "('DocumentNumberFormat', '{Type}-{Year}-{Number:000}', 'تنسيق ترقيم الوثائق')",
                "('AttachmentsPath', 'Attachments', 'مسار حفظ المرفقات')",
                "('MaxFileSize', '10485760', 'الحد الأقصى لحجم الملف بالبايت (10MB)')",
                "('AllowedFileTypes', '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.txt', 'أنواع الملفات المسموحة')",
                "('BackupPath', 'Backups', 'مسار النسخ الاحتياطية')",
                "('AutoBackup', 'true', 'تفعيل النسخ الاحتياطي التلقائي')"
            };

            foreach (string setting in settings)
            {
                string query = $"INSERT OR IGNORE INTO SystemSettings (SettingKey, SettingValue, Description) VALUES {setting}";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void CreateIndexes(SQLiteConnection connection)
        {
            string[] indexes = {
                "CREATE INDEX IF NOT EXISTS IX_Documents_Number ON Documents(DocumentNumber)",
                "CREATE INDEX IF NOT EXISTS IX_Documents_Type ON Documents(DocumentType)",
                "CREATE INDEX IF NOT EXISTS IX_Documents_Date ON Documents(DocumentDate)",
                "CREATE INDEX IF NOT EXISTS IX_Documents_Department ON Documents(DepartmentId)",
                "CREATE INDEX IF NOT EXISTS IX_Documents_Folder ON Documents(FolderId)",
                "CREATE INDEX IF NOT EXISTS IX_Attachments_Document ON Attachments(DocumentId)",
                "CREATE INDEX IF NOT EXISTS IX_Folders_Department ON Folders(DepartmentId)",
                "CREATE INDEX IF NOT EXISTS IX_ActivityLog_Date ON ActivityLog(LogDate)",
                "CREATE INDEX IF NOT EXISTS IX_Documents_Search ON Documents(Subject, DocumentNumber, SenderReceiver)"
            };

            foreach (string index in indexes)
            {
                using (var command = new SQLiteCommand(index, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void CheckAndUpgradeDatabase()
        {
            // التحقق من إصدار قاعدة البيانات وتحديثها إذا لزم الأمر
            // يمكن إضافة منطق التحديث هنا في المستقبل
        }

        public SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        public string GetConnectionString()
        {
            return _connectionString;
        }

        public string GetDatabasePath()
        {
            return _databasePath;
        }

        public bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }

        public void LogActivity(string action, string tableName = null, int? recordId = null, 
                               string oldValues = null, string newValues = null, string userId = "System")
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    string query = @"INSERT INTO ActivityLog (UserId, Action, TableName, RecordId, OldValues, NewValues) 
                                   VALUES (@UserId, @Action, @TableName, @RecordId, @OldValues, @NewValues)";
                    
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@Action", action);
                        command.Parameters.AddWithValue("@TableName", tableName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RecordId", recordId ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@OldValues", oldValues ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@NewValues", newValues ?? (object)DBNull.Value);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء السجل لتجنب تعطيل التطبيق
            }
        }
    }
}
