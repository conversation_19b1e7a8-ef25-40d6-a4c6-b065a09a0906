using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace ArchiveSystem.Models
{
    public class Document : INotifyPropertyChanged
    {
        private int _documentId;
        private string _documentNumber;
        private string _subject;
        private string _documentType;
        private int _departmentId;
        private int? _folderId;
        private DateTime _documentDate;
        private DateTime? _receivedDate;
        private string _senderReceiver;
        private string _notes;
        private DateTime _createdDate;
        private string _createdBy;
        private DateTime? _modifiedDate;
        private string _modifiedBy;
        private bool _isActive;
        private Department _department;
        private Folder _folder;
        private List<Attachment> _attachments;

        public int DocumentId
        {
            get => _documentId;
            set { _documentId = value; OnPropertyChanged(nameof(DocumentId)); }
        }

        public string DocumentNumber
        {
            get => _documentNumber;
            set { _documentNumber = value; OnPropertyChanged(nameof(DocumentNumber)); }
        }

        public string Subject
        {
            get => _subject;
            set { _subject = value; OnPropertyChanged(nameof(Subject)); }
        }

        public string DocumentType
        {
            get => _documentType;
            set { _documentType = value; OnPropertyChanged(nameof(DocumentType)); }
        }

        public int DepartmentId
        {
            get => _departmentId;
            set { _departmentId = value; OnPropertyChanged(nameof(DepartmentId)); }
        }

        public int? FolderId
        {
            get => _folderId;
            set { _folderId = value; OnPropertyChanged(nameof(FolderId)); }
        }

        public DateTime DocumentDate
        {
            get => _documentDate;
            set { _documentDate = value; OnPropertyChanged(nameof(DocumentDate)); }
        }

        public DateTime? ReceivedDate
        {
            get => _receivedDate;
            set { _receivedDate = value; OnPropertyChanged(nameof(ReceivedDate)); }
        }

        public string SenderReceiver
        {
            get => _senderReceiver;
            set { _senderReceiver = value; OnPropertyChanged(nameof(SenderReceiver)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set { _createdBy = value; OnPropertyChanged(nameof(CreatedBy)); }
        }

        public DateTime? ModifiedDate
        {
            get => _modifiedDate;
            set { _modifiedDate = value; OnPropertyChanged(nameof(ModifiedDate)); }
        }

        public string ModifiedBy
        {
            get => _modifiedBy;
            set { _modifiedBy = value; OnPropertyChanged(nameof(ModifiedBy)); }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
        }

        public Department Department
        {
            get => _department;
            set { _department = value; OnPropertyChanged(nameof(Department)); }
        }

        public Folder Folder
        {
            get => _folder;
            set { _folder = value; OnPropertyChanged(nameof(Folder)); }
        }

        public List<Attachment> Attachments
        {
            get => _attachments ?? (_attachments = new List<Attachment>());
            set { _attachments = value; OnPropertyChanged(nameof(Attachments)); OnPropertyChanged(nameof(AttachmentsCount)); }
        }

        // خصائص محسوبة
        public int AttachmentsCount => Attachments?.Count ?? 0;
        public string StatusText => IsActive ? "نشط" : "محذوف";
        public string DocumentDateFormatted => DocumentDate.ToString("yyyy/MM/dd");
        public string ReceivedDateFormatted => ReceivedDate?.ToString("yyyy/MM/dd") ?? "-";
        public string DepartmentName => Department?.DepartmentName ?? "غير محدد";
        public string FolderName => Folder?.FolderName ?? "غير محدد";
        public string TypeIcon => DocumentType == "صادر" ? "📤" : "📥";

        public Document()
        {
            DocumentDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            IsActive = true;
            CreatedBy = "النظام";
            _attachments = new List<Attachment>();
        }

        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrWhiteSpace(DocumentNumber))
                errors.Add("رقم الوثيقة مطلوب");
            else if (DocumentNumber.Length > 50)
                errors.Add("رقم الوثيقة يجب أن يكون أقل من 50 حرف");

            if (string.IsNullOrWhiteSpace(Subject))
                errors.Add("موضوع الوثيقة مطلوب");
            else if (Subject.Length < 5)
                errors.Add("موضوع الوثيقة يجب أن يكون 5 أحرف على الأقل");
            else if (Subject.Length > 200)
                errors.Add("موضوع الوثيقة يجب أن يكون أقل من 200 حرف");

            if (string.IsNullOrWhiteSpace(DocumentType))
                errors.Add("نوع الوثيقة مطلوب");
            else if (DocumentType != "صادر" && DocumentType != "وارد")
                errors.Add("نوع الوثيقة يجب أن يكون صادر أو وارد");

            if (DepartmentId <= 0)
                errors.Add("يجب اختيار القسم");

            if (DocumentDate > DateTime.Now.Date)
                errors.Add("تاريخ الوثيقة لا يمكن أن يكون في المستقبل");

            if (ReceivedDate.HasValue && ReceivedDate.Value > DateTime.Now.Date)
                errors.Add("تاريخ الاستلام لا يمكن أن يكون في المستقبل");

            if (!string.IsNullOrEmpty(SenderReceiver) && SenderReceiver.Length > 200)
                errors.Add("المرسل/المستقبل يجب أن يكون أقل من 200 حرف");

            if (!string.IsNullOrEmpty(Notes) && Notes.Length > 1000)
                errors.Add("الملاحظات يجب أن تكون أقل من 1000 حرف");

            return errors.Count == 0;
        }

        public override string ToString()
        {
            return $"{DocumentNumber} - {Subject}";
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
