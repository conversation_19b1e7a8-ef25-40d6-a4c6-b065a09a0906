using System;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// نافذة عرض الملفات المرفقة
    /// </summary>
    public partial class DocumentViewerForm : Form
    {
        #region Fields
        private string _filePath;
        private string _fileName;
        private string _fileExtension;
        private Panel _toolbarPanel;
        private Panel _contentPanel;
        private Button _zoomInButton;
        private Button _zoomOutButton;
        private Button _printButton;
        private Button _openExternalButton;
        private Button _closeButton;
        private PictureBox _imageViewer;
        private RichTextBox _textViewer;
        private Label _messageLabel;
        private ComboBox _zoomComboBox;
        private int _currentZoomIndex = 2; // 100% default
        private readonly int[] _zoomLevels = { 25, 50, 100, 150, 200 };
        #endregion

        #region Constructor
        public DocumentViewerForm(string filePath)
        {
            InitializeComponent();
            _filePath = filePath;
            _fileName = Path.GetFileName(filePath);
            _fileExtension = Path.GetExtension(filePath).ToLower();
            
            SetupForm();
            CreateControls();
            LoadFile();
            
            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "View.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Initialization
        private void SetupForm()
        {
            this.Text = "📄 عارض الملفات - " + _fileName;
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);
        }

        private void CreateControls()
        {
            CreateToolbar();
            CreateContentArea();
        }

        private void CreateToolbar()
        {
            _toolbarPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = RTLHelper.SecondaryColor,
                Padding = new Padding(10)
            };

            int buttonWidth = 100;
            int buttonHeight = 40;
            int spacing = 10;
            int startX = 10;

            _zoomOutButton = RTLHelper.CreateStyledButton(
                "🔍- تصغير",
                new Point(startX, 10),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                ZoomOutButton_Click
            );

            _zoomInButton = RTLHelper.CreateStyledButton(
                "🔍+ تكبير",
                new Point(startX + buttonWidth + spacing, 10),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.InfoColor,
                ZoomInButton_Click
            );

            _zoomComboBox = new ComboBox
            {
                Location = new Point(startX + (buttonWidth + spacing) * 2, 15),
                Size = new Size(80, 30),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            _zoomComboBox.Items.AddRange(new object[] { "25%", "50%", "100%", "150%", "200%" });
            _zoomComboBox.SelectedIndex = _currentZoomIndex;
            _zoomComboBox.SelectedIndexChanged += ZoomComboBox_SelectedIndexChanged;

            _printButton = RTLHelper.CreateStyledButton(
                "🖨️ طباعة",
                new Point(startX + (buttonWidth + spacing) * 2 + 90, 10),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                PrintButton_Click
            );

            _openExternalButton = RTLHelper.CreateStyledButton(
                "📂 فتح خارجياً",
                new Point(startX + (buttonWidth + spacing) * 3 + 90, 10),
                new Size(120, buttonHeight),
                RTLHelper.SuccessColor,
                OpenExternalButton_Click
            );

            _closeButton = RTLHelper.CreateStyledButton(
                "❌ إغلاق",
                new Point(startX + (buttonWidth + spacing) * 4 + 210, 10),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DangerColor,
                delegate { this.Close(); }
            );

            RTLHelper.ApplyButtonEffects(_zoomInButton);
            RTLHelper.ApplyButtonEffects(_zoomOutButton);
            RTLHelper.ApplyButtonEffects(_printButton);
            RTLHelper.ApplyButtonEffects(_openExternalButton);
            RTLHelper.ApplyButtonEffects(_closeButton);

            _toolbarPanel.Controls.Add(_zoomOutButton);
            _toolbarPanel.Controls.Add(_zoomInButton);
            _toolbarPanel.Controls.Add(_zoomComboBox);
            _toolbarPanel.Controls.Add(_printButton);
            _toolbarPanel.Controls.Add(_openExternalButton);
            _toolbarPanel.Controls.Add(_closeButton);

            this.Controls.Add(_toolbarPanel);
        }

        private void CreateContentArea()
        {
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(10)
            };

            this.Controls.Add(_contentPanel);
        }
        #endregion

        #region File Loading
        private void LoadFile()
        {
            try
            {
                if (!File.Exists(_filePath))
                {
                    ShowErrorMessage("الملف غير موجود أو تم حذفه");
                    return;
                }

                switch (_fileExtension)
                {
                    case ".jpg":
                    case ".jpeg":
                    case ".png":
                    case ".gif":
                        LoadImageFile();
                        break;
                    case ".txt":
                    case ".rtf":
                        LoadTextFile();
                        break;
                    case ".pdf":
                    case ".doc":
                    case ".docx":
                    case ".xls":
                    case ".xlsx":
                    case ".ppt":
                    case ".pptx":
                        ShowExternalViewerMessage();
                        break;
                    default:
                        ShowUnsupportedMessage();
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الملف: " + ex.Message);
            }
        }

        private void LoadImageFile()
        {
            try
            {
                _imageViewer = new PictureBox
                {
                    Dock = DockStyle.Fill,
                    SizeMode = PictureBoxSizeMode.Zoom,
                    BackColor = Color.White,
                    BorderStyle = BorderStyle.FixedSingle
                };

                _imageViewer.Image = Image.FromFile(_filePath);
                _contentPanel.Controls.Add(_imageViewer);

                // تفعيل أزرار التكبير والتصغير
                _zoomInButton.Enabled = true;
                _zoomOutButton.Enabled = true;
                _zoomComboBox.Enabled = true;
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الصورة: " + ex.Message);
            }
        }

        private void LoadTextFile()
        {
            try
            {
                _textViewer = new RichTextBox
                {
                    Dock = DockStyle.Fill,
                    ReadOnly = true,
                    Font = RTLHelper.ArabicFont,
                    RightToLeft = RightToLeft.Yes,
                    BackColor = Color.White,
                    BorderStyle = BorderStyle.FixedSingle
                };

                // قراءة الملف مع تحديد الترميز
                string content;
                if (_fileExtension == ".rtf")
                {
                    _textViewer.LoadFile(_filePath, RichTextBoxStreamType.RichText);
                }
                else
                {
                    // محاولة قراءة الملف بترميز UTF-8 أولاً
                    try
                    {
                        content = File.ReadAllText(_filePath, Encoding.UTF8);
                    }
                    catch
                    {
                        // في حالة الفشل، استخدم الترميز الافتراضي
                        content = File.ReadAllText(_filePath, Encoding.Default);
                    }
                    _textViewer.Text = content;
                }

                _contentPanel.Controls.Add(_textViewer);

                // تعطيل أزرار التكبير والتصغير للنصوص
                _zoomInButton.Enabled = false;
                _zoomOutButton.Enabled = false;
                _zoomComboBox.Enabled = false;
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الملف النصي: " + ex.Message);
            }
        }

        private void ShowExternalViewerMessage()
        {
            _messageLabel = RTLHelper.CreateStyledLabel(
                "يتطلب هذا النوع من الملفات برنامج خارجي للعرض\n\nانقر على زر 'فتح خارجياً' لعرض الملف في البرنامج المناسب",
                new Point(50, 100),
                new Size(_contentPanel.Width - 100, 200),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleCenter
            );

            _contentPanel.Controls.Add(_messageLabel);

            // تعطيل أزرار التكبير والتصغير
            _zoomInButton.Enabled = false;
            _zoomOutButton.Enabled = false;
            _zoomComboBox.Enabled = false;
        }

        private void ShowUnsupportedMessage()
        {
            _messageLabel = RTLHelper.CreateStyledLabel(
                "نوع الملف غير مدعوم للعرض المباشر\n\nيمكنك فتح الملف خارجياً باستخدام البرنامج المناسب",
                new Point(50, 100),
                new Size(_contentPanel.Width - 100, 200),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleCenter
            );

            _contentPanel.Controls.Add(_messageLabel);

            // تعطيل أزرار التكبير والتصغير
            _zoomInButton.Enabled = false;
            _zoomOutButton.Enabled = false;
            _zoomComboBox.Enabled = false;
        }

        private void ShowErrorMessage(string message)
        {
            _messageLabel = RTLHelper.CreateStyledLabel(
                "❌ " + message,
                new Point(50, 100),
                new Size(_contentPanel.Width - 100, 200),
                RTLHelper.ArabicFontLarge,
                RTLHelper.DangerColor,
                ContentAlignment.MiddleCenter
            );

            _contentPanel.Controls.Add(_messageLabel);

            // تعطيل جميع الأزرار عدا الإغلاق
            _zoomInButton.Enabled = false;
            _zoomOutButton.Enabled = false;
            _zoomComboBox.Enabled = false;
            _printButton.Enabled = false;
            _openExternalButton.Enabled = false;
        }
        #endregion

        #region Event Handlers
        private void ZoomInButton_Click(object sender, EventArgs e)
        {
            if (_currentZoomIndex < _zoomLevels.Length - 1)
            {
                _currentZoomIndex++;
                ApplyZoom();
                _zoomComboBox.SelectedIndex = _currentZoomIndex;
            }
        }

        private void ZoomOutButton_Click(object sender, EventArgs e)
        {
            if (_currentZoomIndex > 0)
            {
                _currentZoomIndex--;
                ApplyZoom();
                _zoomComboBox.SelectedIndex = _currentZoomIndex;
            }
        }

        private void ZoomComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentZoomIndex = _zoomComboBox.SelectedIndex;
            ApplyZoom();
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_imageViewer != null && _imageViewer.Image != null)
                {
                    // طباعة الصورة
                    PrintDocument printDoc = new PrintDocument();
                    printDoc.PrintPage += (s, ev) =>
                    {
                        ev.Graphics.DrawImage(_imageViewer.Image, ev.MarginBounds);
                    };

                    PrintDialog printDialog = new PrintDialog();
                    printDialog.Document = printDoc;
                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        printDoc.Print();
                    }
                }
                else if (_textViewer != null)
                {
                    // طباعة النص
                    RTLHelper.ShowRTLMessageBox("ميزة طباعة النصوص قيد التطوير", "طباعة",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    RTLHelper.ShowRTLMessageBox("لا يوجد محتوى للطباعة", "طباعة",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في الطباعة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenExternalButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists(_filePath))
                {
                    Process.Start(_filePath);
                }
                else
                {
                    RTLHelper.ShowRTLMessageBox("الملف غير موجود", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في فتح الملف: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Helper Methods
        private void ApplyZoom()
        {
            if (_imageViewer != null && _imageViewer.Image != null)
            {
                int zoomPercent = _zoomLevels[_currentZoomIndex];

                if (zoomPercent == 100)
                {
                    _imageViewer.SizeMode = PictureBoxSizeMode.Zoom;
                }
                else
                {
                    _imageViewer.SizeMode = PictureBoxSizeMode.CenterImage;

                    // حساب الحجم الجديد
                    int newWidth = (_imageViewer.Image.Width * zoomPercent) / 100;
                    int newHeight = (_imageViewer.Image.Height * zoomPercent) / 100;

                    // إنشاء صورة مكبرة/مصغرة
                    Bitmap scaledImage = new Bitmap(newWidth, newHeight);
                    using (Graphics g = Graphics.FromImage(scaledImage))
                    {
                        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        g.DrawImage(_imageViewer.Image, 0, 0, newWidth, newHeight);
                    }

                    _imageViewer.Image = scaledImage;
                }
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // تنظيف الموارد
            if (_imageViewer != null && _imageViewer.Image != null)
            {
                _imageViewer.Image.Dispose();
            }
            base.OnFormClosed(e);
        }
        #endregion

        #region Designer Code
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Name = "DocumentViewerForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
