@echo off
chcp 65001 >nul
echo ========================================
echo    نظام الأرشفة الإلكترونية
echo    إعداد سريع - Quick Setup
echo ========================================
echo.

echo [1/5] التحقق من متطلبات النظام...
echo Checking system requirements...

REM التحقق من إصدار Windows
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo إصدار Windows: %VERSION%
echo Windows Version: %VERSION%

REM التحقق من .NET Framework
echo التحقق من .NET Framework...
echo Checking .NET Framework...

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo تحذير: .NET Framework 4.8 غير مثبت
    echo Warning: .NET Framework 4.8 not installed
    echo يرجى تحميله من: https://dotnet.microsoft.com/download/dotnet-framework
    echo Please download from: https://dotnet.microsoft.com/download/dotnet-framework
    pause
)

echo [2/5] إنشاء المجلدات المطلوبة...
echo Creating required directories...

if not exist "bin" mkdir bin
if not exist "bin\Release" mkdir "bin\Release"
if not exist "bin\Debug" mkdir "bin\Debug"
if not exist "obj" mkdir obj
if not exist "obj\Release" mkdir "obj\Release"
if not exist "obj\Debug" mkdir "obj\Debug"

echo [3/5] التحقق من ملفات المشروع...
echo Checking project files...

if not exist "WindowsFormsApp1.sln" (
    echo خطأ: ملف الحل غير موجود
    echo Error: Solution file not found
    pause
    exit /b 1
)

if not exist "WindowsFormsApp1.csproj" (
    echo خطأ: ملف المشروع غير موجود
    echo Error: Project file not found
    pause
    exit /b 1
)

echo [4/5] التحقق من ملفات الكود المصدري...
echo Checking source code files...

set MISSING_FILES=0

if not exist "Form1.cs" (
    echo ملف مفقود: Form1.cs
    echo Missing file: Form1.cs
    set MISSING_FILES=1
)

if not exist "Program.cs" (
    echo ملف مفقود: Program.cs
    echo Missing file: Program.cs
    set MISSING_FILES=1
)

if not exist "Models\Department.cs" (
    echo ملف مفقود: Models\Department.cs
    echo Missing file: Models\Department.cs
    set MISSING_FILES=1
)

if %MISSING_FILES% EQU 1 (
    echo خطأ: ملفات مصدرية مفقودة
    echo Error: Missing source files
    pause
    exit /b 1
)

echo [5/5] الإعداد مكتمل!
echo Setup complete!

echo.
echo ========================================
echo الخطوات التالية:
echo Next steps:
echo ========================================
echo.
echo 1. لبناء المشروع: build.bat
echo    To build project: build.bat
echo.
echo 2. لتشغيل التطبيق: run.bat
echo    To run application: run.bat
echo.
echo 3. لفتح في Visual Studio:
echo    To open in Visual Studio:
echo    start WindowsFormsApp1.sln
echo.
echo ========================================
echo.

pause
