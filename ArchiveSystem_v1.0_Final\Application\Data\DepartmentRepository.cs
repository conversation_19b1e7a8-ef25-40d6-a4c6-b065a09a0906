using System;
using System.Collections.Generic;
using ArchiveSystem.Models;

namespace ArchiveSystem.Data
{
    public class DepartmentRepository
    {
        private readonly XmlDataManager _dataManager;

        public DepartmentRepository()
        {
            _dataManager = XmlDataManager.Instance;
        }

        public List<Department> GetAll(bool includeInactive = false)
        {
            try
            {
                return _dataManager.GetDepartments();
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في استرجاع الأقسام: " + ex.Message);
            }
        }

        public Department GetById(int departmentId)
        {
            try
            {
                var departments = _dataManager.GetDepartments();
                return departments.Find(d => d.DepartmentId == departmentId);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في استرجاع القسم: " + ex.Message);
            }
        }

        public bool Insert(Department department)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!department.IsValid(out List<string> errors))
                {
                    throw new ArgumentException("بيانات القسم غير صحيحة: " + string.Join(", ", errors));
                }

                // التحقق من عدم تكرار الاسم
                if (DepartmentNameExists(department.DepartmentName))
                {
                    throw new ArgumentException($"اسم القسم '{department.DepartmentName}' موجود مسبقاً");
                }

                return _dataManager.SaveDepartment(department);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في إضافة القسم: " + ex.Message);
            }
        }

        public bool Update(Department department)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!department.IsValid(out List<string> errors))
                {
                    throw new ArgumentException("بيانات القسم غير صحيحة: " + string.Join(", ", errors));
                }

                // التحقق من عدم تكرار الاسم (باستثناء القسم الحالي)
                if (DepartmentNameExists(department.DepartmentName, department.DepartmentId))
                {
                    throw new ArgumentException($"اسم القسم '{department.DepartmentName}' موجود مسبقاً");
                }

                return _dataManager.SaveDepartment(department);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في تحديث القسم: " + ex.Message);
            }
        }

        public bool Delete(int departmentId)
        {
            try
            {
                return _dataManager.DeleteDepartment(departmentId);
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في حذف القسم: " + ex.Message);
            }
        }

        public bool DepartmentNameExists(string departmentName, int? excludeId = null)
        {
            return _dataManager.DepartmentNameExists(departmentName, excludeId ?? 0);
        }

        public int GetTotalCount(bool includeInactive = false)
        {
            return _dataManager.GetDepartmentsCount();
        }
    }
}
