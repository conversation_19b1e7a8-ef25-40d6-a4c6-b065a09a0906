﻿using System;
using System.Windows.Forms;

namespace ArchiveSystem
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// نقطة دخول التطبيق الرئيسية
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تهيئة البيانات
                // InitializeDatabase();

                // تشغيل النافذة الرئيسية المحسنة
                Application.Run(new EnhancedMainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تشغيل التطبيق: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
