using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة تعديل الأضبارة
    /// </summary>
    public partial class EditFolderForm : Form
    {
        private readonly FolderRepository _folderRepository;
        private readonly DepartmentRepository _departmentRepository;
        private readonly Folder _folder;
        private TextBox _folderNameText;
        private TextBox _descriptionText;
        private Label _departmentLabel;

        public EditFolderForm(Folder folder)
        {
            InitializeComponent();
            _folderRepository = new FolderRepository();
            _departmentRepository = new DepartmentRepository();
            _folder = folder;
            InitializeControls();
            LoadFolderData();
        }

        private void InitializeControls()
        {
            this.Text = "تعديل الأضبارة - " + _folder.FolderName;
            this.Size = new Size(400, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "تعديل الأضبارة",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // القسم
            var deptLabel = new Label
            {
                Text = "القسم:",
                Location = new Point(300, 70),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            this.Controls.Add(deptLabel);

            _departmentLabel = new Label
            {
                Location = new Point(20, 70),
                Size = new Size(270, 25),
                BorderStyle = BorderStyle.Fixed3D,
                TextAlign = ContentAlignment.MiddleRight,
                BackColor = Color.LightGray,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            this.Controls.Add(_departmentLabel);

            // اسم الأضبارة
            var nameLabel = new Label
            {
                Text = "اسم الأضبارة:",
                Location = new Point(300, 110),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            this.Controls.Add(nameLabel);

            _folderNameText = new TextBox
            {
                Location = new Point(20, 110),
                Size = new Size(270, 25),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(_folderNameText);

            // الوصف
            var descLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(300, 150),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            this.Controls.Add(descLabel);

            _descriptionText = new TextBox
            {
                Location = new Point(20, 150),
                Size = new Size(270, 60),
                Font = new Font("Tahoma", 10F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(_descriptionText);

            // أزرار الحفظ والإلغاء
            var saveButton = new Button
            {
                Text = "حفظ التغييرات",
                Location = new Point(20, 240),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(150, 240),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(cancelButton);

            // التركيز على حقل الاسم
            _folderNameText.Focus();
        }

        private void LoadFolderData()
        {
            try
            {
                // تحميل معلومات القسم
                var department = _departmentRepository.GetById(_folder.DepartmentId);
                if (department != null)
                {
                    _departmentLabel.Text = department.DepartmentName;
                }

                // تحميل بيانات الأضبارة
                _folderNameText.Text = _folder.FolderName;
                _descriptionText.Text = _folder.Description;
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل بيانات الأضبارة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(_folderNameText.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الأضبارة", "خطأ في الإدخال", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _folderNameText.Focus();
                    return;
                }

                string folderName = _folderNameText.Text.Trim();

                // التحقق من عدم تكرار اسم الأضبارة في نفس القسم (باستثناء الأضبارة الحالية)
                if (_folderRepository.ExistsInDepartment(folderName, _folder.DepartmentId, _folder.FolderId))
                {
                    MessageBox.Show("اسم الأضبارة موجود مسبقاً في هذا القسم", "اسم مكرر", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _folderNameText.Focus();
                    return;
                }

                // تحديث بيانات الأضبارة
                _folder.FolderName = folderName;
                _folder.Description = _descriptionText.Text.Trim();

                // حفظ التغييرات
                if (_folderRepository.Update(_folder))
                {
                    MessageBox.Show("تم حفظ التغييرات بنجاح", "نجح الحفظ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التغييرات", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ التغييرات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
