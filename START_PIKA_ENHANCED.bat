@echo off
chcp 65001 >nul
title PIKA Enhanced - نظام الأرشفة الإلكترونية المحسن

echo.
echo ██████╗ ██╗██╗  ██╗ █████╗     ███████╗███╗   ██╗██╗  ██╗ █████╗ ███╗   ██╗ ██████╗███████╗██████╗ 
echo ██╔══██╗██║██║ ██╔╝██╔══██╗    ██╔════╝████╗  ██║██║  ██║██╔══██╗████╗  ██║██╔════╝██╔════╝██╔══██╗
echo ██████╔╝██║█████╔╝ ███████║    █████╗  ██╔██╗ ██║███████║███████║██╔██╗ ██║██║     █████╗  ██║  ██║
echo ██╔═══╝ ██║██╔═██╗ ██╔══██║    ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║╚██╗██║██║     ██╔══╝  ██║  ██║
echo ██║     ██║██║  ██╗██║  ██║    ███████╗██║ ╚████║██║  ██║██║  ██║██║ ╚████║╚██████╗███████╗██████╔╝
echo ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝╚══════╝╚═════╝ 
echo.
echo ========================================================================================================
echo                           🚀 نظام الأرشفة الإلكترونية المحسن 🚀
echo                              PIKA Enhanced Archive System
echo ========================================================================================================
echo.
echo 🎯 المميزات الجديدة المحققة:
echo    New Features Achieved:
echo.
echo ✅ شريط جانبي قابل للطي في الجانب الأيمن
echo    Collapsible right sidebar with smooth animations
echo.
echo ✅ تصميم حديث متوافق مع Windows 7
echo    Modern design fully compatible with Windows 7
echo.
echo ✅ انتقالات سلسة وتأثيرات بصرية متقدمة
echo    Smooth transitions and advanced visual effects
echo.
echo ✅ إحصائيات تفاعلية ومحدثة
echo    Interactive and real-time statistics
echo.
echo ✅ أداء محسن وسرعة أفضل
echo    Enhanced performance and better speed
echo.
echo ========================================================================================================
echo.

REM التحقق من وجود التطبيق
if exist "bin\Release\PIKA_Enhanced.exe" (
    echo 🎊 التطبيق جاهز للتشغيل!
    echo    Application ready to run!
    echo.
    echo 🚀 بدء تشغيل PIKA Enhanced...
    echo    Starting PIKA Enhanced...
    echo.
    
    REM تشغيل التطبيق
    start "" "bin\Release\PIKA_Enhanced.exe"
    
    echo ✅ تم تشغيل التطبيق بنجاح!
    echo    Application started successfully!
    echo.
    echo 💡 نصائح سريعة للاستخدام:
    echo    Quick Usage Tips:
    echo.
    echo 🔄 انقر زر التبديل (◀/▶) لطي الشريط الجانبي
    echo    Click toggle button (◀/▶) to collapse sidebar
    echo.
    echo 📊 راقب الإحصائيات التفاعلية في الصفحة الرئيسية
    echo    Monitor interactive statistics on main page
    echo.
    echo 🎨 استمتع بالتصميم الحديث والانتقالات السلسة
    echo    Enjoy modern design and smooth transitions
    echo.
    
) else (
    echo ❌ التطبيق غير مبني بعد!
    echo    Application not built yet!
    echo.
    echo 🔨 بناء التطبيق أولاً...
    echo    Building application first...
    echo.
    
    call build_enhanced.bat
    
    if exist "bin\Release\PIKA_Enhanced.exe" (
        echo.
        echo ✅ تم البناء بنجاح! تشغيل التطبيق...
        echo    Build successful! Starting application...
        echo.
        start "" "bin\Release\PIKA_Enhanced.exe"
    ) else (
        echo.
        echo ❌ فشل في بناء التطبيق
        echo    Failed to build application
        echo.
        echo يرجى التحقق من:
        echo Please check:
        echo - تثبيت .NET Framework 4.8.1
        echo - .NET Framework 4.8.1 installation
        echo - وجود جميع الملفات المطلوبة
        echo - All required files are present
        echo.
        pause
        exit /b 1
    )
)

echo ========================================================================================================
echo.
echo 🎉 شكراً لاستخدام PIKA Enhanced!
echo    Thank you for using PIKA Enhanced!
echo.
echo 📧 للدعم والمساعدة، راجع README_ENHANCED.md
echo    For support and help, check README_ENHANCED.md
echo.
echo ========================================================================================================
echo.
echo اضغط أي مفتاح للإغلاق...
echo Press any key to close...
pause >nul
