using System;
using System.Collections.Generic;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
using System.Windows.Forms;
using ArchiveSystem.Models;

namespace ArchiveSystem.Services
{
    public class FileManager
    {
        private static FileManager _instance;
        private static readonly object _lock = new object();
        private string _attachmentsPath;
        private string _backupsPath;
        private string _tempPath;

        public static FileManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new FileManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private FileManager()
        {
            InitializePaths();
        }

        private void InitializePaths()
        {
            string appPath = Application.StartupPath;
            
            _attachmentsPath = Path.Combine(appPath, "Attachments");
            _backupsPath = Path.Combine(appPath, "Backups");
            _tempPath = Path.Combine(appPath, "Temp");

            // إنشاء المجلدات إذا لم تكن موجودة
            CreateDirectoryIfNotExists(_attachmentsPath);
            CreateDirectoryIfNotExists(_backupsPath);
            CreateDirectoryIfNotExists(_tempPath);
        }

        private void CreateDirectoryIfNotExists(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المجلد {path}: {ex.Message}");
            }
        }

        public string SaveAttachment(int documentId, string sourceFilePath, string originalFileName)
        {
            try
            {
                // التحقق من وجود الملف المصدر
                if (!File.Exists(sourceFilePath))
                {
                    throw new FileNotFoundException("الملف المصدر غير موجود");
                }

                // التحقق من نوع الملف
                string fileExtension = Path.GetExtension(originalFileName).ToLower();
                if (!Attachment.IsFileTypeAllowed(fileExtension))
                {
                    throw new ArgumentException($"نوع الملف {fileExtension} غير مسموح");
                }

                // التحقق من حجم الملف
                var fileInfo = new FileInfo(sourceFilePath);
                if (fileInfo.Length > 50 * 1024 * 1024) // 50MB
                {
                    throw new ArgumentException("حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)");
                }

                // إنشاء مجلد الوثيقة
                string documentFolder = Path.Combine(_attachmentsPath, $"Document_{documentId}");
                CreateDirectoryIfNotExists(documentFolder);

                // إنشاء اسم ملف فريد
                string fileName = GenerateUniqueFileName(documentFolder, originalFileName);
                string destinationPath = Path.Combine(documentFolder, fileName);

                // نسخ الملف
                File.Copy(sourceFilePath, destinationPath, true);

                return destinationPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ المرفق: {ex.Message}");
            }
        }

        public bool DeleteAttachment(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    // نقل الملف إلى سلة المحذوفات بدلاً من الحذف النهائي
                    string deletedFolder = Path.Combine(_attachmentsPath, "Deleted");
                    CreateDirectoryIfNotExists(deletedFolder);

                    string fileName = Path.GetFileName(filePath);
                    string deletedPath = Path.Combine(deletedFolder, $"{DateTime.Now:yyyyMMdd_HHmmss}_{fileName}");

                    File.Move(filePath, deletedPath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المرفق: {ex.Message}");
            }
        }

        public byte[] ReadFileBytes(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("الملف غير موجود");
                }

                return File.ReadAllBytes(filePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة الملف: {ex.Message}");
            }
        }

        public void SaveFileFromBytes(byte[] fileBytes, string destinationPath)
        {
            try
            {
                string directory = Path.GetDirectoryName(destinationPath);
                CreateDirectoryIfNotExists(directory);

                File.WriteAllBytes(destinationPath, fileBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الملف: {ex.Message}");
            }
        }

        public Image LoadImageFromFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("ملف الصورة غير موجود");
                }

                // التحقق من أن الملف صورة
                string extension = Path.GetExtension(filePath).ToLower();
                if (!IsImageFile(extension))
                {
                    throw new ArgumentException("الملف ليس صورة");
                }

                return Image.FromFile(filePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل الصورة: {ex.Message}");
            }
        }

        public void SaveImageToFile(Image image, string filePath, ImageFormat format = null)
        {
            try
            {
                string directory = Path.GetDirectoryName(filePath);
                CreateDirectoryIfNotExists(directory);

                if (format == null)
                {
                    string extension = Path.GetExtension(filePath).ToLower();
                    format = GetImageFormat(extension);
                }

                image.Save(filePath, format);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الصورة: {ex.Message}");
            }
        }

        public Image CreateThumbnail(string imagePath, int maxWidth = 200, int maxHeight = 200)
        {
            try
            {
                using (var originalImage = LoadImageFromFile(imagePath))
                {
                    // حساب الأبعاد الجديدة مع الحفاظ على النسبة
                    var newSize = CalculateThumbnailSize(originalImage.Size, maxWidth, maxHeight);
                    
                    var thumbnail = new Bitmap(newSize.Width, newSize.Height);
                    using (var graphics = Graphics.FromImage(thumbnail))
                    {
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                        graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;

                        graphics.DrawImage(originalImage, 0, 0, newSize.Width, newSize.Height);
                    }

                    return thumbnail;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الصورة المصغرة: {ex.Message}");
            }
        }

        public string CreateBackup(string sourcePath, string backupName = null)
        {
            try
            {
                if (string.IsNullOrEmpty(backupName))
                {
                    backupName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                }

                string backupPath = Path.Combine(_backupsPath, backupName);
                
                if (File.Exists(sourcePath))
                {
                    // نسخ ملف واحد
                    string fileName = Path.GetFileName(sourcePath);
                    string destinationFile = Path.Combine(backupPath, fileName);
                    CreateDirectoryIfNotExists(backupPath);
                    File.Copy(sourcePath, destinationFile, true);
                }
                else if (Directory.Exists(sourcePath))
                {
                    // نسخ مجلد كامل
                    CopyDirectory(sourcePath, backupPath);
                }
                else
                {
                    throw new ArgumentException("المسار المصدر غير موجود");
                }

                return backupPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        public void CleanupTempFiles()
        {
            try
            {
                if (Directory.Exists(_tempPath))
                {
                    var tempFiles = Directory.GetFiles(_tempPath);
                    foreach (string file in tempFiles)
                    {
                        try
                        {
                            var fileInfo = new FileInfo(file);
                            // حذف الملفات المؤقتة الأقدم من ساعة واحدة
                            if (fileInfo.CreationTime < DateTime.Now.AddHours(-1))
                            {
                                File.Delete(file);
                            }
                        }
                        catch
                        {
                            // تجاهل أخطاء حذف الملفات المؤقتة
                        }
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء تنظيف الملفات المؤقتة
            }
        }

        public long GetDirectorySize(string directoryPath)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return 0;

                long size = 0;
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                
                foreach (string file in files)
                {
                    var fileInfo = new FileInfo(file);
                    size += fileInfo.Length;
                }

                return size;
            }
            catch
            {
                return 0;
            }
        }

        public string GetAttachmentsPath() => _attachmentsPath;
        public string GetBackupsPath() => _backupsPath;
        public string GetTempPath() => _tempPath;

        private string GenerateUniqueFileName(string directory, string originalFileName)
        {
            string fileName = Path.GetFileNameWithoutExtension(originalFileName);
            string extension = Path.GetExtension(originalFileName);
            string fullPath = Path.Combine(directory, originalFileName);

            int counter = 1;
            while (File.Exists(fullPath))
            {
                string newFileName = $"{fileName}_{counter}{extension}";
                fullPath = Path.Combine(directory, newFileName);
                counter++;
            }

            return Path.GetFileName(fullPath);
        }

        private bool IsImageFile(string extension)
        {
            string[] imageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".ico" };
            return Array.Exists(imageExtensions, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase));
        }

        private ImageFormat GetImageFormat(string extension)
        {
            switch (extension.ToLower())
            {
                case ".jpg":
                case ".jpeg":
                    return ImageFormat.Jpeg;
                case ".png":
                    return ImageFormat.Png;
                case ".gif":
                    return ImageFormat.Gif;
                case ".bmp":
                    return ImageFormat.Bmp;
                case ".tiff":
                    return ImageFormat.Tiff;
                case ".ico":
                    return ImageFormat.Icon;
                default:
                    return ImageFormat.Png;
            }
        }

        private Size CalculateThumbnailSize(Size originalSize, int maxWidth, int maxHeight)
        {
            double ratioX = (double)maxWidth / originalSize.Width;
            double ratioY = (double)maxHeight / originalSize.Height;
            double ratio = Math.Min(ratioX, ratioY);

            int newWidth = (int)(originalSize.Width * ratio);
            int newHeight = (int)(originalSize.Height * ratio);

            return new Size(newWidth, newHeight);
        }

        private void CopyDirectory(string sourceDir, string destinationDir)
        {
            CreateDirectoryIfNotExists(destinationDir);

            // نسخ الملفات
            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string fileName = Path.GetFileName(file);
                string destFile = Path.Combine(destinationDir, fileName);
                File.Copy(file, destFile, true);
            }

            // نسخ المجلدات الفرعية
            foreach (string subDir in Directory.GetDirectories(sourceDir))
            {
                string dirName = Path.GetFileName(subDir);
                string destSubDir = Path.Combine(destinationDir, dirName);
                CopyDirectory(subDir, destSubDir);
            }
        }
    }
}
