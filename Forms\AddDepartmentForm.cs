using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة إضافة قسم جديد
    /// </summary>
    public partial class AddDepartmentForm : Form
    {
        private readonly DepartmentRepository _departmentRepository;
        private TextBox _departmentNameText;
        private TextBox _descriptionText;

        public AddDepartmentForm()
        {
            InitializeComponent();
            _departmentRepository = new DepartmentRepository();
            InitializeControls();
        }

        private void InitializeControls()
        {
            this.Text = "إضافة قسم جديد";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "إضافة قسم جديد",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // اسم القسم
            var nameLabel = new Label
            {
                Text = "اسم القسم:",
                Location = new Point(300, 70),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            this.Controls.Add(nameLabel);

            _departmentNameText = new TextBox
            {
                Location = new Point(20, 70),
                Size = new Size(270, 25),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(_departmentNameText);

            // الوصف
            var descLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(300, 110),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            this.Controls.Add(descLabel);

            _descriptionText = new TextBox
            {
                Location = new Point(20, 110),
                Size = new Size(270, 60),
                Font = new Font("Tahoma", 10F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(_descriptionText);

            // أزرار الحفظ والإلغاء
            var saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(20, 200),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(110, 200),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(cancelButton);

            // التركيز على حقل الاسم
            _departmentNameText.Focus();
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(_departmentNameText.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم القسم", "خطأ في الإدخال", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _departmentNameText.Focus();
                    return;
                }

                // إنشاء القسم الجديد
                var department = new Department
                {
                    DepartmentName = _departmentNameText.Text.Trim(),
                    Description = _descriptionText.Text.Trim(),
                    CreatedDate = DateTime.Now,
                    IsActive = true
                };

                // حفظ القسم
                int departmentId = _departmentRepository.Insert(department);

                if (departmentId > 0)
                {
                    MessageBox.Show("تم حفظ القسم بنجاح", "نجح الحفظ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ القسم", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ القسم: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
