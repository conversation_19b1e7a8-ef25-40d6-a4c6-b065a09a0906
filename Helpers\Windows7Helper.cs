using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ArchiveSystem.Helpers
{
    /// <summary>
    /// مساعد للتوافق مع Windows 7 وتحسين الأداء
    /// </summary>
    public static class Windows7Helper
    {
        #region Windows API Declarations
        [DllImport("dwmapi.dll")]
        private static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);

        [DllImport("dwmapi.dll")]
        private static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);

        [DllImport("dwmapi.dll")]
        private static extern int DwmIsCompositionEnabled(ref int pfEnabled);

        [DllImport("user32.dll")]
        private static extern bool SetLayeredWindowAttributes(IntPtr hwnd, uint crKey, byte bAlpha, uint dwFlags);

        [DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll")]
        private static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        private struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }

        private const int GWL_EXSTYLE = -20;
        private const int WS_EX_LAYERED = 0x80000;
        private const int LWA_ALPHA = 0x2;
        private const int LWA_COLORKEY = 0x1;
        #endregion

        #region Properties
        /// <summary>
        /// التحقق من تفعيل Aero في Windows 7
        /// </summary>
        public static bool IsAeroEnabled
        {
            get
            {
                if (Environment.OSVersion.Version.Major >= 6)
                {
                    int enabled = 0;
                    DwmIsCompositionEnabled(ref enabled);
                    return enabled == 1;
                }
                return false;
            }
        }

        /// <summary>
        /// التحقق من نظام Windows 7
        /// </summary>
        public static bool IsWindows7
        {
            get
            {
                var version = Environment.OSVersion.Version;
                return version.Major == 6 && version.Minor == 1;
            }
        }

        /// <summary>
        /// التحقق من نظام Windows 7 أو أحدث
        /// </summary>
        public static bool IsWindows7OrNewer
        {
            get
            {
                var version = Environment.OSVersion.Version;
                return version.Major > 6 || (version.Major == 6 && version.Minor >= 1);
            }
        }
        #endregion

        #region Form Enhancement Methods
        /// <summary>
        /// تطبيق تحسينات Windows 7 على النافذة
        /// </summary>
        public static void ApplyWindows7Enhancements(Form form)
        {
            if (!IsWindows7OrNewer) return;

            try
            {
                // تفعيل الرسم المزدوج لتحسين الأداء
                EnableDoubleBuffering(form);

                // تطبيق تأثيرات Aero إذا كانت متاحة
                if (IsAeroEnabled)
                {
                    ApplyAeroEffects(form);
                }

                // تحسين عرض الخطوط
                EnableClearTypeText(form);

                // تحسين الأداء العام
                OptimizePerformance(form);
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في حالة عدم دعم النظام
            }
        }

        /// <summary>
        /// تفعيل الرسم المزدوج لتحسين الأداء
        /// </summary>
        private static void EnableDoubleBuffering(Control control)
        {
            if (control is Form form)
            {
                form.SetStyle(ControlStyles.AllPaintingInWmPaint |
                             ControlStyles.UserPaint |
                             ControlStyles.DoubleBuffer |
                             ControlStyles.ResizeRedraw, true);
            }

            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                if (child is Panel || child is UserControl)
                {
                    EnableDoubleBuffering(child);
                }
            }
        }

        /// <summary>
        /// تطبيق تأثيرات Aero
        /// </summary>
        private static void ApplyAeroEffects(Form form)
        {
            try
            {
                var margins = new MARGINS()
                {
                    bottomHeight = 1,
                    leftWidth = 1,
                    rightWidth = 1,
                    topHeight = 1
                };

                DwmExtendFrameIntoClientArea(form.Handle, ref margins);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// تحسين عرض الخطوط
        /// </summary>
        private static void EnableClearTypeText(Control control)
        {
            control.Font = new Font(control.Font.FontFamily, control.Font.Size, control.Font.Style, GraphicsUnit.Point);
            
            foreach (Control child in control.Controls)
            {
                EnableClearTypeText(child);
            }
        }

        /// <summary>
        /// تحسين الأداء العام
        /// </summary>
        private static void OptimizePerformance(Form form)
        {
            // تحسين إعدادات الرسم
            form.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
            form.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            form.SetStyle(ControlStyles.UserPaint, true);
            form.SetStyle(ControlStyles.ResizeRedraw, true);
            form.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
        }
        #endregion

        #region Animation Helpers
        /// <summary>
        /// تطبيق تأثير fade للنافذة
        /// </summary>
        public static void ApplyFadeEffect(Form form, bool fadeIn = true, int duration = 300)
        {
            if (!IsWindows7OrNewer) return;

            try
            {
                var timer = new Timer();
                var steps = duration / 20; // 20ms intervals
                var currentStep = 0;
                var startOpacity = fadeIn ? 0.0 : 1.0;
                var endOpacity = fadeIn ? 1.0 : 0.0;

                form.Opacity = startOpacity;

                timer.Interval = 20;
                timer.Tick += (s, e) =>
                {
                    currentStep++;
                    var progress = (double)currentStep / steps;
                    
                    if (progress >= 1.0)
                    {
                        form.Opacity = endOpacity;
                        timer.Stop();
                        timer.Dispose();
                    }
                    else
                    {
                        form.Opacity = startOpacity + (endOpacity - startOpacity) * progress;
                    }
                };

                timer.Start();
            }
            catch
            {
                // في حالة الفشل، تطبيق الشفافية مباشرة
                form.Opacity = fadeIn ? 1.0 : 0.0;
            }
        }

        /// <summary>
        /// تطبيق تأثير slide للعنصر
        /// </summary>
        public static void ApplySlideEffect(Control control, Direction direction, int distance = 50, int duration = 300)
        {
            if (!IsWindows7OrNewer) return;

            try
            {
                var timer = new Timer();
                var steps = duration / 20;
                var currentStep = 0;
                var startLocation = control.Location;
                var endLocation = startLocation;

                // تحديد الاتجاه
                switch (direction)
                {
                    case Direction.Left:
                        endLocation = new Point(startLocation.X - distance, startLocation.Y);
                        break;
                    case Direction.Right:
                        endLocation = new Point(startLocation.X + distance, startLocation.Y);
                        break;
                    case Direction.Up:
                        endLocation = new Point(startLocation.X, startLocation.Y - distance);
                        break;
                    case Direction.Down:
                        endLocation = new Point(startLocation.X, startLocation.Y + distance);
                        break;
                }

                timer.Interval = 20;
                timer.Tick += (s, e) =>
                {
                    currentStep++;
                    var progress = (double)currentStep / steps;
                    
                    if (progress >= 1.0)
                    {
                        control.Location = endLocation;
                        timer.Stop();
                        timer.Dispose();
                    }
                    else
                    {
                        var x = (int)(startLocation.X + (endLocation.X - startLocation.X) * progress);
                        var y = (int)(startLocation.Y + (endLocation.Y - startLocation.Y) * progress);
                        control.Location = new Point(x, y);
                    }
                };

                timer.Start();
            }
            catch
            {
                // في حالة الفشل، تطبيق الموقع مباشرة
                control.Location = direction == Direction.Left || direction == Direction.Right 
                    ? new Point(control.Location.X + (direction == Direction.Left ? -distance : distance), control.Location.Y)
                    : new Point(control.Location.X, control.Location.Y + (direction == Direction.Up ? -distance : distance));
            }
        }
        #endregion

        #region Color Helpers
        /// <summary>
        /// الحصول على ألوان Windows 7 الافتراضية
        /// </summary>
        public static class Windows7Colors
        {
            public static readonly Color AeroBlue = Color.FromArgb(185, 209, 234);
            public static readonly Color AeroGlass = Color.FromArgb(200, 255, 255, 255);
            public static readonly Color BorderColor = Color.FromArgb(112, 112, 112);
            public static readonly Color HeaderGradientStart = Color.FromArgb(242, 242, 242);
            public static readonly Color HeaderGradientEnd = Color.FromArgb(235, 235, 235);
            public static readonly Color ButtonHover = Color.FromArgb(229, 241, 251);
            public static readonly Color ButtonPressed = Color.FromArgb(204, 228, 247);
        }

        /// <summary>
        /// تطبيق نظام ألوان Windows 7
        /// </summary>
        public static void ApplyWindows7ColorScheme(Control control)
        {
            if (control is Form form)
            {
                form.BackColor = SystemColors.Control;
            }
            else if (control is Button button)
            {
                button.BackColor = SystemColors.ButtonFace;
                button.ForeColor = SystemColors.ControlText;
            }
            else if (control is Panel panel)
            {
                panel.BackColor = SystemColors.Control;
            }

            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyWindows7ColorScheme(child);
            }
        }
        #endregion

        #region Enums
        public enum Direction
        {
            Left,
            Right,
            Up,
            Down
        }
        #endregion
    }
}
