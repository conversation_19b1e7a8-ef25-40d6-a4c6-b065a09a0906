<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{082CEB67-5887-4F0C-8E6A-130EB061A4A6}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>ArchiveSystem</RootNamespace>
    <AssemblyName>PIKA_Enhanced</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <ApplicationIcon>AppIcon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="SimpleEnhancedMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimpleDepartmentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimpleDocumentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimpleAddDepartmentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimpleAddDocumentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DocumentViewerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimpleEditDocumentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DepartmentCard.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Data\FolderRepository.cs" />
    <Compile Include="FolderManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddFolderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AdvancedSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\SimpleDataManager.cs" />
    <Compile Include="Data\SimpleDepartmentRepository.cs" />
    <Compile Include="Models\DepartmentSimple.cs" />
    <Compile Include="Models\DocumentSimple.cs" />
    <Compile Include="Models\DocumentAttachment.cs" />
    <Compile Include="Models\Folder.cs" />
    <Compile Include="Helpers\RTLHelper.cs" />
    <Compile Include="Helpers\IconManager.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="AppIcon.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
