using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    public partial class SimpleDepartmentsForm : Form
    {
        private FlowLayoutPanel _departmentsFlowPanel;
        private Button _addButton;
        private Button _editButton;
        private Button _deleteButton;
        private Button _closeButton;
        private Panel _bottomPanel;
        private List<DepartmentSimple> _departments;
        private SimpleDepartmentRepository _departmentRepo;
        private SimpleDataManager _dataManager;

        public SimpleDepartmentsForm()
        {
            InitializeComponent();
            _departmentRepo = new SimpleDepartmentRepository();
            _dataManager = new SimpleDataManager();
            SetupForm();
            CreateControls();
            LoadDepartments();

            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Departments.ico");

            // تطبيق الإصلاح الشامل لـ RTL
            RTLHelper.ComprehensiveRTLFix(this);
        }

        private void SetupForm()
        {
            this.Text = "إدارة الأقسام";
            this.Size = new Size(900, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = RTLHelper.CreateStyledLabel(
                "📁 إدارة الأقسام",
                new Point(20, 20),
                new Size(300, 35),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(titleLabel);

            // وصف النافذة
            var descLabel = RTLHelper.CreateStyledLabel(
                "إضافة وتعديل وحذف أقسام المؤسسة",
                new Point(20, 55),
                new Size(400, 25),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(descLabel);

            // لوحة البطاقات
            CreateDepartmentsFlowPanel();

            // لوحة الأزرار السفلية
            CreateBottomPanel();
        }

        private void CreateDepartmentsFlowPanel()
        {
            _departmentsFlowPanel = new FlowLayoutPanel
            {
                Location = new Point(20, 90),
                Size = new Size(this.Width - 60, this.Height - 200),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = true,
                AutoScroll = true,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(10)
            };

            this.Controls.Add(_departmentsFlowPanel);
        }

        private void CreateBottomPanel()
        {
            _bottomPanel = new Panel
            {
                Location = new Point(0, this.Height - 100),
                Size = new Size(this.Width, 80),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = RTLHelper.LightColor
            };

            int buttonWidth = 140;
            int buttonHeight = 40;
            int spacing = 20;
            int totalButtonsWidth = (buttonWidth * 2) + spacing;
            int startX = (this.Width - totalButtonsWidth) / 2;
            int buttonY = 20;

            _addButton = RTLHelper.CreateStyledButton(
                "➕ إضافة قسم جديد",
                new Point(startX, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                AddButton_Click
            );

            _closeButton = RTLHelper.CreateStyledButton(
                "❌ إغلاق",
                new Point(startX + buttonWidth + spacing, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                delegate { this.Close(); }
            );

            RTLHelper.ApplyButtonEffects(_addButton);
            RTLHelper.ApplyButtonEffects(_closeButton);

            _bottomPanel.Controls.Add(_addButton);
            _bottomPanel.Controls.Add(_closeButton);

            this.Controls.Add(_bottomPanel);
        }

        private void LoadDepartments()
        {
            try
            {
                _departments = _departmentRepo.GetAll();
                RefreshDepartmentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الأقسام: " + ex.Message, "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                _departments = new List<DepartmentSimple>();
            }
        }

        private void RefreshDepartmentsList()
        {
            _departmentsFlowPanel.Controls.Clear();

            foreach (var dept in _departments)
            {
                if (dept.IsActive) // عرض الأقسام النشطة فقط
                {
                    var departmentCard = new DepartmentCard(dept);

                    // تحميل إحصائيات القسم
                    int outgoing, incoming;
                    _dataManager.GetDepartmentStatistics(dept.DepartmentId, out outgoing, out incoming);
                    departmentCard.OutgoingDocuments = outgoing;
                    departmentCard.IncomingDocuments = incoming;
                    departmentCard.UpdateStatistics();

                    // ربط الأحداث
                    departmentCard.EditClicked += DepartmentCard_EditClicked;
                    departmentCard.DeleteClicked += DepartmentCard_DeleteClicked;
                    departmentCard.OpenClicked += DepartmentCard_OpenClicked;

                    // إضافة هامش بين البطاقات
                    departmentCard.Margin = new Padding(10);

                    _departmentsFlowPanel.Controls.Add(departmentCard);
                }
            }
        }

        #region Department Card Event Handlers
        private void DepartmentCard_EditClicked(object sender, DepartmentSimple department)
        {
            EditDepartment(department);
        }

        private void DepartmentCard_DeleteClicked(object sender, DepartmentSimple department)
        {
            DeleteDepartment(department);
        }

        private void DepartmentCard_OpenClicked(object sender, DepartmentSimple department)
        {
            OpenDepartment(department);
        }
        #endregion

        private void AddButton_Click(object sender, EventArgs e)
        {
            var addForm = new SimpleAddDepartmentForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var newDept = new DepartmentSimple
                    {
                        DepartmentName = addForm.DepartmentName,
                        Description = addForm.DepartmentDescription
                    };

                    if (_departmentRepo.Insert(newDept))
                    {
                        LoadDepartments(); // إعادة تحميل القائمة
                        RTLHelper.ShowRTLMessageBox("تم إضافة القسم بنجاح!", "نجح الحفظ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في إضافة القسم", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("خطأ في إضافة القسم: " + ex.Message, "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void EditDepartment(DepartmentSimple department)
        {
            var editForm = new SimpleAddDepartmentForm(department);

            if (editForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    department.DepartmentName = editForm.DepartmentName;
                    department.Description = editForm.DepartmentDescription;

                    if (_departmentRepo.Update(department))
                    {
                        LoadDepartments(); // إعادة تحميل القائمة
                        RTLHelper.ShowRTLMessageBox("تم تحديث القسم بنجاح!", "نجح التحديث",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في تحديث القسم", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في تحديث القسم: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void DeleteDepartment(DepartmentSimple department)
        {
            var result = RTLHelper.ShowRTLMessageBox("هل أنت متأكد من حذف القسم '" + department.DepartmentName + "'؟\n\nملاحظة: سيتم الحذف المنطقي وليس الحذف النهائي.",
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    if (_departmentRepo.Delete(department.DepartmentId))
                    {
                        LoadDepartments(); // إعادة تحميل القائمة
                        RTLHelper.ShowRTLMessageBox("تم حذف القسم بنجاح!", "نجح الحذف",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في حذف القسم", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في حذف القسم: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void OpenDepartment(DepartmentSimple department)
        {
            RTLHelper.ShowRTLMessageBox("سيتم فتح إدارة وثائق القسم: " + department.DepartmentName + "\n\nهذه الميزة ستكون متاحة في المراحل القادمة", "فتح القسم",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Name = "SimpleDepartmentsForm";
            this.ResumeLayout(false);
        }
    }
}
