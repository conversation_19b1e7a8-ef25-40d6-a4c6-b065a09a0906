using System;
using System.Collections.Generic;

namespace ArchiveSystem.Models
{
    /// <summary>
    /// نموذج الأضبارة
    /// </summary>
    public class Folder
    {
        #region Properties
        public int FolderId { get; set; }
        public int DepartmentId { get; set; }
        public string FolderName { get; set; }
        public DateTime FolderDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public bool IsActive { get; set; }

        // خصائص إضافية للعرض
        public string DepartmentName { get; set; }
        #endregion

        #region Computed Properties
        /// <summary>
        /// تاريخ الأضبارة مُنسق للعرض
        /// </summary>
        public string FolderDateFormatted
        {
            get { return FolderDate.ToString("yyyy/MM/dd"); }
        }

        /// <summary>
        /// تاريخ الإنشاء مُنسق للعرض
        /// </summary>
        public string CreatedDateFormatted
        {
            get { return CreatedDate.ToString("yyyy/MM/dd HH:mm"); }
        }

        /// <summary>
        /// نص الحالة
        /// </summary>
        public string StatusText
        {
            get { return IsActive ? "نشط" : "غير نشط"; }
        }

        /// <summary>
        /// أيقونة الأضبارة
        /// </summary>
        public string FolderIcon
        {
            get { return "📁"; }
        }
        #endregion

        #region Constructor
        public Folder()
        {
            FolderDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            IsActive = true;
            CreatedBy = "النظام";
        }
        #endregion

        #region Validation
        /// <summary>
        /// التحقق من صحة بيانات الأضبارة
        /// </summary>
        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrEmpty(FolderName) || FolderName.Trim().Length == 0)
                errors.Add("اسم الأضبارة مطلوب");
            else if (FolderName.Length > 100)
                errors.Add("اسم الأضبارة يجب أن يكون أقل من 100 حرف");

            if (DepartmentId <= 0)
                errors.Add("يجب اختيار القسم");

            if (FolderDate > DateTime.Now.Date)
                errors.Add("تاريخ الأضبارة لا يمكن أن يكون في المستقبل");

            return errors.Count == 0;
        }
        #endregion

        #region Methods
        public override string ToString()
        {
            return FolderName + " (" + FolderDateFormatted + ")";
        }

        /// <summary>
        /// إنشاء نسخة من الأضبارة
        /// </summary>
        public Folder Clone()
        {
            return new Folder
            {
                FolderId = this.FolderId,
                DepartmentId = this.DepartmentId,
                FolderName = this.FolderName,
                FolderDate = this.FolderDate,
                CreatedDate = this.CreatedDate,
                CreatedBy = this.CreatedBy,
                IsActive = this.IsActive,
                DepartmentName = this.DepartmentName
            };
        }
        #endregion
    }
}
