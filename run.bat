@echo off
echo ========================================
echo    نظام الأرشفة الإلكترونية
echo    Electronic Archive System
echo ========================================
echo.

echo التحقق من وجود التطبيق...
echo Checking for application...

if not exist "bin\Release\نظام الأرشفة الإلكترونية.exe" (
    echo التطبيق غير مبني. جاري البناء...
    echo Application not built. Building...
    call build.bat
    if %ERRORLEVEL% NEQ 0 (
        echo فشل في بناء التطبيق
        echo Failed to build application
        pause
        exit /b 1
    )
)

echo تشغيل التطبيق...
echo Running application...
echo.

cd bin\Release
start "نظام الأرشفة الإلكترونية" "نظام الأرشفة الإلكترونية.exe"

echo تم تشغيل التطبيق بنجاح!
echo Application started successfully!
echo.

timeout /t 3 /nobreak >nul
