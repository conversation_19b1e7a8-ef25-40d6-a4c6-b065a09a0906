using System;
using System.Drawing;
using System.Windows.Forms;

namespace ArchiveSystem.Helpers
{
    /// <summary>
    /// مدير الأيقونات المبسط
    /// </summary>
    public static class IconManager
    {
        /// <summary>
        /// تعيين أيقونة للنموذج
        /// </summary>
        public static void SetFormIcon(Form form, string iconName)
        {
            try
            {
                // محاولة تحميل الأيقونة من الملف
                if (System.IO.File.Exists(iconName))
                {
                    form.Icon = new Icon(iconName);
                }
                else if (System.IO.File.Exists("Icons\\" + iconName))
                {
                    form.Icon = new Icon("Icons\\" + iconName);
                }
                // إذا لم توجد الأيقونة، نتجاهل الخطأ
            }
            catch
            {
                // تجاهل أخطاء الأيقونة
            }
        }

        /// <summary>
        /// إنشاء جميع الأيقونات المطلوبة
        /// </summary>
        public static void CreateAllIcons()
        {
            try
            {
                // إنشاء مجلد الأيقونات إذا لم يكن موجوداً
                if (!System.IO.Directory.Exists("Icons"))
                {
                    System.IO.Directory.CreateDirectory("Icons");
                }

                // إنشاء أيقونة بسيطة افتراضية
                CreateSimpleIcon("Icons\\MainApp.ico");
                CreateSimpleIcon("Icons\\Departments.ico");
                CreateSimpleIcon("Icons\\Documents.ico");
                CreateSimpleIcon("Icons\\Add.ico");
                CreateSimpleIcon("Icons\\Edit.ico");
                CreateSimpleIcon("Icons\\Delete.ico");
                CreateSimpleIcon("Icons\\View.ico");
                CreateSimpleIcon("Icons\\Search.ico");
                CreateSimpleIcon("Icons\\Settings.ico");
            }
            catch
            {
                // تجاهل أخطاء إنشاء الأيقونات
            }
        }

        /// <summary>
        /// إنشاء أيقونة بسيطة
        /// </summary>
        private static void CreateSimpleIcon(string filePath)
        {
            try
            {
                if (System.IO.File.Exists(filePath)) return;

                // إنشاء bitmap بسيط
                using (var bitmap = new Bitmap(32, 32))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // رسم مربع ملون بسيط
                    graphics.Clear(Color.Blue);
                    graphics.FillRectangle(Brushes.White, 8, 8, 16, 16);
                    
                    // حفظ كأيقونة
                    var icon = Icon.FromHandle(bitmap.GetHicon());
                    using (var stream = new System.IO.FileStream(filePath, System.IO.FileMode.Create))
                    {
                        icon.Save(stream);
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء إنشاء الأيقونة
            }
        }
    }
}
