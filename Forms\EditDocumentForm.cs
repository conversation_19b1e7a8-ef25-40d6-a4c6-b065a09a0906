using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة تعديل الوثيقة
    /// </summary>
    public partial class EditDocumentForm : Form
    {
        private readonly DocumentRepository _documentRepository;
        private readonly DepartmentRepository _departmentRepository;
        private readonly FolderRepository _folderRepository;
        private readonly Document _document;

        private ComboBox _documentTypeCombo;
        private TextBox _documentNumberText;
        private DateTimePicker _documentDatePicker;
        private TextBox _subjectText;
        private TextBox _issuedFromText;
        private TextBox _receivedToText;
        private ComboBox _departmentCombo;
        private ComboBox _folderCombo;

        public EditDocumentForm(Document document)
        {
            InitializeComponent();
            _documentRepository = new DocumentRepository();
            _departmentRepository = new DepartmentRepository();
            _folderRepository = new FolderRepository();
            _document = document;
            InitializeControls();
            LoadInitialData();
            LoadDocumentData();
        }

        private void InitializeControls()
        {
            this.Text = "تعديل الوثيقة - " + _document.DocumentNumber;
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "تعديل الوثيقة",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            int yPos = 70;
            int spacing = 40;

            // نوع الكتاب
            this.Controls.Add(CreateLabel("نوع الكتاب:", new Point(480, yPos)));
            _documentTypeCombo = new ComboBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _documentTypeCombo.Items.AddRange(new[] { "صادر", "وارد" });
            this.Controls.Add(_documentTypeCombo);
            yPos += spacing;

            // رقم الكتاب
            this.Controls.Add(CreateLabel("رقم الكتاب:", new Point(480, yPos)));
            _documentNumberText = new TextBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25)
            };
            this.Controls.Add(_documentNumberText);
            yPos += spacing;

            // تاريخ الكتاب
            this.Controls.Add(CreateLabel("تاريخ الكتاب:", new Point(480, yPos)));
            _documentDatePicker = new DateTimePicker
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25),
                Format = DateTimePickerFormat.Short
            };
            this.Controls.Add(_documentDatePicker);
            yPos += spacing;

            // موضوع الكتاب
            this.Controls.Add(CreateLabel("موضوع الكتاب:", new Point(480, yPos)));
            _subjectText = new TextBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(_subjectText);
            yPos += 80;

            // صادر من
            this.Controls.Add(CreateLabel("صادر من:", new Point(480, yPos)));
            _issuedFromText = new TextBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25)
            };
            this.Controls.Add(_issuedFromText);
            yPos += spacing;

            // وارد إلى
            this.Controls.Add(CreateLabel("وارد إلى:", new Point(480, yPos)));
            _receivedToText = new TextBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25)
            };
            this.Controls.Add(_receivedToText);
            yPos += spacing;

            // القسم
            this.Controls.Add(CreateLabel("القسم:", new Point(480, yPos)));
            _departmentCombo = new ComboBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _departmentCombo.SelectedIndexChanged += DepartmentCombo_SelectedIndexChanged;
            this.Controls.Add(_departmentCombo);
            yPos += spacing;

            // الأضبارة
            this.Controls.Add(CreateLabel("الأضبارة:", new Point(480, yPos)));
            _folderCombo = new ComboBox
            {
                Location = new Point(200, yPos),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            this.Controls.Add(_folderCombo);
            yPos += spacing;

            // أزرار الحفظ والإلغاء
            var saveButton = new Button
            {
                Text = "حفظ التغييرات",
                Location = new Point(200, yPos + 20),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(330, yPos + 20),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(cancelButton);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
        }

        private void LoadInitialData()
        {
            // تحميل الأقسام
            var departments = _departmentRepository.GetAll();
            _departmentCombo.DataSource = departments;
            _departmentCombo.DisplayMember = "DepartmentName";
            _departmentCombo.ValueMember = "DepartmentId";
        }

        private void LoadDocumentData()
        {
            // تحميل بيانات الوثيقة
            _documentTypeCombo.SelectedItem = _document.DocumentType;
            _documentNumberText.Text = _document.DocumentNumber;
            _documentDatePicker.Value = _document.DocumentDate;
            _subjectText.Text = _document.Subject;
            _issuedFromText.Text = _document.IssuedFrom;
            _receivedToText.Text = _document.ReceivedTo;

            // تحديد القسم
            _departmentCombo.SelectedValue = _document.DepartmentId;
            LoadFolders();

            // تحديد الأضبارة
            _folderCombo.SelectedValue = _document.FolderId;
        }

        private void DepartmentCombo_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadFolders();
        }

        private void LoadFolders()
        {
            if (_departmentCombo.SelectedValue != null)
            {
                int departmentId = (int)_departmentCombo.SelectedValue;
                var folders = _folderRepository.GetByDepartmentId(departmentId);
                _folderCombo.DataSource = folders;
                _folderCombo.DisplayMember = "FolderName";
                _folderCombo.ValueMember = "FolderId";
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // تحديث بيانات الوثيقة
                _document.DocumentType = _documentTypeCombo.SelectedItem.ToString();
                _document.DocumentNumber = _documentNumberText.Text.Trim();
                _document.DocumentDate = _documentDatePicker.Value.Date;
                _document.Subject = _subjectText.Text.Trim();
                _document.IssuedFrom = _issuedFromText.Text.Trim();
                _document.ReceivedTo = _receivedToText.Text.Trim();
                _document.DepartmentId = (int)_departmentCombo.SelectedValue;
                _document.FolderId = (int)_folderCombo.SelectedValue;

                // حفظ التغييرات
                if (_documentRepository.Update(_document))
                {
                    MessageBox.Show("تم حفظ التغييرات بنجاح", "نجح الحفظ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التغييرات", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ التغييرات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من الحقول الإلزامية
            if (string.IsNullOrWhiteSpace(_documentNumberText.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الكتاب", "خطأ في الإدخال", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberText.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_subjectText.Text))
            {
                MessageBox.Show("يرجى إدخال موضوع الكتاب", "خطأ في الإدخال", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _subjectText.Focus();
                return false;
            }

            if (_departmentCombo.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار القسم", "خطأ في الإدخال", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _departmentCombo.Focus();
                return false;
            }

            if (_folderCombo.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الأضبارة", "خطأ في الإدخال", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderCombo.Focus();
                return false;
            }

            // التحقق من عدم تكرار رقم الكتاب (باستثناء الوثيقة الحالية)
            string documentNumber = _documentNumberText.Text.Trim();
            string documentType = _documentTypeCombo.SelectedItem.ToString();
            int year = _documentDatePicker.Value.Year;

            if (_documentRepository.DocumentNumberExists(documentNumber, documentType, year, _document.DocumentId))
            {
                MessageBox.Show("رقم الكتاب " + documentNumber + " موجود مسبقاً لنفس النوع والسنة",
                              "رقم مكرر", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberText.Focus();
                return false;
            }

            return true;
        }
    }
}
