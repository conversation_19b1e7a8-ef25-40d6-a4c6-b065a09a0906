using System;
using System.Drawing;
using System.Windows.Forms;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    public partial class SimpleAddDepartmentForm : Form
    {
        private TextBox _nameTextBox;
        private TextBox _descriptionTextBox;
        private Button _saveButton;
        private Button _cancelButton;
        private bool _isEditMode;

        public string DepartmentName { get; private set; }
        public string DepartmentDescription { get; private set; }

        public SimpleAddDepartmentForm(DepartmentSimple department = null)
        {
            _isEditMode = department != null;
            InitializeComponent();
            SetupForm();
            CreateControls();

            if (_isEditMode)
            {
                LoadDepartmentData(department);
            }

            // تطبيق إعدادات RTL والأيقونة
            RTLHelper.ApplyRTLSettings(this);
            IconManager.SetFormIcon(this, _isEditMode ? "Edit.ico" : "Add.ico");

            // إصلاح مشاكل المحاذاة
            RTLHelper.FixCommonAlignmentIssues(this);
        }

        private void SetupForm()
        {
            this.Text = _isEditMode ? "✏️ تعديل القسم" : "➕ إضافة قسم جديد";
            this.Size = new Size(550, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }

        private void CreateControls()
        {
            // عنوان النافذة
            var titleLabel = RTLHelper.CreateStyledLabel(
                _isEditMode ? "✏️ تعديل بيانات القسم" : "➕ إضافة قسم جديد",
                new Point(20, 20),
                new Size(400, 35),
                RTLHelper.ArabicFontLargeBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(titleLabel);

            // وصف النافذة
            var descLabel = RTLHelper.CreateStyledLabel(
                _isEditMode ? "تعديل معلومات القسم المحدد" : "إدخال معلومات القسم الجديد",
                new Point(20, 55),
                new Size(400, 25),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(descLabel);

            // تسمية اسم القسم
            var nameLabel = RTLHelper.CreateStyledLabel(
                "اسم القسم: *",
                new Point(400, 110),
                new Size(120, 25),
                RTLHelper.ArabicFontBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(nameLabel);

            // حقل اسم القسم
            _nameTextBox = RTLHelper.CreateStyledTextBox(
                new Point(50, 110),
                new Size(330, 30),
                false
            );
            this.Controls.Add(_nameTextBox);

            // تسمية الوصف
            var descriptionLabel = RTLHelper.CreateStyledLabel(
                "وصف القسم: *",
                new Point(400, 160),
                new Size(120, 25),
                RTLHelper.ArabicFontBold,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(descriptionLabel);

            // حقل الوصف
            _descriptionTextBox = RTLHelper.CreateStyledTextBox(
                new Point(50, 160),
                new Size(330, 100),
                true
            );
            this.Controls.Add(_descriptionTextBox);

            // ملاحظة الحقول المطلوبة
            var requiredLabel = RTLHelper.CreateStyledLabel(
                "* الحقول المطلوبة",
                new Point(50, 270),
                new Size(150, 20),
                RTLHelper.ArabicFont,
                RTLHelper.DangerColor,
                ContentAlignment.MiddleRight
            );
            this.Controls.Add(requiredLabel);

            // أزرار التحكم
            CreateButtons();
        }

        private void CreateButtons()
        {
            // زر الحفظ
            _saveButton = RTLHelper.CreateStyledButton(
                _isEditMode ? "💾 تحديث" : "💾 حفظ",
                new Point(280, 310),
                new Size(120, 45),
                RTLHelper.SuccessColor,
                SaveButton_Click
            );

            // زر الإلغاء
            _cancelButton = RTLHelper.CreateStyledButton(
                "❌ إلغاء",
                new Point(150, 310),
                new Size(120, 45),
                RTLHelper.DangerColor,
                delegate { this.DialogResult = DialogResult.Cancel; }
            );

            // تطبيق تأثيرات بصرية
            RTLHelper.ApplyButtonEffects(_saveButton);
            RTLHelper.ApplyButtonEffects(_cancelButton);

            this.Controls.Add(_saveButton);
            this.Controls.Add(_cancelButton);
        }

        private void LoadDepartmentData(DepartmentSimple department)
        {
            _nameTextBox.Text = department.DepartmentName;
            _descriptionTextBox.Text = department.Description;
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                DepartmentName = _nameTextBox.Text.Trim();
                DepartmentDescription = _descriptionTextBox.Text.Trim();
                
                this.DialogResult = DialogResult.OK;
            }
        }

        private bool ValidateInput()
        {
            // التحقق من اسم القسم
            if (string.IsNullOrEmpty(_nameTextBox.Text) || _nameTextBox.Text.Trim().Length == 0)
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال اسم القسم", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _nameTextBox.Focus();
                return false;
            }

            if (_nameTextBox.Text.Trim().Length < 3)
            {
                RTLHelper.ShowRTLMessageBox("يجب أن يكون اسم القسم 3 أحرف على الأقل", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _nameTextBox.Focus();
                return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrEmpty(_descriptionTextBox.Text) || _descriptionTextBox.Text.Trim().Length == 0)
            {
                RTLHelper.ShowRTLMessageBox("يرجى إدخال وصف القسم", "خطأ في البيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _descriptionTextBox.Focus();
                return false;
            }

            return true;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 350);
            this.Name = "SimpleAddDepartmentForm";
            this.ResumeLayout(false);
        }
    }
}
