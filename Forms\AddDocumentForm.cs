using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Services;

namespace ArchiveSystem.Forms
{
    /// <summary>
    /// نافذة إضافة وثيقة جديدة
    /// </summary>
    public partial class AddDocumentForm : Form
    {
        private readonly DepartmentRepository _departmentRepository;
        private readonly FolderRepository _folderRepository;
        private readonly DocumentRepository _documentRepository;
        private readonly AttachmentRepository _attachmentRepository;
        private readonly FileService _fileService;

        private ComboBox _documentTypeCombo;
        private TextBox _documentSequenceText;
        private Label _entryDateLabel;
        private TextBox _documentNumberText;
        private DateTimePicker _documentDatePicker;
        private TextBox _subjectText;
        private TextBox _issuedFromText;
        private TextBox _receivedToText;
        private ComboBox _departmentCombo;
        private ComboBox _folderCombo;
        private TextBox _saveSequenceText;
        private ListBox _attachmentsList;
        private Panel _previewPanel;
        private PictureBox _previewPictureBox;

        private List<string> _selectedFiles;
        private int _currentPreviewIndex;

        public event EventHandler DocumentSaved;

        public AddDocumentForm()
        {
            InitializeComponent();
            InitializeServices();
            InitializeControls();
            LoadInitialData();
        }

        private void InitializeServices()
        {
            _departmentRepository = new DepartmentRepository();
            _folderRepository = new FolderRepository();
            _documentRepository = new DocumentRepository();
            _attachmentRepository = new AttachmentRepository();
            _fileService = new FileService();
            _selectedFiles = new List<string>();
            _currentPreviewIndex = 0;
        }

        private void InitializeControls()
        {
            this.Text = "إضافة وثيقة جديدة";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);
            this.BackColor = Color.FromArgb(240, 242, 247);

            CreateFormControls();
        }

        private void CreateFormControls()
        {
            // عنوان النافذة
            var titleLabel = new Label
            {
                Text = "إضافة وثيقة جديدة",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(50, 30),
                Size = new Size(300, 40),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(titleLabel);

            // الجانب الأيمن - الحقول
            CreateRightPanel();

            // الجانب الأيسر - المعاينة
            CreateLeftPanel();
        }

        private void CreateRightPanel()
        {
            var rightPanel = new Panel
            {
                Location = new Point(50, 80),
                Size = new Size(500, 600),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            int yPos = 20;
            int labelWidth = 120;
            int controlWidth = 300;
            int spacing = 40;

            // نوع الكتاب
            rightPanel.Controls.Add(CreateLabel("نوع الكتاب:", new Point(350, yPos)));
            _documentTypeCombo = new ComboBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _documentTypeCombo.Items.AddRange(new[] { "صادر", "وارد" });
            _documentTypeCombo.SelectedIndex = 0;
            rightPanel.Controls.Add(_documentTypeCombo);
            yPos += spacing;

            // تسلسل الكتاب
            rightPanel.Controls.Add(CreateLabel("تسلسل الكتاب:", new Point(350, yPos)));
            _documentSequenceText = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                ReadOnly = true,
                BackColor = Color.LightGray,
                Text = _documentRepository.GetNextSequence().ToString()
            };
            rightPanel.Controls.Add(_documentSequenceText);
            yPos += spacing;

            // تاريخ الإدخال
            rightPanel.Controls.Add(CreateLabel("تاريخ الإدخال:", new Point(350, yPos)));
            _entryDateLabel = new Label
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"),
                BorderStyle = BorderStyle.Fixed3D,
                TextAlign = ContentAlignment.MiddleRight,
                BackColor = Color.LightGray
            };
            rightPanel.Controls.Add(_entryDateLabel);
            yPos += spacing;

            // رقم الكتاب
            rightPanel.Controls.Add(CreateLabel("رقم الكتاب:", new Point(350, yPos)));
            _documentNumberText = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25)
            };
            rightPanel.Controls.Add(_documentNumberText);
            yPos += spacing;

            // تاريخ الكتاب
            rightPanel.Controls.Add(CreateLabel("تاريخ الكتاب:", new Point(350, yPos)));
            _documentDatePicker = new DateTimePicker
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                Format = DateTimePickerFormat.Short
            };
            rightPanel.Controls.Add(_documentDatePicker);
            yPos += spacing;

            // موضوع الكتاب
            rightPanel.Controls.Add(CreateLabel("موضوع الكتاب:", new Point(350, yPos)));
            _subjectText = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            rightPanel.Controls.Add(_subjectText);
            yPos += 80;

            // صادر من
            rightPanel.Controls.Add(CreateLabel("صادر من:", new Point(350, yPos)));
            _issuedFromText = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25)
            };
            rightPanel.Controls.Add(_issuedFromText);
            yPos += spacing;

            // وارد إلى
            rightPanel.Controls.Add(CreateLabel("وارد إلى:", new Point(350, yPos)));
            _receivedToText = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25)
            };
            rightPanel.Controls.Add(_receivedToText);
            yPos += spacing;

            // القسم
            rightPanel.Controls.Add(CreateLabel("القسم:", new Point(350, yPos)));
            _departmentCombo = new ComboBox
            {
                Location = new Point(30, yPos),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _departmentCombo.SelectedIndexChanged += DepartmentCombo_SelectedIndexChanged;
            rightPanel.Controls.Add(_departmentCombo);

            var addDeptButton = new Button
            {
                Text = "إضافة قسم",
                Location = new Point(290, yPos),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addDeptButton.Click += AddDeptButton_Click;
            rightPanel.Controls.Add(addDeptButton);
            yPos += spacing;

            // الأضبارة
            rightPanel.Controls.Add(CreateLabel("الأضبارة:", new Point(350, yPos)));
            _folderCombo = new ComboBox
            {
                Location = new Point(30, yPos),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            rightPanel.Controls.Add(_folderCombo);

            var addFolderButton = new Button
            {
                Text = "إضافة أضبارة",
                Location = new Point(290, yPos),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addFolderButton.Click += AddFolderButton_Click;
            rightPanel.Controls.Add(addFolderButton);
            yPos += spacing;

            // تسلسل الحفظ
            rightPanel.Controls.Add(CreateLabel("تسلسل الحفظ:", new Point(350, yPos)));
            _saveSequenceText = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                ReadOnly = true,
                BackColor = Color.LightPink,
                ForeColor = Color.Red,
                Text = _documentRepository.GetNextSaveSequence().ToString()
            };
            rightPanel.Controls.Add(_saveSequenceText);
            yPos += spacing;

            // أزرار الحفظ والإلغاء
            var saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(30, yPos + 20),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;
            rightPanel.Controls.Add(saveButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(150, yPos + 20),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, e) => this.Close();
            rightPanel.Controls.Add(cancelButton);

            this.Controls.Add(rightPanel);
        }

        private void CreateLeftPanel()
        {
            var leftPanel = new Panel
            {
                Location = new Point(600, 80),
                Size = new Size(500, 600),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // عنوان المرفقات
            var attachmentsLabel = new Label
            {
                Text = "المرفقات:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                Location = new Point(400, 10),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            leftPanel.Controls.Add(attachmentsLabel);

            // أزرار إدارة الملفات
            var selectFileButton = new Button
            {
                Text = "اختيار ملف",
                Location = new Point(300, 40),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            selectFileButton.Click += SelectFileButton_Click;
            leftPanel.Controls.Add(selectFileButton);

            var scanButton = new Button
            {
                Text = "ماسح ضوئي",
                Location = new Point(390, 40),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            scanButton.Click += ScanButton_Click;
            leftPanel.Controls.Add(scanButton);

            // قائمة المرفقات
            _attachmentsList = new ListBox
            {
                Location = new Point(250, 80),
                Size = new Size(230, 150),
                Font = new Font("Tahoma", 9F)
            };
            _attachmentsList.SelectedIndexChanged += AttachmentsList_SelectedIndexChanged;
            leftPanel.Controls.Add(_attachmentsList);

            var removeFileButton = new Button
            {
                Text = "حذف الملف",
                Location = new Point(250, 240),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            removeFileButton.Click += RemoveFileButton_Click;
            leftPanel.Controls.Add(removeFileButton);

            // منطقة المعاينة
            var previewLabel = new Label
            {
                Text = "معاينة:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                Location = new Point(400, 280),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            leftPanel.Controls.Add(previewLabel);

            _previewPanel = new Panel
            {
                Location = new Point(20, 310),
                Size = new Size(460, 250),
                BorderStyle = BorderStyle.Fixed3D,
                BackColor = Color.LightGray
            };

            _previewPictureBox = new PictureBox
            {
                Dock = DockStyle.Fill,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.White
            };
            _previewPanel.Controls.Add(_previewPictureBox);
            leftPanel.Controls.Add(_previewPanel);

            // أزرار التنقل
            var prevButton = new Button
            {
                Text = "السابق",
                Location = new Point(20, 570),
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            prevButton.Click += PrevButton_Click;
            leftPanel.Controls.Add(prevButton);

            var nextButton = new Button
            {
                Text = "التالي",
                Location = new Point(90, 570),
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(127, 140, 141),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            nextButton.Click += NextButton_Click;
            leftPanel.Controls.Add(nextButton);

            var enlargeButton = new Button
            {
                Text = "تكبير",
                Location = new Point(420, 570),
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            enlargeButton.Click += EnlargeButton_Click;
            leftPanel.Controls.Add(enlargeButton);

            this.Controls.Add(leftPanel);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
        }

        private void LoadInitialData()
        {
            // تحميل الأقسام
            var departments = _departmentRepository.GetAll();
            _departmentCombo.DataSource = departments;
            _departmentCombo.DisplayMember = "DepartmentName";
            _departmentCombo.ValueMember = "DepartmentId";

            if (departments.Count > 0)
            {
                _departmentCombo.SelectedIndex = 0;
                LoadFolders();
            }
        }

        private void DepartmentCombo_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadFolders();
        }

        private void LoadFolders()
        {
            if (_departmentCombo.SelectedValue != null)
            {
                int departmentId = (int)_departmentCombo.SelectedValue;
                var folders = _folderRepository.GetByDepartmentId(departmentId);
                _folderCombo.DataSource = folders;
                _folderCombo.DisplayMember = "FolderName";
                _folderCombo.ValueMember = "FolderId";
            }
        }

        private void AddDeptButton_Click(object sender, EventArgs e)
        {
            var addDeptForm = new AddDepartmentForm();
            if (addDeptForm.ShowDialog() == DialogResult.OK)
            {
                LoadInitialData();
            }
        }

        private void AddFolderButton_Click(object sender, EventArgs e)
        {
            if (_departmentCombo.SelectedValue != null)
            {
                int departmentId = (int)_departmentCombo.SelectedValue;
                var addFolderForm = new AddFolderForm(departmentId);
                if (addFolderForm.ShowDialog() == DialogResult.OK)
                {
                    LoadFolders();
                }
            }
        }

        private void SelectFileButton_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = _fileService.GetSupportedImageFormats();
                openFileDialog.Multiselect = true;
                openFileDialog.Title = "اختيار الملفات";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    foreach (string fileName in openFileDialog.FileNames)
                    {
                        _selectedFiles.Add(fileName);
                        _attachmentsList.Items.Add(Path.GetFileName(fileName));
                    }

                    if (_selectedFiles.Count > 0)
                    {
                        _currentPreviewIndex = _selectedFiles.Count - 1;
                        ShowPreview();
                    }
                }
            }
        }

        private void ScanButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("وظيفة المسح الضوئي قيد التطوير", "المسح الضوئي",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void RemoveFileButton_Click(object sender, EventArgs e)
        {
            if (_attachmentsList.SelectedIndex >= 0)
            {
                int index = _attachmentsList.SelectedIndex;
                _selectedFiles.RemoveAt(index);
                _attachmentsList.Items.RemoveAt(index);

                if (_selectedFiles.Count > 0)
                {
                    _currentPreviewIndex = Math.Min(_currentPreviewIndex, _selectedFiles.Count - 1);
                    ShowPreview();
                }
                else
                {
                    _previewPictureBox.Image = null;
                }
            }
        }

        private void AttachmentsList_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_attachmentsList.SelectedIndex >= 0)
            {
                _currentPreviewIndex = _attachmentsList.SelectedIndex;
                ShowPreview();
            }
        }

        private void PrevButton_Click(object sender, EventArgs e)
        {
            if (_selectedFiles.Count > 0 && _currentPreviewIndex > 0)
            {
                _currentPreviewIndex--;
                ShowPreview();
                _attachmentsList.SelectedIndex = _currentPreviewIndex;
            }
        }

        private void NextButton_Click(object sender, EventArgs e)
        {
            if (_selectedFiles.Count > 0 && _currentPreviewIndex < _selectedFiles.Count - 1)
            {
                _currentPreviewIndex++;
                ShowPreview();
                _attachmentsList.SelectedIndex = _currentPreviewIndex;
            }
        }

        private void EnlargeButton_Click(object sender, EventArgs e)
        {
            if (_selectedFiles.Count > 0 && _currentPreviewIndex < _selectedFiles.Count)
            {
                string filePath = _selectedFiles[_currentPreviewIndex];
                _fileService.OpenFile(filePath);
            }
        }

        private void ShowPreview()
        {
            if (_selectedFiles.Count > 0 && _currentPreviewIndex < _selectedFiles.Count)
            {
                string filePath = _selectedFiles[_currentPreviewIndex];
                string extension = Path.GetExtension(filePath).ToLower();

                if (extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                    extension == ".bmp" || extension == ".gif" || extension == ".tiff")
                {
                    try
                    {
                        _previewPictureBox.Image = Image.FromFile(filePath);
                    }
                    catch
                    {
                        _previewPictureBox.Image = null;
                    }
                }
                else
                {
                    _previewPictureBox.Image = null;
                }
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // إنشاء الوثيقة
                var document = new Document
                {
                    DocumentSequence = int.Parse(_documentSequenceText.Text),
                    EntryDate = DateTime.Now,
                    DocumentNumber = _documentNumberText.Text.Trim(),
                    DocumentDate = _documentDatePicker.Value.Date,
                    Subject = _subjectText.Text.Trim(),
                    DocumentType = _documentTypeCombo.SelectedItem.ToString(),
                    IssuedFrom = _issuedFromText.Text.Trim(),
                    ReceivedTo = _receivedToText.Text.Trim(),
                    DepartmentId = (int)_departmentCombo.SelectedValue,
                    FolderId = (int)_folderCombo.SelectedValue,
                    SaveSequence = int.Parse(_saveSequenceText.Text)
                };

                // حفظ الوثيقة
                int documentId = _documentRepository.Insert(document);

                // حفظ المرفقات
                SaveAttachments(documentId);

                MessageBox.Show("تم حفظ الوثيقة بنجاح", "نجح الحفظ",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إثارة حدث الحفظ
                DocumentSaved?.Invoke(this, EventArgs.Empty);

                // إعادة تهيئة النموذج
                ResetForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ الوثيقة: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من الحقول الإلزامية
            if (string.IsNullOrWhiteSpace(_documentNumberText.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الكتاب", "خطأ في الإدخال",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberText.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_subjectText.Text))
            {
                MessageBox.Show("يرجى إدخال موضوع الكتاب", "خطأ في الإدخال",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _subjectText.Focus();
                return false;
            }

            if (_departmentCombo.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار القسم", "خطأ في الإدخال",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _departmentCombo.Focus();
                return false;
            }

            if (_folderCombo.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الأضبارة", "خطأ في الإدخال",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _folderCombo.Focus();
                return false;
            }

            // التحقق من عدم تكرار رقم الكتاب
            string documentNumber = _documentNumberText.Text.Trim();
            string documentType = _documentTypeCombo.SelectedItem.ToString();
            int year = _documentDatePicker.Value.Year;

            if (_documentRepository.DocumentNumberExists(documentNumber, documentType, year))
            {
                MessageBox.Show("رقم الكتاب " + documentNumber + " موجود مسبقاً لنفس النوع والسنة",
                              "رقم مكرر", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _documentNumberText.Focus();
                return false;
            }

            return true;
        }

        private void SaveAttachments(int documentId)
        {
            foreach (string filePath in _selectedFiles)
            {
                try
                {
                    // نسخ الملف إلى مجلد المرفقات
                    string savedPath = _fileService.SaveAttachment(filePath, documentId, Path.GetFileName(filePath));

                    // إنشاء سجل المرفق
                    var attachment = new Attachment
                    {
                        DocumentId = documentId,
                        FileName = Path.GetFileName(filePath),
                        FilePath = savedPath,
                        FileExtension = Path.GetExtension(filePath),
                        FileSize = new FileInfo(filePath).Length,
                        FileType = _fileService.GetFileType(Path.GetExtension(filePath))
                    };

                    // حفظ المرفق في قاعدة البيانات
                    _attachmentRepository.Insert(attachment);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("خطأ في حفظ المرفق " + Path.GetFileName(filePath) + ": " + ex.Message,
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        private void ResetForm()
        {
            // إعادة تهيئة الحقول
            _documentSequenceText.Text = _documentRepository.GetNextSequence().ToString();
            _entryDateLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            _documentNumberText.Clear();
            _documentDatePicker.Value = DateTime.Now;
            _subjectText.Clear();
            _issuedFromText.Clear();
            _receivedToText.Clear();
            _saveSequenceText.Text = _documentRepository.GetNextSaveSequence().ToString();

            // إعادة تهيئة المرفقات
            _selectedFiles.Clear();
            _attachmentsList.Items.Clear();
            _previewPictureBox.Image = null;
            _currentPreviewIndex = 0;

            // التركيز على أول حقل
            _documentNumberText.Focus();
        }
    }
}
