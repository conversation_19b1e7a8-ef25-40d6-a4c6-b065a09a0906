using System;

namespace ArchiveSystem.Models
{
    /// <summary>
    /// نموذج مرفق الوثيقة
    /// </summary>
    public class DocumentAttachment
    {
        public int AttachmentId { get; set; }
        public int DocumentId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string FileType { get; set; }
        public DateTime UploadDate { get; set; }
        public bool IsActive { get; set; }

        /// <summary>
        /// حجم الملف مُنسق للعرض
        /// </summary>
        public string FileSizeFormatted
        {
            get
            {
                if (FileSize < 1024)
                    return FileSize + " بايت";
                else if (FileSize < 1024 * 1024)
                    return Math.Round((double)FileSize / 1024, 2) + " كيلوبايت";
                else
                    return Math.Round((double)FileSize / (1024 * 1024), 2) + " ميجابايت";
            }
        }

        /// <summary>
        /// أيقونة نوع الملف
        /// </summary>
        public string FileTypeIcon
        {
            get
            {
                if (string.IsNullOrEmpty(FileType))
                    return "📄";

                string ext = FileType.ToLower();
                switch (ext)
                {
                    case ".pdf":
                        return "📕";
                    case ".doc":
                    case ".docx":
                        return "📘";
                    case ".xls":
                    case ".xlsx":
                        return "📗";
                    case ".ppt":
                    case ".pptx":
                        return "📙";
                    case ".jpg":
                    case ".jpeg":
                    case ".png":
                    case ".gif":
                        return "🖼️";
                    case ".txt":
                    case ".rtf":
                        return "📝";
                    default:
                        return "📄";
                }
            }
        }

        /// <summary>
        /// تاريخ الرفع مُنسق للعرض
        /// </summary>
        public string UploadDateFormatted
        {
            get { return UploadDate.ToString("yyyy/MM/dd HH:mm"); }
        }

        public DocumentAttachment()
        {
            UploadDate = DateTime.Now;
            IsActive = true;
        }

        /// <summary>
        /// التحقق من صحة بيانات المرفق
        /// </summary>
        public bool IsValid(out string error)
        {
            error = string.Empty;

            if (string.IsNullOrEmpty(FileName) || FileName.Trim().Length == 0)
            {
                error = "اسم الملف مطلوب";
                return false;
            }

            if (FileName.Length > 255)
            {
                error = "اسم الملف يجب أن يكون أقل من 255 حرف";
                return false;
            }

            if (FileSize <= 0)
            {
                error = "حجم الملف غير صحيح";
                return false;
            }

            if (FileSize > 10 * 1024 * 1024) // 10 MB
            {
                error = "حجم الملف يجب أن يكون أقل من 10 ميجابايت";
                return false;
            }

            if (string.IsNullOrEmpty(FileType))
            {
                error = "نوع الملف مطلوب";
                return false;
            }

            // التحقق من أنواع الملفات المدعومة
            string[] supportedTypes = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", 
                                      ".ppt", ".pptx", ".jpg", ".jpeg", ".png", 
                                      ".gif", ".txt", ".rtf" };
            
            bool isSupported = false;
            string lowerFileType = FileType.ToLower();
            foreach (string type in supportedTypes)
            {
                if (lowerFileType == type)
                {
                    isSupported = true;
                    break;
                }
            }

            if (!isSupported)
            {
                error = "نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG, PNG, GIF, TXT, RTF";
                return false;
            }

            return true;
        }

        public override string ToString()
        {
            return FileName + " (" + FileSizeFormatted + ")";
        }
    }
}
