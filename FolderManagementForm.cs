using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ArchiveSystem.Data;
using ArchiveSystem.Models;
using ArchiveSystem.Helpers;

namespace ArchiveSystem
{
    /// <summary>
    /// نافذة إدارة الأضبارات
    /// </summary>
    public partial class FolderManagementForm : Form
    {
        #region Fields
        private FolderRepository _folderRepo;
        private SimpleDepartmentRepository _departmentRepo;
        private List<Folder> _folders;
        private List<DepartmentSimple> _departments;
        
        // Controls
        private ComboBox _departmentFilterComboBox;
        private DataGridView _foldersDataGridView;
        private Button _addButton;
        private Button _editButton;
        private Button _deleteButton;
        private Button _closeButton;
        private Panel _topPanel;
        private Panel _bottomPanel;
        private Label _statisticsLabel;
        #endregion

        #region Constructor
        public FolderManagementForm()
        {
            InitializeComponent();
            _folderRepo = new FolderRepository();
            _departmentRepo = new SimpleDepartmentRepository();
            
            SetupForm();
            CreateControls();
            LoadData();
            
            // تطبيق إعدادات RTL والأيقونة
            IconManager.SetFormIcon(this, "Folders.ico");
            RTLHelper.ComprehensiveRTLFix(this);
        }
        #endregion

        #region Form Setup
        private void SetupForm()
        {
            this.Text = "📁 إدارة الأضبارات";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = RTLHelper.ArabicFont;
            this.BackColor = RTLHelper.LightColor;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);
        }

        private void CreateControls()
        {
            CreateTopPanel();
            CreateDataGrid();
            CreateBottomPanel();
        }

        private void CreateTopPanel()
        {
            _topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(20)
            };

            // عنوان النافذة
            var titleLabel = RTLHelper.CreateStyledLabel(
                "📁 إدارة الأضبارات",
                new Point(20, 10),
                new Size(300, 35),
                RTLHelper.ArabicFontTitle,
                RTLHelper.PrimaryColor,
                ContentAlignment.MiddleRight
            );

            // فلتر الأقسام
            var departmentLabel = RTLHelper.CreateStyledLabel(
                "فلترة حسب القسم:",
                new Point(20, 50),
                new Size(120, 25),
                RTLHelper.ArabicFont,
                RTLHelper.DarkColor,
                ContentAlignment.MiddleRight
            );

            _departmentFilterComboBox = new ComboBox
            {
                Location = new Point(150, 50),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Font = RTLHelper.ArabicFont
            };
            _departmentFilterComboBox.SelectedIndexChanged += DepartmentFilter_SelectedIndexChanged;

            // إحصائيات
            _statisticsLabel = RTLHelper.CreateStyledLabel(
                "",
                new Point(400, 50),
                new Size(300, 25),
                RTLHelper.ArabicFont,
                RTLHelper.InfoColor,
                ContentAlignment.MiddleRight
            );

            _topPanel.Controls.Add(titleLabel);
            _topPanel.Controls.Add(departmentLabel);
            _topPanel.Controls.Add(_departmentFilterComboBox);
            _topPanel.Controls.Add(_statisticsLabel);

            this.Controls.Add(_topPanel);
        }

        private void CreateDataGrid()
        {
            _foldersDataGridView = RTLHelper.CreateStyledDataGridView(
                new Point(20, 120),
                new Size(this.Width - 60, this.Height - 250)
            );

            // إعداد الأعمدة
            _foldersDataGridView.Columns.Add("FolderName", "اسم الأضبارة");
            _foldersDataGridView.Columns.Add("DepartmentName", "القسم");
            _foldersDataGridView.Columns.Add("FolderDate", "تاريخ الأضبارة");
            _foldersDataGridView.Columns.Add("CreatedDate", "تاريخ الإنشاء");
            _foldersDataGridView.Columns.Add("Status", "الحالة");

            // تحديد عرض الأعمدة
            _foldersDataGridView.Columns["FolderName"].Width = 250;
            _foldersDataGridView.Columns["DepartmentName"].Width = 200;
            _foldersDataGridView.Columns["FolderDate"].Width = 120;
            _foldersDataGridView.Columns["CreatedDate"].Width = 150;
            _foldersDataGridView.Columns["Status"].Width = 100;

            _foldersDataGridView.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;
            _foldersDataGridView.SelectionChanged += FoldersDataGridView_SelectionChanged;

            this.Controls.Add(_foldersDataGridView);
        }

        private void CreateBottomPanel()
        {
            _bottomPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = RTLHelper.LightColor,
                Padding = new Padding(20)
            };

            int buttonWidth = 140;
            int buttonHeight = 40;
            int spacing = 20;
            int totalButtonsWidth = (buttonWidth * 4) + (spacing * 3);
            int startX = (this.Width - totalButtonsWidth) / 2;
            int buttonY = 20;

            _addButton = RTLHelper.CreateStyledButton(
                "➕ إضافة أضبارة",
                new Point(startX, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.SuccessColor,
                AddButton_Click
            );

            _editButton = RTLHelper.CreateStyledButton(
                "✏️ تعديل",
                new Point(startX + buttonWidth + spacing, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.AccentColor,
                EditButton_Click
            );
            _editButton.Enabled = false;

            _deleteButton = RTLHelper.CreateStyledButton(
                "🗑️ حذف",
                new Point(startX + (buttonWidth + spacing) * 2, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DangerColor,
                DeleteButton_Click
            );
            _deleteButton.Enabled = false;

            _closeButton = RTLHelper.CreateStyledButton(
                "❌ إغلاق",
                new Point(startX + (buttonWidth + spacing) * 3, buttonY),
                new Size(buttonWidth, buttonHeight),
                RTLHelper.DarkColor,
                delegate { this.Close(); }
            );

            RTLHelper.ApplyButtonEffects(_addButton);
            RTLHelper.ApplyButtonEffects(_editButton);
            RTLHelper.ApplyButtonEffects(_deleteButton);
            RTLHelper.ApplyButtonEffects(_closeButton);

            _bottomPanel.Controls.Add(_addButton);
            _bottomPanel.Controls.Add(_editButton);
            _bottomPanel.Controls.Add(_deleteButton);
            _bottomPanel.Controls.Add(_closeButton);

            this.Controls.Add(_bottomPanel);
        }
        #endregion

        #region Data Loading
        private void LoadData()
        {
            LoadDepartments();
            LoadFolders();
            UpdateStatistics();
        }

        private void LoadDepartments()
        {
            try
            {
                _departments = _departmentRepo.GetAll().Where(d => d.IsActive).ToList();
                
                _departmentFilterComboBox.Items.Clear();
                _departmentFilterComboBox.Items.Add(new ComboBoxItem { Text = "جميع الأقسام", Value = 0 });
                
                foreach (var dept in _departments)
                {
                    _departmentFilterComboBox.Items.Add(new ComboBoxItem 
                    { 
                        Text = dept.DepartmentName, 
                        Value = dept.DepartmentId 
                    });
                }
                
                _departmentFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الأقسام: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadFolders()
        {
            try
            {
                _folders = _folderRepo.GetAll();
                RefreshFoldersGrid();
            }
            catch (Exception ex)
            {
                RTLHelper.ShowRTLMessageBox("خطأ في تحميل الأضبارات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                _folders = new List<Folder>();
            }
        }

        private void RefreshFoldersGrid()
        {
            _foldersDataGridView.Rows.Clear();

            var filteredFolders = GetFilteredFolders();

            foreach (var folder in filteredFolders)
            {
                if (folder.IsActive)
                {
                    int rowIndex = _foldersDataGridView.Rows.Add(
                        folder.FolderName,
                        folder.DepartmentName,
                        folder.FolderDateFormatted,
                        folder.CreatedDateFormatted,
                        folder.StatusText
                    );

                    _foldersDataGridView.Rows[rowIndex].Tag = folder;
                }
            }

            UpdateStatistics();
        }

        private List<Folder> GetFilteredFolders()
        {
            if (_departmentFilterComboBox.SelectedItem == null)
                return _folders;

            var selectedItem = (ComboBoxItem)_departmentFilterComboBox.SelectedItem;
            if (selectedItem.Value == 0) // جميع الأقسام
                return _folders;

            return _folders.Where(f => f.DepartmentId == selectedItem.Value).ToList();
        }

        private void UpdateStatistics()
        {
            var filteredFolders = GetFilteredFolders().Where(f => f.IsActive).ToList();
            _statisticsLabel.Text = "إجمالي الأضبارات: " + filteredFolders.Count;
        }
        #endregion

        #region Event Handlers
        private void DepartmentFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            RefreshFoldersGrid();
        }

        private void FoldersDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = _foldersDataGridView.SelectedRows.Count > 0;
            _editButton.Enabled = hasSelection;
            _deleteButton.Enabled = hasSelection;
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            var addForm = new AddFolderForm(_departments);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var newFolder = new Folder
                    {
                        FolderName = addForm.FolderName,
                        DepartmentId = addForm.SelectedDepartmentId,
                        FolderDate = addForm.FolderDate,
                        CreatedBy = "المستخدم الحالي"
                    };

                    if (_folderRepo.Insert(newFolder))
                    {
                        LoadFolders();
                        RTLHelper.ShowRTLMessageBox("تم إضافة الأضبارة بنجاح!", "نجح الحفظ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        RTLHelper.ShowRTLMessageBox("فشل في إضافة الأضبارة", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    RTLHelper.ShowRTLMessageBox("خطأ في إضافة الأضبارة: " + ex.Message, "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_foldersDataGridView.SelectedRows.Count > 0)
            {
                var selectedFolder = (Folder)_foldersDataGridView.SelectedRows[0].Tag;
                var editForm = new AddFolderForm(_departments, selectedFolder);

                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        selectedFolder.FolderName = editForm.FolderName;
                        selectedFolder.DepartmentId = editForm.SelectedDepartmentId;
                        selectedFolder.FolderDate = editForm.FolderDate;

                        if (_folderRepo.Update(selectedFolder))
                        {
                            LoadFolders();
                            RTLHelper.ShowRTLMessageBox("تم تحديث الأضبارة بنجاح!", "نجح التحديث",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            RTLHelper.ShowRTLMessageBox("فشل في تحديث الأضبارة", "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        RTLHelper.ShowRTLMessageBox("خطأ في تحديث الأضبارة: " + ex.Message, "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_foldersDataGridView.SelectedRows.Count > 0)
            {
                var selectedFolder = (Folder)_foldersDataGridView.SelectedRows[0].Tag;

                var result = RTLHelper.ShowRTLMessageBox("هل أنت متأكد من حذف الأضبارة '" + selectedFolder.FolderName + "'؟\n\nملاحظة: سيتم الحذف المنطقي وليس الحذف النهائي.",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        if (_folderRepo.Delete(selectedFolder.FolderId))
                        {
                            LoadFolders();
                            RTLHelper.ShowRTLMessageBox("تم حذف الأضبارة بنجاح!", "نجح الحذف",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            RTLHelper.ShowRTLMessageBox("فشل في حذف الأضبارة", "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        RTLHelper.ShowRTLMessageBox("خطأ في حذف الأضبارة: " + ex.Message, "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        #endregion

        #region Designer Code
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Name = "FolderManagementForm";
            this.ResumeLayout(false);
        }
        #endregion
    }
}
